import { SvgSpinners90Ring } from "@/components/common/icons";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog-shadcn";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/shadcn-button";
import { Textarea } from "@/components/ui/textarea";

import { toast } from "@/hooks/use-toast";
import { useAddBillingAddressMutation } from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus } from "lucide-react";
import { useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { CountrySelect, StateSelect, CitySelect } from "react-country-state-city";
import "react-country-state-city/dist/react-country-state-city.css";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { z } from "zod";
import { Country, State } from "react-country-state-city/dist/esm/types";
import { useLocationData } from "@/hooks/use-location-data";
import { ShippingAddress } from "@/supabase/types";

// Create billing address schema with all fields
const billingAddressSchema = z.object({
  address: z.string().min(1, "Address is required"),
  address2: z.string().optional(),
  address_type: z.enum(["residential", "commercial"], {
    required_error: "Address type is required",
  }),
  company_name: z.string().optional(),
  city: z.string().min(1, "City is required"),
  country: z.string().min(1, "Country is required"),
  state: z.string().min(1, "State is required"),
  zip_code: z.string().min(1, "Zip code is required"),
  note: z.string().min(1, "Please provide a reason for adding this billing address"),
});

// Define the type from our schema
type BillingAddressFormValues = z.infer<typeof billingAddressSchema>;

export function AddBillingAddressDialog() {
  const userData = useAuthStore((state) => state.data);
  const userId = userData.id;
  const addBillingAddressMutation = useAddBillingAddressMutation(userId);
  const [openBillingAddressDialog, setOpenBillingAddressDialog] = useState(false);

  const [hasStates, setHasStates] = useState(true);
  const [hasCities, setHasCities] = useState(true);

  // Get location data from our custom hook with United States as default
  const {
    countryId, stateId,
    selectedCountry, selectedState, selectedCity,
    updateCountry, updateState, updateCity,
    isLoading
  } = useLocationData({});

  const form = useForm<BillingAddressFormValues>({
    resolver: zodResolver(billingAddressSchema),
    defaultValues: {
      address: "",
      address2: "",
      address_type: "commercial" as const,
      company_name: "",
      city: "",
      country: "United States",
      state: "",
      zip_code: "",
      note: "",
    },
  });

  const addNewBillingAddress = (data: BillingAddressFormValues) => {
    addBillingAddressMutation
      .mutateAsync(data)
      .then(() => {
        form.reset();
        form.clearErrors();

        toast({
          title: "Success",
          description:
            "Billing address added successfully. Please wait for approval.",
          duration: 3000,
          variant: "success",
        });

        setOpenBillingAddressDialog(false);
      })
      .catch((e) => {
        console.log(e);
      });
  };

  const cancel = () => {
    form.reset();
    form.clearErrors();
    setOpenBillingAddressDialog(false);
  };

  const openBillingAddressDialogHandler = () => {
    setOpenBillingAddressDialog(true);
  };

  return (
    <Dialog
      open={openBillingAddressDialog}
      onOpenChange={setOpenBillingAddressDialog}
    >
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="relative w-full h-full flex flex-col items-center justify-center gap-4 font-medium text-base min-h-[240px]"
          onClick={openBillingAddressDialogHandler}
        >
          <Plus size={48} strokeWidth={3} />
          <div>Add Billing Address</div>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md md:max-w-2xl max-h-[90vh] overflow-y-auto">
        <Form {...form}>
          <DialogHeader>
            <DialogTitle>Add new Billing Address</DialogTitle>
            <DialogDescription>
              Add a new billing address for your account.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <form
              onSubmit={form.handleSubmit(addNewBillingAddress)}
              className="space-y-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="company_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Company Name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="address_type"
                  render={({ field }) => (
                    <FormItem aria-required>
                      <FormLabel>Address Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select address type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="commercial">Commercial</SelectItem>
                          <SelectItem value="residential">Residential</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4">
                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem aria-required>
                      <FormLabel>Address Line 1</FormLabel>
                      <FormControl>
                        <Input placeholder="123 Main St" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="address2"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address Line 2</FormLabel>
                      <FormControl>
                        <Input placeholder="Apartment, suite, unit, etc." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4">
                <FormField
                  control={form.control}
                  name="country"
                  render={({ field }) => (
                    <FormItem aria-required>
                      <FormLabel>Country</FormLabel>
                      <FormControl>
                        {isLoading ? (
                          <div className="flex items-center h-10 px-3 border rounded-md">
                            <SvgSpinners90Ring className="w-4 h-4 mr-2" />
                            <span className="text-sm text-muted-foreground">Loading countries...</span>
                          </div>
                        ) : (
                          <CountrySelect
                            onChange={(e: Country) => {
                              field.onChange(e.name);
                              setHasStates(e.hasStates);
                              if (!e.hasStates) {
                                setHasCities(false);
                              } else {
                                setHasCities(true);
                              }
                              // Update country and reset dependent fields
                              updateCountry(e);
                              // Reset form values
                              form.setValue("state", "");
                              form.setValue("city", "");
                            }}
                            placeHolder="Select Country"
                            defaultValue={selectedCountry}
                          />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem aria-required>
                      <FormLabel>State</FormLabel>
                      <FormControl>
                        {isLoading ? (
                          <div className="flex items-center h-10 px-3 border rounded-md">
                            <SvgSpinners90Ring className="w-4 h-4 mr-2" />
                            <span className="text-sm text-muted-foreground">Loading states...</span>
                          </div>
                        ) : (
                          <>
                            {
                              hasStates ? (
                                <StateSelect
                                  countryid={countryId}
                                  onChange={(e: State) => {
                                    field.onChange(e.name);
                                    setHasCities(e.hasCities);
                                    // Update state and reset city
                                    updateState(e);
                                    // Reset city form value
                                    form.setValue("city", "");
                                  }}
                                  placeHolder="Select State"
                                  defaultValue={selectedState}
                                />
                              ) : (
                                <Input type="text" placeholder="Enter State" {...field} />
                              )
                            }
                          </>
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem aria-required>
                      <FormLabel>City</FormLabel>
                      <FormControl>
                        {isLoading ? (
                          <div className="flex items-center h-10 px-3 border rounded-md">
                            <SvgSpinners90Ring className="w-4 h-4 mr-2" />
                            <span className="text-sm text-muted-foreground">Loading cities...</span>
                          </div>
                        ) : (
                          <>
                            {
                              hasCities ? (
                                <CitySelect
                                  countryid={countryId}
                                  stateid={stateId}
                                  onChange={(e) => {
                                    field.onChange(e.name);
                                    // Update city
                                    updateCity(e);
                                  }}
                                  placeHolder="Select City"
                                  defaultValue={selectedCity}
                                />
                              ) : (
                                <Input type="text" placeholder="Enter City" {...field} />
                              )
                            }
                          </>
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="zip_code"
                render={({ field }) => (
                  <FormItem aria-required>
                    <FormLabel>Zip Code</FormLabel>
                    <FormControl>
                      <Input placeholder="123456" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="note"
                render={({ field }) => (
                  <FormItem aria-required>
                    <FormLabel>
                      Reason for adding this address{" "}
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Please explain why you're adding this billing address"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {addBillingAddressMutation.isError ? (
                <div className="w-full">
                  <p className="text-red-500">
                    {addBillingAddressMutation.error.message}
                  </p>
                </div>
              ) : null}

              <DialogFooter className="pt-4">
                <DialogClose asChild>
                  <Button onClick={cancel} variant="destructive" type="button">
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  type="submit"
                  disabled={addBillingAddressMutation.isPending}
                >
                  Add Billing Address
                  {addBillingAddressMutation.isPending && (
                    <SvgSpinners90Ring className="w-4 h-4 ml-2" />
                  )}
                </Button>
              </DialogFooter>
            </form>
          </div>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
