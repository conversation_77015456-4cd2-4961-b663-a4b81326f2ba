import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAuth } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { Category, Customer, Discount, Group } from "@/supabase/types";
import { NextApiResponse } from "next";

export default checkAuth(
  matchRoute({
    GET: handler,
  })
);

export interface GroupWithDiscount extends Pick<Group, "id" | "name"> {}

export interface CustomerWithRelations extends Customer {
  group?: Partial<GroupWithDiscount>;
  categories?: Category[];
  subCategories?: Category[];
}

export interface GetCustomerResponse {
  customer?: CustomerWithRelations | null;
  error?: string;
}

async function handler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<GetCustomerResponse>
) {
  const { id } = req.query;

  if (!id || typeof id !== "string") {
    return res.status(400).json({ error: "Missing or invalid id" });
  }

  if (req.user?.id !== id) {
    return res.status(403).json({ error: "Unauthorized" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const { data: customerData, error: customerError } = await supabaseAdminClient
    .from("customers")
    .select(
      "*, customer_groups(groups(id, name)), customer_categories(categories(*))"
    )
    .eq("user_id", id)
    .single();

  if (customerError) {
    return res.status(400).json({ error: customerError.message });
  }

  const { data: subCategories, error: subCategoriesError } =
    await supabaseAdminClient
      .from("categories")
      .select("*")
      .in(
        "parent_category_id",
        customerData?.customer_categories?.map(
          (item) => item.categories?.id ?? ""
        ) ?? []
      );

  if (subCategoriesError) {
    return res.status(400).json({ error: subCategoriesError.message });
  }

  const group: GroupWithDiscount | undefined = customerData.customer_groups
    ? {
        id: customerData.customer_groups?.groups.id ?? "",
        name: customerData.customer_groups?.groups?.name ?? "",
      }
    : undefined;

  const categories: Category[] =
    customerData.customer_categories?.map(
      (item) => item.categories ?? ({} as Category)
    ) ?? [];

  const customer = customerData
    ? {
        id: customerData.id,
        company_logo: customerData.company_logo,
        company_name: customerData.company_name,
        company_website: customerData.company_website,
        cost_percentage: customerData.cost_percentage,
        created_at: customerData.created_at,
        credit_limit: customerData.credit_limit,
        customer_number: customerData.customer_number,
        payment_options: customerData.payment_options,
        phone: customerData.phone,
        primary_contact_name: customerData.primary_contact_name,
        role: customerData.role,
        shipping_notes: customerData.shipping_notes,
        status: customerData.status,
        user_id: customerData.user_id,
        group: group,
        categories: categories,
        subCategories: subCategories,
      }
    : null;

  return res.status(200).json({ customer });
}
