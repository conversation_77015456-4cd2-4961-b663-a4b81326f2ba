import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAuth } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { Notification } from "@/supabase/types";
import { NextApiResponse } from "next";

export default checkAuth(
    matchRoute({
        GET: getNotificationsHandler,
    })
);

export interface GetNotificationsResponse {
    notifications?: Notification[];
    error?: string;
}

async function getNotificationsHandler(
    req: NextApiRequestWithUserContext,
    res: NextApiResponse<GetNotificationsResponse>
) {
    const { slug: id } = req.query;
    const { user } = req;

    if (!user) {
        return res.status(401).json({ error: "Unauthorized" });
    }

    if (user.id !== id) {
        return res.status(403).json({ error: "Forbidden" });
    }

    const supabaseAdminClient = createSupabaseAdminClient();

    const { data, error } = await supabaseAdminClient
        .from("notifications")
        .select("*")
        .eq("user_id", id)
        .neq("dismissed", true)
        .order("created_at", { ascending: false });

    if (error) {
        return res.status(400).json({ error: error.message });
    }

    return res.status(200).json({ notifications: data });
}
