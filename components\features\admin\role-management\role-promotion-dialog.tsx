import { <PERSON><PERSON> } from "@/components/ui/shadcn-button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog-shadcn";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { RoleBadge } from "@/components/ui/role-badge";
import { toast } from "@/hooks/use-toast";
import { PublicUserWithCustomer } from "@/pages/api/users/index";
import { UserRole } from "@/supabase/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import useAuthStore from "@/stores/auth-store";
import { useUpdateUserRoleMutation } from "@/queries/admin-queries";

interface RolePromotionDialogProps {
  user: PublicUserWithCustomer;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const updateRoleSchema = z.object({
  role: z.enum(["admin", "manager", "staff"], {
    required_error: "Please select a role",
  }),
});

type UpdateRoleFormData = z.infer<typeof updateRoleSchema>;

const roleOptions: { value: UserRole; label: string; description: string }[] = [
  {
    value: "manager",
    label: "Customer",
    description: "Customer access with management capabilities",
  },
  {
    value: "staff",
    label: "Staff",
    description: "Limited admin access for day-to-day operations",
  },
  {
    value: "admin",
    label: "Admin",
    description: "Full administrative access to all features",
  },
];

export function RolePromotionDialog({
  user,
  open,
  onOpenChange,
}: RolePromotionDialogProps) {
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);
  const token = useAuthStore((state) => state.token);

  const form = useForm<UpdateRoleFormData>({
    resolver: zodResolver(updateRoleSchema),
    defaultValues: {
      role: user.role,
    },
  });

  const updateRoleMutation = useUpdateUserRoleMutation(token || "");

  const handleSubmit = (data: UpdateRoleFormData) => {
    if (data.role === user.role) {
      toast({
        title: "No Changes",
        description: "The selected role is the same as the current role",
        variant: "default",
      });
      onOpenChange(false);
      return;
    }

    setSelectedRole(data.role);
    setIsConfirmOpen(true);
  };

  const handleConfirm = () => {
    if (selectedRole && selectedRole !== user.role) {
      updateRoleMutation.mutate(
        { id: user.id, role: selectedRole },
        {
          onSuccess: (data) => {
            toast({
              title: "Role Updated",
              description: data.message || "User role has been updated successfully",
              variant: "default",
            });
            
            // Close dialogs
            setIsConfirmOpen(false);
            onOpenChange(false);
            
            // Reset form
            form.reset();
          },
          onError: (error: Error) => {
            toast({
              title: "Error",
              description: error.message || "Failed to update user role",
              variant: "destructive",
            });
            setIsConfirmOpen(false);
          },
        }
      );
    }
  };

  const handleCancel = () => {
    setIsConfirmOpen(false);
    setSelectedRole(null);
  };

  const handleDialogClose = (open: boolean) => {
    if (!open) {
      form.reset();
      setSelectedRole(null);
      setIsConfirmOpen(false);
    }
    onOpenChange(open);
  };

  return (
    <>
      {/* Main Role Selection Dialog */}
      <Dialog open={open && !isConfirmOpen} onOpenChange={handleDialogClose}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Update User Role</DialogTitle>
            <DialogDescription>
              Change the role for {user.first_name} {user.last_name} ({user.email})
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="mb-4">
              <p className="text-sm font-medium text-gray-700">Current Role:</p>
              <RoleBadge role={user.role} className="mt-1" />
            </div>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel className="text-base font-medium">
                        Select New Role
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          value={field.value}
                          className="space-y-3"
                        >
                          {roleOptions.map((option) => (
                            <div
                              key={option.value}
                              className="flex items-start space-x-3 space-y-0"
                            >
                              <RadioGroupItem
                                value={option.value}
                                id={option.value}
                                className="mt-1.5"
                              />
                              <div className="flex-1 space-y-1">
                                <label
                                  htmlFor={option.value}
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                                >
                                  <div className="flex items-center gap-2">
                                    <RoleBadge role={option.value} />
                                    <span>{option.label}</span>
                                  </div>
                                </label>
                                <p className="text-xs text-gray-500">
                                  {option.description}
                                </p>
                              </div>
                            </div>
                          ))}
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => handleDialogClose(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">Continue</Button>
                </DialogFooter>
              </form>
            </Form>
          </div>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <Dialog open={isConfirmOpen} onOpenChange={setIsConfirmOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Confirm Role Change</DialogTitle>
            <DialogDescription>
              Are you sure you want to change {user.first_name} {user.last_name}'s role?
            </DialogDescription>
          </DialogHeader>

          <div className="py-4 space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Current Role:</span>
                <RoleBadge role={user.role} />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">New Role:</span>
                {selectedRole && <RoleBadge role={selectedRole} />}
              </div>
            </div>

            {selectedRole === "admin" && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  ⚠️ This user will have full administrative access
                </p>
              </div>
            )}

            {user.role === "admin" && selectedRole !== "admin" && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-800">
                  ⚠️ This user will lose administrative privileges
                </p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={updateRoleMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleConfirm}
              disabled={updateRoleMutation.isPending}
              variant={selectedRole === "admin" ? "default" : user.role === "admin" ? "destructive" : "default"}
            >
              {updateRoleMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Confirm Change"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}