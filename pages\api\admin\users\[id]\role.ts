import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { UserRole } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

const updateUserRoleSchema = z.object({
  role: z.enum(["admin", "manager", "staff"], {
    errorMap: () => ({ message: "Role must be one of: admin, manager, staff" }),
  }),
});

interface UpdateUserRoleRequest extends NextApiRequest {
  body: {
    role: UserRole;
  };
  query: {
    id: string;
  };
}

interface UpdateUserRoleResponse {
  success: boolean;
  message: string;
  data?: {
    id: string;
    role: UserRole;
  };
  error?: string;
}

async function updateUserRoleHandler(
  req: UpdateUserRoleRequest,
  res: NextApiResponse<UpdateUserRoleResponse>
) {
  try {
    const { id } = req.query;
    
    if (!id || typeof id !== "string") {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
        error: "Invalid user ID provided",
      });
    }

    // Validate request body
    const validation = updateUserRoleSchema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({
        success: false,
        message: "Invalid role provided",
        error: validation.error.errors.map(e => e.message).join(", "),
      });
    }

    const { role } = validation.data;
    const supabaseAdminClient = createSupabaseAdminClient();

    // Check if user exists
    const { data: existingUser, error: userError } = await supabaseAdminClient
      .schema("public")
      .from("users")
      .select("id, role, first_name, last_name, email")
      .eq("id", id)
      .single();

    if (userError || !existingUser) {
      return res.status(404).json({
        success: false,
        message: "User not found",
        error: userError?.message || "User does not exist",
      });
    }

    // Check if user is trying to change their own role (prevent self-demotion)
    // Note: This would require getting the current user's ID from the JWT token
    // For now, we'll implement a business rule to prevent demoting the last admin

    // If changing to non-admin role, check if this is the last admin
    if (existingUser.role === "admin" && role !== "admin") {
      const { data: adminUsers, error: adminCountError } = await supabaseAdminClient
        .schema("public")
        .from("users")
        .select("id")
        .eq("role", "admin");

      if (adminCountError) {
        return res.status(500).json({
          success: false,
          message: "Error checking admin users",
          error: adminCountError.message,
        });
      }

      if (adminUsers && adminUsers.length <= 1) {
        return res.status(400).json({
          success: false,
          message: "Cannot demote the last admin user",
          error: "At least one admin user must remain in the system",
        });
      }
    }

    // Update the user's role
    const { data: updatedUser, error: updateError } = await supabaseAdminClient
      .schema("public")
      .from("users")
      .update({ 
        role: role, 
        updated_at: new Date().toISOString() 
      })
      .eq("id", id)
      .select("id, role")
      .single();

    if (updateError || !updatedUser) {
      return res.status(500).json({
        success: false,
        message: "Failed to update user role",
        error: updateError?.message || "Database update failed",
      });
    }

    // Also update the customer role if customer record exists
    const { data: customer, error: customerFindError } = await supabaseAdminClient
      .schema("public")
      .from("customers")
      .select("id")
      .eq("user_id", id)
      .single();

    if (!customerFindError && customer) {
      // Map user roles to customer roles
      const customerRole = role === "admin" || role === "staff" ? "staff" : "customer";
      
      const { error: customerUpdateError } = await supabaseAdminClient
        .schema("public")
        .from("customers")
        .update({ role: customerRole })
        .eq("user_id", id);

      if (customerUpdateError) {
        console.error(`Warning: Failed to update customer role for user ${id}:`, customerUpdateError.message);
        // Don't fail the entire operation if customer update fails
      } else {
        console.log(`Customer role updated: user ${id} customer role set to ${customerRole}`);
      }
    }

    // Log the role change for audit purposes
    console.log(`User role updated: ${existingUser.email} (${id}) changed from ${existingUser.role} to ${role}`);

    return res.status(200).json({
      success: true,
      message: `Successfully updated ${existingUser.first_name} ${existingUser.last_name}'s role to ${role}`,
      data: {
        id: updatedUser.id,
        role: updatedUser.role,
      },
    });

  } catch (error) {
    console.error("Error in updateUserRoleHandler:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: (error as Error).message || "An unexpected error occurred",
    });
  }
}

export default checkAdmin(matchRoute({
  PUT: updateUserRoleHandler,
}));