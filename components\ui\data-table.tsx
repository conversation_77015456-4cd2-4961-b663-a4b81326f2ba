import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  Table as TableInstance,
  useReactTable,
} from "@tanstack/react-table";
import { ReactNode, useState, useEffect, useRef, useMemo } from "react";
import { DataTablePagination } from "./data-table-pagination";
import { DataTableViewOptions } from "./data-table-view-options";
import { Input } from "./input";
import { Skeleton } from "./skeleton";
import React from "react";

interface DataTableFilterProps<TData> {
  table: TableInstance<TData>;
  column: string;
  placeholder?: string;
  className?: string;
  onSearch?: (value: string) => void;
  serverSideSearch?: boolean;
  debounceTime?: number;
  preserveFocus?: boolean;
}

export const DataTableFilter = React.forwardRef(function DataTableFilter<TData>(
  {
    table,
    column,
    placeholder = "Filter...",
    className,
    onSearch,
    serverSideSearch = false,
    debounceTime = 300,
    preserveFocus = false,
  }: DataTableFilterProps<TData>,
  forwardedRef: React.ForwardedRef<HTMLInputElement>
) {
  const [searchValue, setSearchValue] = useState<string>("");
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const isFirstRenderRef = useRef(true);
  const internalInputRef = useRef<HTMLInputElement>(null);
  const wasFocusedRef = useRef(false);

  // Combine refs
  const inputRef = useMemo(() => {
    if (forwardedRef) {
      return forwardedRef;
    }
    return internalInputRef;
  }, [forwardedRef]);

  // Debounce function for server-side search
  const debouncedSearch = (value: string) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      if (serverSideSearch && onSearch) {
        // Save focus state before the search is triggered and table is refreshed
        if (internalInputRef.current) {
          wasFocusedRef.current =
            document.activeElement === internalInputRef.current;
        }
        onSearch(value);
      }
    }, debounceTime);
  };

  // Clean up the debounce timer on component unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // Set initial value from table if there's already a filter value
  useEffect(() => {
    if (isFirstRenderRef.current && !serverSideSearch) {
      const initialValue = table.getColumn(column)?.getFilterValue() as string;
      if (initialValue) {
        setSearchValue(initialValue);
      }
      isFirstRenderRef.current = false;
    }
  }, [table, column, serverSideSearch]);

  // Restore focus if the input was focused before refresh
  useEffect(() => {
    if (preserveFocus && wasFocusedRef.current && internalInputRef.current) {
      internalInputRef.current.focus();

      // Move cursor to the end of the input
      const length = internalInputRef.current.value.length;
      internalInputRef.current.setSelectionRange(length, length);

      wasFocusedRef.current = false;
    }
  }, [table, preserveFocus]); // Re-run when table changes, which happens after search refresh

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchValue(value);

    // For client-side filtering, update immediately
    if (!serverSideSearch) {
      table.getColumn(column)?.setFilterValue(value);
    } else {
      // For server-side filtering, debounce the search
      debouncedSearch(value);
    }
  };

  return (
    <Input
      ref={inputRef}
      placeholder={placeholder}
      value={searchValue}
      onChange={handleSearchChange}
      className={className}
    />
  );
});

interface DataTableToolbarProps<TData> {
  table: TableInstance<TData>;
  children?: ReactNode;
}

export function DataTableToolbar<TData>({
  table,
  children,
}: DataTableToolbarProps<TData>) {
  return (
    <div className="flex items-center justify-between py-4">
      <div className="flex items-center gap-2 min-w-sm">{children}</div>
      <DataTableViewOptions table={table} />
    </div>
  );
}

interface DataTableProps<T, V> {
  columns: ColumnDef<T, V>[];
  data: T[];
  table?: TableInstance<T>;
  filterColumn?: string;
  filterPlaceholder?: string;
  renderToolbar?: (table: TableInstance<T>) => ReactNode;
  className?: string;
}

export function DataTable<T, V>({
  columns,
  data,
  table: externalTable,
  filterColumn,
  filterPlaceholder,
  renderToolbar,
  className,
}: DataTableProps<T, V>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [rowSelection, setRowSelection] = useState({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  // Use the provided table instance or create a new one
  const table =
    externalTable ||
    useReactTable({
      data,
      columns,
      getCoreRowModel: getCoreRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      onSortingChange: setSorting,
      getSortedRowModel: getSortedRowModel(),
      onRowSelectionChange: setRowSelection,
      onColumnFiltersChange: setColumnFilters,
      getFilteredRowModel: getFilteredRowModel(),
      state: {
        sorting,
        rowSelection,
        columnFilters,
      },
    });

  return (
    <div className="w-full h-full flex flex-col gap-2">
      {renderToolbar ? (
        renderToolbar(table)
      ) : filterColumn ? (
        <DataTableToolbar table={table}>
          <DataTableFilter
            table={table}
            column={filterColumn}
            placeholder={filterPlaceholder}
            className="max-w-lg"
          />
        </DataTableToolbar>
      ) : null}
      <div className="relative w-full h-full">
        <Table className={cn("rounded-xl border-2", className)}>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => {
              return (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              );
            })}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => {
                return (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => {
                      return (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="w-full h-fit py-10">
        <DataTablePagination table={table} />
      </div>
    </div>
  );
}

export function DataTableSkeleton() {
  return (
    <div className="relative w-full h-full">
      <Table>
        <TableHeader>
          <TableRow>
            {Array.from({ length: 6 }).map((_, i) => {
              return (
                <TableHead key={`data-table-head-skeleton-${i}`}>
                  <Skeleton className="h-8 w-[120px]" />
                </TableHead>
              );
            })}
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 6 }).map((_, i) => {
            return (
              <TableRow key={`data-table-body-skeleton-${i}`}>
                <TableCell colSpan={6} className="w-full h-16 text-center">
                  <Skeleton className="h-6 w-full" />
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
