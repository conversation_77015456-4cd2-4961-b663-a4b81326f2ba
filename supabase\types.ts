import { Database } from "supabase/database.types";

export const RoleAdmin = "admin";
export const RoleCustomer = "customer";
export const RoleManager = "manager";
export const RoleStaff = "staff";

export type AccountRole = Database["public"]["Enums"]["customer_role"];
export type UserRole = Database["public"]["Enums"]["user_role"];
export type AccountStatus = Database["public"]["Enums"]["account_status"];
export type ShippingAddress =
  Database["public"]["Tables"]["shipping_addresses"]["Row"];
export type BillingAddress =
  Database["public"]["Tables"]["billing_addresses"]["Row"];
export type Customer = Database["public"]["Tables"]["customers"]["Row"];
export type Discount = Database["public"]["Tables"]["discounts"]["Row"];
export type MinimumOrderedQuantity = Database["public"]["Tables"]["moq"]["Row"];
export type OrderStatus = Database["public"]["Tables"]["order_statuses"]["Row"];
export type Order = Database["public"]["Tables"]["orders"]["Row"];
export type OrderItem = Database["public"]["Tables"]["order_items"]["Row"];
export type Product = Database["public"]["Tables"]["products"]["Row"];
export type RequestForQuoteStatus = Database["public"]["Tables"]["rfq"]["Row"];
export type PublicUser = Database["public"]["Tables"]["users"]["Row"];
export type PasswordReset =
  Database["public"]["Tables"]["password_resets"]["Row"];
export type Group = Database["public"]["Tables"]["groups"]["Row"];
export type CustomerGroup =
  Database["public"]["Tables"]["customer_groups"]["Row"];
export type Category = Database["public"]["Tables"]["categories"]["Row"];
export type CustomerCategory =
  Database["public"]["Tables"]["customer_categories"]["Row"];
export type ProductCategory =
  Database["public"]["Tables"]["product_categories"]["Row"];
export type CancellationRequest =
  Database["public"]["Tables"]["cancellation_requests"]["Row"];
export type CancellationRequestStatus =
  Database["public"]["Enums"]["cancellation_request_status"];
export type UserStatus = Database["public"]["Enums"]["account_status"];
export type ProductGroupPrice =
  Database["public"]["Tables"]["product_group_prices"]["Row"];
export type Permission = Database["public"]["Tables"]["permissions"]["Row"];
export type BusinessDetail =
  Database["public"]["Tables"]["business_details"]["Row"];
export type NevadaTaxRate = Database["public"]["Tables"]["nevada_tax_rates"]["Row"];
export type NevadaTaxRateInsert = Database["public"]["Tables"]["nevada_tax_rates"]["Insert"];
export type NevadaTaxRateUpdate = Database["public"]["Tables"]["nevada_tax_rates"]["Update"];
export type Notification = Database["public"]["Tables"]["notifications"]["Row"];
export type NotificationType = Database["public"]["Enums"]["notification_type"];

// Create a type for permission action strings
export type PermissionAction = `${
  | "read"
  | "create"
  | "update"
  | "delete"}:${string}`;

export type ProductOptionType = "select" | "text" | "number";

export type OptionSelectProps = {
  value: string;
  name: string;
  price: number | undefined;
};

export interface ProductOptionSelect {
  name: string;
  type: ProductOptionType;
  value: string;
  options: OptionSelectProps[];
}

export interface ProductOptionText {
  name: string;
  type: ProductOptionType;
  value: string;
}

export interface ProductOptionNumber {
  name: string;
  type: ProductOptionType;
  value: number;
  min: number;
  max: number;
}

export type ProductOption =
  | ProductOptionText
  | ProductOptionSelect
  | ProductOptionNumber;
export type ProductOptions = ProductOption[];

export interface ProductSpecification {
  specification: { [key: string]: string };
  finish_and_material: string;
  features: string[];
}

export interface ProductInstallation {
  label: string;
  fileUrl?: string;
}

export interface ProductHelpfulHint {
  label: string;
  url: string;
}

export interface ProductDeliveryAndShipping {
  shipping?: {
    label: string;
    url: string;
  };
  delivery?: {
    label: string;
    url: string;
  };
}

export interface ProductWarranty {
  label: string;
  url: string;
}
