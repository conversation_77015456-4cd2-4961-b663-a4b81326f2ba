{"name": "@webriq-pagebuilder/site-template-default", "version": "6.0.8", "private": true, "license": "MIT", "author": {"name": "WebriQ", "email": "<EMAIL>"}, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "update:tag": "echo 'Enter custom tag: 👇 (eg: latest, next, your-own-tag)' && read ctag && yarn add @webriq-pagebuilder/sanity-plugin-input-component-gpt3@$ctag @webriq-pagebuilder/sanity-plugin-input-component-variants@$ctag @webriq-pagebuilder/sanity-plugin-schema-blog@$ctag @webriq-pagebuilder/sanity-plugin-schema-commerce@$ctag @webriq-pagebuilder/sanity-plugin-schema-default@$ctag @webriq-pagebuilder/sanity-plugin-webriq-blog@$ctag @webriq-pagebuilder/sanity-plugin-webriq-components@$ctag @webriq-pagebuilder/sanity-plugin-webriq-forms@$ctag @webriq-pagebuilder/sanity-plugin-webriq-payments@$ctag @webriq-pagebuilder/sanity-plugin-desk-studio-version@$ctag @webriq-pagebuilder/sanity-plugin-inspector-inline-edit@$ctag @webriq-pagebuilder/sanity-plugin-input-component-social-accounts@$ctag", "update-stackshift:tag": "echo 'Enter custom tag: 👇 (eg: latest, next, your-own-tag)' && read ctag && yarn add @stackshift-ui/app-promo@$ctag @stackshift-ui/blog@$ctag @stackshift-ui/button@$ctag @stackshift-ui/call-to-action@$ctag @stackshift-ui/contact@$ctag @stackshift-ui/container@$ctag @stackshift-ui/cookies@$ctag @stackshift-ui/faqs@$ctag @stackshift-ui/features@$ctag @stackshift-ui/flex@$ctag @stackshift-ui/footer@$ctag @stackshift-ui/form-field@$ctag @stackshift-ui/grid@$ctag @stackshift-ui/header@$ctag @stackshift-ui/heading@$ctag @stackshift-ui/how-it-works@$ctag @stackshift-ui/image@$ctag @stackshift-ui/input@$ctag @stackshift-ui/link@$ctag @stackshift-ui/logo-cloud@$ctag @stackshift-ui/navigation@$ctag @stackshift-ui/newsletter@$ctag @stackshift-ui/portfolio@$ctag @stackshift-ui/section@$ctag @stackshift-ui/signin-signup@$ctag @stackshift-ui/statistics@$ctag @stackshift-ui/swiper-pagination@$ctag @stackshift-ui/system@$ctag @stackshift-ui/team@$ctag @stackshift-ui/testimonial@$ctag @stackshift-ui/text@$ctag @stackshift-ui/text-component@$ctag @stackshift-ui/webriq-form@$ctag", "update-stackshift": "yarn add @stackshift-ui/app-promo@latest @stackshift-ui/blog@latest @stackshift-ui/button@latest @stackshift-ui/call-to-action@latest @stackshift-ui/contact@latest @stackshift-ui/container@latest @stackshift-ui/cookies@latest @stackshift-ui/faqs@latest @stackshift-ui/features@latest @stackshift-ui/flex@latest @stackshift-ui/footer@latest @stackshift-ui/form-field@latest @stackshift-ui/grid@latest @stackshift-ui/header@latest @stackshift-ui/heading@latest @stackshift-ui/how-it-works@latest @stackshift-ui/image@latest @stackshift-ui/input@latest @stackshift-ui/link@latest @stackshift-ui/logo-cloud@latest @stackshift-ui/navigation@latest @stackshift-ui/newsletter@latest @stackshift-ui/portfolio@latest @stackshift-ui/section@latest @stackshift-ui/signin-signup@latest @stackshift-ui/statistics@latest @stackshift-ui/swiper-pagination@latest @stackshift-ui/system@latest @stackshift-ui/team@latest @stackshift-ui/testimonial@latest @stackshift-ui/text@latest @stackshift-ui/text-component@latest @stackshift-ui/webriq-form@latest", "stackshift:custom": "plop", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "build-storybook-docs": "storybook build --docs", "chromatic": "npx chromatic --project-token=chpt_68d919151b3e677 --only-changed", "test": "npx playwright test", "test:ui": "npx playwright test --ui", "test:generate": "npx playwright codegen localhost:3000", "generate": "plop"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@portabletext/react": "^3.0.9", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-tooltip": "^1.1.8", "@sanity/code-input": "^4.1.1", "@sanity/icons": "^3.4.0", "@sanity/image-url": "^1.0.2", "@sanity/types": "^3.17.0", "@sanity/ui": "^2.8.9", "@sanity/vision": "^3.17.0", "@stackshift-ui/app-promo": "^6.0.12", "@stackshift-ui/blog": "^6.0.15", "@stackshift-ui/button": "^6.0.11", "@stackshift-ui/call-to-action": "^6.0.14", "@stackshift-ui/contact": "^6.0.13", "@stackshift-ui/container": "^6.0.11", "@stackshift-ui/cookies": "^6.0.12", "@stackshift-ui/faqs": "^6.0.14", "@stackshift-ui/features": "^6.0.13", "@stackshift-ui/flex": "^6.0.11", "@stackshift-ui/footer": "^6.0.13", "@stackshift-ui/form-field": "^6.0.13", "@stackshift-ui/grid": "^6.0.11", "@stackshift-ui/header": "^6.0.15", "@stackshift-ui/heading": "^6.0.11", "@stackshift-ui/how-it-works": "^6.0.12", "@stackshift-ui/image": "^6.0.11", "@stackshift-ui/input": "^6.0.12", "@stackshift-ui/link": "^6.0.11", "@stackshift-ui/logo-cloud": "^6.0.14", "@stackshift-ui/navigation": "^6.0.13", "@stackshift-ui/newsletter": "^6.0.12", "@stackshift-ui/portfolio": "^6.0.13", "@stackshift-ui/section": "^6.0.11", "@stackshift-ui/signin-signup": "^6.0.14", "@stackshift-ui/statistics": "^6.0.13", "@stackshift-ui/swiper-pagination": "^6.0.11", "@stackshift-ui/system": "^6.0.11", "@stackshift-ui/team": "^6.0.14", "@stackshift-ui/testimonial": "^6.0.13", "@stackshift-ui/text": "^6.0.11", "@stackshift-ui/text-component": "^6.0.13", "@stackshift-ui/webriq-form": "^6.0.11", "@stripe/react-stripe-js": "^2.1.0", "@stripe/stripe-js": "^4.8.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.65.0", "@tanstack/react-query-devtools": "^5.65.0", "@tanstack/react-table": "^8.21.2", "@tiptap/extension-image": "^2.11.7", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@types/jdenticon": "^2.2.0", "@webriq-pagebuilder/sanity-plugin-desk-studio-version": "^6.0.8", "@webriq-pagebuilder/sanity-plugin-input-component-gpt3": "^6.0.8", "@webriq-pagebuilder/sanity-plugin-input-component-social-accounts": "^6.0.8", "@webriq-pagebuilder/sanity-plugin-input-component-variants": "^6.0.8", "@webriq-pagebuilder/sanity-plugin-inspector-inline-edit": "^6.0.8", "@webriq-pagebuilder/sanity-plugin-schema-blog": "^6.0.8", "@webriq-pagebuilder/sanity-plugin-schema-commerce": "^6.0.8", "@webriq-pagebuilder/sanity-plugin-schema-default": "^6.0.8", "@webriq-pagebuilder/sanity-plugin-webriq-blog": "^6.0.8", "@webriq-pagebuilder/sanity-plugin-webriq-components": "^6.0.8", "@webriq-pagebuilder/sanity-plugin-webriq-forms": "^6.0.8", "@webriq-pagebuilder/sanity-plugin-webriq-payments": "^6.0.8", "axios": "^1.3.6", "braille": "^1.1.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "cookie": "^1.0.2", "cors": "^2.8.5", "country-data-list": "^1.4.1", "crypto-js": "^4.1.1", "date-fns": "^4.1.0", "deepmerge": "^4.3.1", "history": "^5.3.0", "jdenticon": "^3.3.0", "jsonwebtoken": "^9.0.0", "libphonenumber-js": "^1.12.8", "lodash": "^4.17.21", "lucide-react": "^0.474.0", "magicast": "^0.3.3", "marked": "^14.1.2", "msw-storybook-addon": "1.10.0", "nanoid": "3.1.30", "next": "^14.2.25", "next-sanity": "4.3.3", "node-fetch": "^3.3.2", "nodemailer": "^6.10.0", "polished": "^4.2.2", "react": "^18.2.0", "react-circle-flags": "^0.0.23", "react-colorful": "^5.6.1", "react-country-state-city": "^1.1.12", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-error-boundary": "^4.1.1", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.1", "react-icons": "^5.4.0", "react-is": "^18.2.0", "react-rx": "^4.1.27", "react-toast": "^1.0.3", "recharts": "^2.15.1", "sanity": "^3.17.0", "sanity-plugin-iframe-pane": "2.6.1", "sanity-plugin-media": "^2.2.2", "sass": "^1.62.1", "sharp": "^0.33.5", "split-pane-react": "^0.1.3", "stripe": "^17.2.0", "styled-components": "^6.1.13", "swiper": "^11.1.14", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "ts-dedent": "^2.2.0", "vanilla-cookieconsent": "^3.0.1", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@chromatic-com/storybook": "^3", "@netlify/plugin-nextjs": "^5.5.0", "@playwright/test": "^1.51.1", "@storybook/addon-designs": "^8.0.4", "@storybook/addon-docs": "^8.4.7", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-links": "^8.4.7", "@storybook/addon-mdx-gfm": "^8.4.7", "@storybook/addon-onboarding": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/core-common": "^8.4.7", "@storybook/csf-tools": "^8.4.7", "@storybook/manager-api": "^8.4.7", "@storybook/nextjs": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/test": "^8.4.7", "@storybook/testing-library": "^0.2.2", "@storybook/theming": "^8.4.7", "@storybook/types": "^8.4.7", "@types/react": "^18.2.65", "@types/react-dom": "^18.2.21", "@types/vhtml": "^2.2.4", "autoprefixer": "^10.4.20", "chromatic": "^11.0.8", "dotenv": "^16.4.7", "eslint": "^9.12.0", "eslint-config-next": "14.1.3", "eslint-plugin-storybook": "^0.11.2", "msw": "1.3.2", "plop": "^4.0.1", "postcss": "^8.4.47", "prettier": "^3.0.3", "sitemap": "^8.0.0", "storybook": "^8.4.7", "tailwindcss": "^3.4.12", "typescript": "5.6.3"}, "packageManager": "yarn@1.22.21+sha512.ca75da26c00327d26267ce33536e5790f18ebd53266796fbb664d2a4a5116308042dd8ee7003b276a20eace7d3c5561c3577bdd71bcb67071187af124779620a"}