import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAuth } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiResponse } from "next";

export default checkAuth(
    matchRoute({
        PATCH: dismissNotificationHandler,
    })
);

export interface DismissNotificationResponse {
    error?: string;
    success?: boolean;
}

async function dismissNotificationHandler(
    req: NextApiRequestWithUserContext,
    res: NextApiResponse<DismissNotificationResponse>
) {
    const { id } = req.query;
    const { user } = req;

    if (!user || !id) {
        return res.status(401).json({ error: "Unauthorized" });
    }

    const supabaseAdminClient = createSupabaseAdminClient();
    const { error } = await supabaseAdminClient
        .from("notifications")
        .update({ dismissed: true })
        .eq("id", id.toString());

    if (error) {
        return res.status(400).json({ error: error.message });
    }

    return res.status(200).json({ success: true });
}