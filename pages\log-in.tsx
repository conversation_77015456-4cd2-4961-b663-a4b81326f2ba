import { SvgSpinners90Ring } from "@/components/common/icons";
import { Footer } from "@/components/sections/footer";
import { Navigation } from "@/components/sections/navigation";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/shadcn-button";
import { useToast } from "@/hooks/use-toast";
import {
  useFooterQuery,
  useNavigationQuery,
} from "@/queries/component-queries";
import { zodResolver } from "@hookform/resolvers/zod";
import { Eye, EyeClosed } from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useSigninMutation } from "queries/user-queries";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import useAuthStore from "stores/auth-store";
import { z } from "zod";
export const signinFormSchema = z.object({
  email: z.string().email({ message: "Invalid email" }),
  password: z
    .string()
    .min(8, { message: "Password must be at least 8 characters" }),
});

type SignInForm = z.infer<typeof signinFormSchema>;

export default function SignUp() {
  const { toast } = useToast();
  const signInMutation = useSigninMutation();
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)();

  const [showPassword, setShowPassword] = useState(false);
  const togglePasswordVisibility = () => setShowPassword(!showPassword);

  const signInForm = useForm<SignInForm>({
    resolver: zodResolver(signinFormSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const signUpHandler = async (data: SignInForm) => {
    if (isAuthenticated) return;
    try {
      await signInMutation.mutateAsync(data);
    } catch (e) { }
  };

  useEffect(
    function showToast() {
      if (signInMutation.isSuccess) {
        toast({
          title: "Log in successful",
          description: "You have successfully logged in.",
          variant: "success",
          duration: 3000,
        });
      }

      if (signInMutation.isError) {
        toast({
          title: "Log in failed",
          description: signInMutation?.error?.message ?? "Something went wrong",
          variant: "destructive",
          duration: 3000,
        });
      }
    },
    [signInMutation.isSuccess, signInMutation.isError]
  );
  const navigationData = useNavigationQuery();
  const footerData = useFooterQuery();

  return (
    <React.Fragment>
      {navigationData.data && (
        <div className="sticky top-0 left-0 w-full h-full z-50">
          <Navigation data={navigationData.data?.data} />
        </div>
      )}
      <Head>
        <title>Log In</title>
        <meta name="description" content="Log in to your account" />
      </Head>
      <section className="relative w-full py-40">
        <div className="w-full h-full mx-auto max-w-6xl grid place-content-center">
          <div className="px-12 py-10 shadow-lg shadow-slate-300 rounded-sm w-lg">
            <h1 className="text-3xl font-medium text-center py-5">Log In</h1>
            <Form {...signInForm}>
              <form
                onSubmit={signInForm.handleSubmit(signUpHandler)}
                className="w-full h-full flex flex-col gap-4"
              >
                <FormField
                  control={signInForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel aria-required>Email</FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="email"
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />
                <FormField
                  control={signInForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel
                        aria-required
                        className="flex items-center justify-between"
                      >
                        <span>Password</span>
                        <Link className="text-primary" href="/forgot-password">
                          Forgot your password?
                        </Link>
                      </FormLabel>
                      <FormControl>
                        <div className="relative w-full h-fit">
                          <Input
                            autoComplete="current-password"
                            type={showPassword ? "text" : "password"}
                            placeholder="**********"
                            className="w-full h-fit mr-4"
                            {...field}
                          />
                          <Button type="button" className="w-fit h-fit absolute z-10 right-2 top-1/2 transform -translate-y-1/2 hover:bg-transparent bg-transparent text-primary" size="icon" onClick={togglePasswordVisibility}>
                            {
                              showPassword ? (
                                <EyeClosed className="w-7 h-7" />
                              ) : (
                                <Eye className="w-7 h-7" />
                              )
                            }
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />
                {signInMutation.isError ? (
                  <div className="relative w-full h-fit">
                    <p className="text-red-500 text-sm">
                      {signInMutation.error?.message === "Email not confirmed"
                        ? "Log in failed. Please confirm your email."
                        : signInMutation.error?.message}
                    </p>
                  </div>
                ) : null}
                <div className="w-full h-full flex flex-col items-end">
                  <div className="w-full h-fit flex items-center justify-end py-4">
                    <span className="flex items-center gap-2 text-sm">
                      Don't have an account?
                      <Link
                        target="_self"
                        prefetch
                        href="/sign-up"
                        className="underline text-primary"
                      >
                        Sign up
                      </Link>
                    </span>
                  </div>
                  <Button
                    type="submit"
                    size="lg"
                    className="w-full bg-primary"
                    disabled={signInForm.formState.isSubmitting}
                    aria-disabled={signInForm.formState.isSubmitting}
                  >
                    <span>Log In</span>
                    {signInForm.formState.isSubmitting && <SvgSpinners90Ring />}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </section>
      {footerData.data && (
        <div className="w-full h-full">
          <Footer data={footerData.data?.data} />
        </div>
      )}
    </React.Fragment>
  );
}
