import { Badge } from "@/components/ui/badge";
import { UserRole } from "@/supabase/types";
import { cn } from "@/lib/utils";

interface RoleBadgeProps {
  role: UserRole;
  className?: string;
}

export function RoleBadge({ role, className }: RoleBadgeProps) {
  const getRoleConfig = (role: UserRole) => {
    switch (role) {
      case "admin":
        return {
          label: "Admin",
          variant: "destructive" as const,
          className: "bg-red-50 text-red-700 border-red-200 hover:bg-red-100",
        };
      case "staff":
        return {
          label: "Staff",
          variant: "default" as const,
          className: "bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100",
        };
      case "manager":
        return {
          label: "Customer",
          variant: "secondary" as const,
          className: "bg-green-50 text-green-700 border-green-200 hover:bg-green-100",
        };
      default:
        return {
          label: role,
          variant: "outline" as const,
          className: "",
        };
    }
  };

  const config = getRoleConfig(role);

  return (
    <Badge 
      variant={config.variant}
      className={cn(
        "font-medium text-xs px-2 py-1",
        config.className,
        className
      )}
    >
      {config.label}
    </Badge>
  );
}