import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { PublicUserWithCustomer } from "@/pages/api/users/index";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiResponse } from "next";

export default checkAdmin(
  matchRoute({
    GET: searchUsersHandler,
  }),
);

export interface SearchUsersResponse {
  data?: PublicUserWithCustomer[];
  error?: string;
  total?: number;
}

async function searchUsersHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<SearchUsersResponse>,
) {
  try {
    const { query, page = 1, limit = 10, roles } = req.query;

    if (!query) {
      return res.status(400).json({ error: "Search query is required" });
    }

    const searchTerm = query.toString().toLowerCase();
    const pageNumber = Number(page);
    const limitNumber = Number(limit);
    const offset = (pageNumber - 1) * limitNumber;
    
    // Parse roles parameter - supports both single role and comma-separated multiple roles
    const rolesParam = roles as string;
    const rolesList = rolesParam ? rolesParam.split(',').map(r => r.trim().toLowerCase()) : null;

    const supabaseAdminClient = createSupabaseAdminClient();

    // Build base query with search terms
    const baseQuery = supabaseAdminClient
      .from("users")
      .select(
        "id, first_name, last_name, email, role, status, created_at, notes",
        {
          count: "exact",
        },
      )
      .or(
        `first_name.ilike.%${searchTerm}%,last_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`,
      );

    // Apply role filtering if roles parameter is provided
    if (rolesList && rolesList.length > 0) {
      baseQuery.in("role", rolesList);
    }

    // Search for matching users
    const {
      data: users,
      error: usersError,
      count,
    } = await baseQuery
      .order("created_at", { ascending: false })
      .range(offset, offset + limitNumber - 1);

    if (usersError) {
      return res.status(500).json({ error: usersError.message });
    }

    if (!users || users.length === 0) {
      return res.status(200).json({ data: [], total: 0 });
    }

    const userIds = users.map((user) => user.id);

    // Get business details for these users
    const { data: businessDetails, error: businessError } =
      await supabaseAdminClient
        .from("business_details")
        .select("*")
        .in("user_id", userIds);

    if (businessError) {
      return res.status(500).json({ error: businessError.message });
    }

    // Get customer data for these users
    const { data: customers, error: customersError } = await supabaseAdminClient
      .from("customers")
      .select(
        `
        id,
        user_id,
        company_name,
        primary_contact_name,
        phone,
        customer_number,
        credit_limit,
        company_website,
        role,
        status,
        shipping_notes,
        billing_addresses(*),
        categories:customer_categories(*, category_data:categories(*)),
        group_data:customer_groups(*, data:groups(*))
      `,
      )
      .in("user_id", userIds);

    if (customersError) {
      return res.status(500).json({ error: customersError.message });
    }

    // If no customers found, return just users with business details
    if (!customers?.length) {
      return res.status(200).json({
        data: users.map((user) => ({
          ...user,
          business_details:
            businessDetails?.find((bd) => bd.user_id === user.id) || null,
          customer_data: [],
        })) as unknown as PublicUserWithCustomer[],
        total: count || 0,
      });
    }

    const customerIds = customers.map((c) => c.id);

    // Get all related data for customers concurrently
    const [
      customerGroupsResult,
      billingAddressesResult,
      customerCategoriesResult,
    ] = await Promise.all([
      supabaseAdminClient
        .from("customer_groups")
        .select(
          `
          customer_id,
          groups (
            id,
            name,
            description
          )
        `
        )
        .in("customer_id", customerIds),
      supabaseAdminClient
        .from("billing_addresses")
        .select(
          `
          id,
          city,
          state,
          address,
          country,
          default,
          approved,
          zip_code,
          created_at,
          customer_id
        `
        )
        .in("customer_id", customerIds),
      supabaseAdminClient
        .from("customer_categories")
        .select(
          `
          customer_id,
          categories (
            id,
            name,
            description
          )
        `
        )
        .in("customer_id", customerIds),
    ]);

    const { data: customerGroupsData } = customerGroupsResult;
    const { data: billingAddressesData } = billingAddressesResult;
    const { data: customerCategoriesData } = customerCategoriesResult;

    // Rebuild the nested structure to match the original response format
    const customersWithDetails = users.map((user) => {
      // Find business details for this user
      const userBusinessDetails = businessDetails?.find(
        (bd) => bd.user_id === user.id
      );

      // Find customers for this user
      const userCustomers = customers
        ?.filter((c) => c.user_id === user.id)
        .map((customer) => {
          // Find related data for this customer
          const customerGroups = customerGroupsData?.filter(
            (cg) => cg.customer_id === customer.id
          );
          const customerBillingAddresses = billingAddressesData?.filter(
            (ba) => ba.customer_id === customer.id
          );
          const customerCategories = customerCategoriesData?.filter(
            (cc) => cc.customer_id === customer.id
          );

          return {
            ...customer,
            group_data: customerGroups?.length
              ? customerGroups[0].groups
              : null,
            billing_addresses: customerBillingAddresses || [],
            categories:
              customerCategories?.map((cc) => ({
                category_data: cc.categories,
              })) || [],
          };
        });

      return {
        ...user,
        business_details: userBusinessDetails || null,
        customer_data: userCustomers || [],
      };
    }) as unknown as PublicUserWithCustomer[];

    return res.status(200).json({
      data: customersWithDetails,
      total: count || customersWithDetails.length,
    });
  } catch (error: any) {
    console.error("Search users error:", error);
    return res.status(400).json({
      error: error.message || "An error occurred while searching users data",
    });
  }
}
