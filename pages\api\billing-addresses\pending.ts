import { createMailer, createMailerOptions } from "@/lib/mailer";
import { sendEmail } from "@/lib/mailer/sender";
import { createBillingAddressApprovalTemplate } from "@/lib/mailer/templates";
import { checkAdmin, checkPermission } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { BillingAddress } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default checkAdmin(
  matchRoute({
    GET: getPendingBillingAddressesHandler,
    POST: checkPermission(
      "update:billing_addresses",
      approveBillingAddressHandler
    ),
  })
);

interface BillingAddressWithCustomer extends BillingAddress {
  customer_data: {
    id: string;
    user_data: {
      id: string;
      email: string;
    };
  };
}

export interface GetPendingBillingAddressesResponse {
  error?: string;
  billingAddresses?: BillingAddressWithCustomer[];
  total?: number;
  totalPages?: number;
}

async function getPendingBillingAddressesHandler(
  req: NextApiRequest,
  res: NextApiResponse<GetPendingBillingAddressesResponse>
) {
  const query = req.query;
  const page = query.page ? parseInt(query.page as string) : 1;
  const limit = query.limit ? parseInt(query.limit as string) : 10;

  const supabaseAdminClient = createSupabaseAdminClient();

  const { data, error } = await supabaseAdminClient
    .schema("public")
    .from("billing_addresses")
    .select(
      "*, customer_data:customers(id, company_name, user_data:users(id, email))",
      {
        count: "exact",
      }
    )
    .eq("approved", false)
    .range((page - 1) * limit, page * limit - 1);

  if (error) {
    return res.status(400).json({ error: error.message });
  }

  const total = data.length;
  const totalPages = Math.ceil(total / limit);

  const billingAddresses = data as unknown as BillingAddressWithCustomer[];

  return res.status(200).json({ billingAddresses, total, totalPages });
}

const approveBillingAddressSchema = z.object({
  billing_address_id: z.string(),
});

export type ApproveBillingAddress = z.infer<typeof approveBillingAddressSchema>;

export interface ApproveBillingAddressResponse {
  error?: string;
  data?: string;
}

async function approveBillingAddressHandler(
  req: NextApiRequest,
  res: NextApiResponse<ApproveBillingAddressResponse>
) {
  const parsedData = approveBillingAddressSchema.safeParse(req.body);

  if (parsedData.error) {
    return res.status(400).json({ error: parsedData.error.issues[0].message });
  }

  const { billing_address_id } = parsedData.data;

  const supabaseAdminClient = createSupabaseAdminClient();

  // Get the billing address details before updating
  const { data: billingAddress, error: fetchError } = await supabaseAdminClient
    .schema("public")
    .from("billing_addresses")
    .select(
      "*, customer_data:customers(id, company_name, user_data:users(id, email, first_name, last_name))"
    )
    .eq("id", billing_address_id)
    .single();

  if (fetchError) {
    return res.status(400).json({ error: fetchError.message });
  }

  // Update the billing address
  const { data: _, error } = await supabaseAdminClient
    .schema("public")
    .from("billing_addresses")
    .update({ approved: true })
    .eq("id", billing_address_id);

  if (error) {
    return res.status(400).json({ error: error.message });
  }

  // Send approval email
  const userEmail = billingAddress.customer_data?.user_data?.email;
  const firstName = billingAddress.customer_data?.user_data?.first_name || "";
  const lastName = billingAddress.customer_data?.user_data?.last_name || "";
  const name =
    firstName && lastName
      ? `${firstName} ${lastName}`
      : firstName || lastName || "Customer";

  if (userEmail) {
    const emailTemplate = createBillingAddressApprovalTemplate({
      to: userEmail,
      name: name,
      addressId: billingAddress.id,
      addressLine1: billingAddress.address || "",
      addressLine2: undefined,
      city: billingAddress.city || "",
      state: billingAddress.state || "",
      postalCode: billingAddress.zip_code || "",
      country: billingAddress.country || "",
      approvalDate: new Date().toISOString(),
      accountUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/store/account`,
      additionalNotes:
        "You can now use this address for placing orders and receiving invoices.",
    });

    const mailerOptions = createMailerOptions();
    const mailer = createMailer(mailerOptions);
    await sendEmail(mailer, emailTemplate);
  }

  return res.status(200).json({ data: "Billing address updated" });
}
