import emailTemplate from ".";

export default function createForgotPasswordTemplate(to: string, token: string) {
    const APP_NAME = process.env.APP_NAME ?? "Maxton Valve";
    const APP_EMAIL_FROM = process.env.MAIL_FROM ?? "<EMAIL>";
    const from = `${APP_NAME} <${APP_EMAIL_FROM}>`;

    const url = `${process.env.NEXT_PUBLIC_SITE_URL}/forgot-password/${token}`;

    return emailTemplate({
        to,
        subject: "Reset Your Password",
        from: from,
        body: `
          <p>Your password can be reset by clicking the button below. If you did not request a new password, please ignore this email.</p>
              <a style="display: inline-block; margin-top: 5px; border-radius: 6px; padding: 8px 16px; background-color: #0045d8; color: rgb(248, 250, 252); text-decoration: none" href="${url}">Reset Password</a>
              `
    });
}