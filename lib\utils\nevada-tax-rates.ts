// Nevada County Tax Rates (effective 01/01/20)
// Based on the Nevada Department of Taxation county map

export interface NevadaCountyTaxRate {
  name: string;
  rate: number; // percentage (e.g., 7.725 for 7.725%)
}

export const NEVADA_COUNTY_TAX_RATES: Record<string, NevadaCountyTaxRate> = {
  'Carson City': { name: 'Carson City', rate: 7.60 },
  'Churchill': { name: '<PERSON>', rate: 7.60 },
  '<PERSON>': { name: '<PERSON>', rate: 8.375 },
  '<PERSON>': { name: '<PERSON>', rate: 7.100 },
  '<PERSON><PERSON>': { name: '<PERSON><PERSON>', rate: 7.100 },
  'Esmeralda': { name: '<PERSON><PERSON><PERSON><PERSON>', rate: 6.850 },
  '<PERSON>': { name: '<PERSON>', rate: 6.850 },
  '<PERSON>': { name: '<PERSON>', rate: 6.850 },
  'Lander': { name: '<PERSON><PERSON>', rate: 7.100 },
  'Lincoln': { name: '<PERSON>', rate: 7.100 },
  'Lyon': { name: '<PERSON>', rate: 7.100 },
  'Mineral': { name: 'Mine<PERSON>', rate: 6.850 },
  'Nye': { name: '<PERSON>ye', rate: 7.600 },
  'Pershing': { name: '<PERSON><PERSON>', rate: 7.100 },
  'Storey': { name: '<PERSON>y', rate: 7.60 },
  'Washoe': { name: 'Washoe', rate: 8.265 },
  'White Pine': { name: 'White Pine', rate: 7.725 },
};

// City to County mapping for Nevada
export const NEVADA_CITY_TO_COUNTY: Record<string, string> = {
  // Clark County
  'Las Vegas': 'Clark',
  'Henderson': 'Clark',
  'North Las Vegas': 'Clark',
  'Boulder City': 'Clark',
  'Mesquite': 'Clark',
  'Overton': 'Clark',
  'Searchlight': 'Clark',
  'Bunkerville': 'Clark',
  'Jean': 'Clark',
  'Primm': 'Clark',
  'Cal-Nev-Ari': 'Clark',
  'Enterprise': 'Clark',
  'Paradise': 'Clark',
  'Spring Valley': 'Clark',
  'Summerlin': 'Clark',
  'Whitney': 'Clark',
  
  // Washoe County
  'Reno': 'Washoe',
  'Sparks': 'Washoe',
  'Incline Village': 'Washoe',
  'Crystal Bay': 'Washoe',
  'Verdi': 'Washoe',
  'Gerlach': 'Washoe',
  'Empire': 'Washoe',
  'Nixon': 'Washoe',
  'Wadsworth': 'Washoe',
  
  // Carson City (Independent City)
  'Carson City': 'Carson City',
  
  // Douglas County
  'Gardnerville': 'Douglas',
  'Minden': 'Douglas',
  'Stateline': 'Douglas',
  'Zephyr Cove': 'Douglas',
  'Genoa': 'Douglas',
  'Topaz Lake': 'Douglas',
  
  // Lyon County
  'Yerington': 'Lyon',
  'Fernley': 'Lyon',
  'Dayton': 'Lyon',
  'Silver Springs': 'Lyon',
  'Stagecoach': 'Lyon',
  'Mason': 'Lyon',
  'Smith': 'Lyon',
  
  // Churchill County
  'Fallon': 'Churchill',
  'Stillwater': 'Churchill',
  'Frenchman': 'Churchill',
  
  // Mineral County
  'Hawthorne': 'Mineral',
  'Schurz': 'Mineral',
  'Luning': 'Mineral',
  'Mina': 'Mineral',
  
  // Nye County
  'Pahrump': 'Nye',
  'Tonopah': 'Nye',
  'Beatty': 'Nye',
  'Amargosa Valley': 'Nye',
  'Round Mountain': 'Nye',
  'Gabbs': 'Nye',
  'Manhattan': 'Nye',
  'Belmont': 'Nye',
  
  // Esmeralda County
  'Goldfield': 'Esmeralda',
  'Silver Peak': 'Esmeralda',
  'Dyer': 'Esmeralda',
  
  // Lincoln County
  'Caliente': 'Lincoln',
  'Pioche': 'Lincoln',
  'Panaca': 'Lincoln',
  'Alamo': 'Lincoln',
  'Rachel': 'Lincoln',
  
  // White Pine County
  'Ely': 'White Pine',
  'McGill': 'White Pine',
  'Ruth': 'White Pine',
  'Baker': 'White Pine',
  'Lund': 'White Pine',
  
  // Elko County
  'Elko': 'Elko',
  'West Wendover': 'Elko',
  'Carlin': 'Elko',
  'Wells': 'Elko',
  'Jackpot': 'Elko',
  'Spring Creek': 'Elko',
  'Mountain City': 'Elko',
  'Owyhee': 'Elko',
  
  // Humboldt County
  'Winnemucca': 'Humboldt',
  'McDermitt': 'Humboldt',
  'Golconda': 'Humboldt',
  'Orovada': 'Humboldt',
  'Paradise Valley': 'Humboldt',
  
  // Lander County
  'Battle Mountain': 'Lander',
  'Austin': 'Lander',
  
  // Eureka County
  'Eureka': 'Eureka',
  'Beowawe': 'Eureka',
  'Crescent Valley': 'Eureka',
  
  // Pershing County
  'Lovelock': 'Pershing',
  'Imlay': 'Pershing',
  'Grass Valley': 'Pershing',
  
  // Storey County
  'Virginia City': 'Storey',
  'Gold Hill': 'Storey',
};

export function getTaxRateForCity(city: string, state: string): number {
  // Only calculate tax for Nevada
  if (state !== 'Nevada' && state !== 'NV') {
    return 0;
  }
  
  // Normalize city name for lookup
  const normalizedCity = city.trim();
  
  // Look up county for the city
  const county = NEVADA_CITY_TO_COUNTY[normalizedCity];
  
  if (county && NEVADA_COUNTY_TAX_RATES[county]) {
    return NEVADA_COUNTY_TAX_RATES[county].rate;
  }
  
  // Default to lowest rate if city not found
  return 6.850; // Esmeralda, Eureka, Humboldt, Mineral counties
}

export function getTaxRateForCounty(county: string): number {
  const countyData = NEVADA_COUNTY_TAX_RATES[county];
  return countyData ? countyData.rate : 0;
}

export function calculateTaxAmount(subtotal: number, taxRate: number, isTaxExempt: boolean = false): number {
  if (isTaxExempt || taxRate === 0) {
    return 0;
  }
  
  return Math.round((subtotal * (taxRate / 100)) * 100) / 100; // Round to 2 decimal places
}

export function getCountyForCity(city: string): string | null {
  const normalizedCity = city.trim();
  return NEVADA_CITY_TO_COUNTY[normalizedCity] || null;
}