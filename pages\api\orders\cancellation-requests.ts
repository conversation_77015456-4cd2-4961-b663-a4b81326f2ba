import { createMailer, createMailerOptions } from "@/lib/mailer";
import { sendEmail } from "@/lib/mailer/sender";
import { createCancellationApprovalTemplate } from "@/lib/mailer/templates";
import { checkAdmin, checkPermission } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { CancellationRequest } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default checkAdmin(
  matchRoute({
    GET: getCancellationRequestsHandler,
    POST: checkPermission(
      "update:cancellation_requests",
      updateCancellationRequestHandler,
    ),
  }),
);

export interface CancellationRequestWithCustomerAndOrder
  extends CancellationRequest {
  user_data?: { s; id?: string; email?: string };
  order_data?: {
    id?: string;
    total_amount?: number;
    tax_exempt?: boolean;
    order_status?: {
      status?: string;
    }[];
  };
}

export interface GetCancellationRequestsResponse {
  data?: CancellationRequestWithCustomerAndOrder[];
  error?: string;
  total?: number;
  totalPages?: number;
}

async function getCancellationRequestsHandler(
  req: NextApiRequest,
  res: NextApiResponse<GetCancellationRequestsResponse>,
) {
  const query = req.query;
  const page = query.page ? parseInt(query.page as string) : 1;
  const limit = query.limit ? parseInt(query.limit as string) : 10;
  const search = query.search ? String(query.search) : "";

  const supabaseAdminClient = createSupabaseAdminClient();

  // Get the total count for all cancellation requests first
  const { count, error: countError } = await supabaseAdminClient
    .schema("public")
    .from("cancellation_requests")
    .select("*", { count: "exact", head: true })
    .eq("status", "pending");

  if (countError) {
    console.error("Count error:", countError);
    return res.status(400).json({ error: countError.message });
  }

  // Ensure we have a valid count
  const totalCount = count || 0;

  // Determine if we need to fetch all data (for searching) or just the current page
  const shouldFetchAll = !!search;

  // Build the base query
  let query_builder = supabaseAdminClient
    .schema("public")
    .from("cancellation_requests")
    .select(
      "*, order_data:orders(id, total_amount, tax_exempt, order_status:order_statuses(status)), user_data:users(id, email)",
      { count: "exact" },
    )
    .eq("status", "pending")
    .order("created_at", { ascending: false });

  // Execute the query
  const { data: allRequestsData, error: fetchError } = shouldFetchAll
    ? await query_builder
    : await query_builder.range((page - 1) * limit, page * limit - 1);

  if (fetchError) {
    console.error("Fetch error:", fetchError);
    return res.status(400).json({ error: fetchError.message });
  }

  // Apply search filter if provided
  let filteredData = allRequestsData || [];

  if (search && search.trim() !== "") {
    const searchTerm = search.toLowerCase().trim();
    const isFullUuid = searchTerm.includes("-") && searchTerm.length > 30;

    filteredData = filteredData.filter((request) => {
      // For full UUIDs, do exact matching
      if (isFullUuid) {
        return request.id.toLowerCase() === searchTerm;
      }
      // For partial searches, do substring matching on request ID
      return request.id.toLowerCase().includes(searchTerm);
    });
  }

  // Calculate total filtered count
  const totalFiltered = filteredData.length;

  // Apply pagination to filtered results if we fetched all data
  let paginatedData = filteredData;
  if (shouldFetchAll) {
    const startIdx = (page - 1) * limit;
    const endIdx = startIdx + limit;
    paginatedData = filteredData.slice(startIdx, endIdx);
  }

  // Calculate pagination values
  const total = search ? totalFiltered : totalCount;
  const totalPages = Math.max(1, Math.ceil(total / limit));

  const cancellationRequests =
    paginatedData as unknown as CancellationRequestWithCustomerAndOrder[];

  return res
    .status(200)
    .json({ data: cancellationRequests, total, totalPages });
}

export const updateCancellationRequestSchema = z.object({
  id: z.string().uuid(),
  status: z.enum(["approved", "rejected"]),
  order_id: z.string().uuid(),
});

export type UpdateCancellationRequest = z.infer<
  typeof updateCancellationRequestSchema
>;

export interface UpdateCancellationRequestResponse {
  data?: string;
  error?: string;
}

async function updateCancellationRequestHandler(
  req: NextApiRequest,
  res: NextApiResponse<UpdateCancellationRequestResponse>,
) {
  const parsedData = updateCancellationRequestSchema.safeParse(req.body);

  if (!parsedData.success) {
    return res.status(400).json({ error: parsedData.error.message });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  // First, get the cancellation request details with customer and order data
  const { data: requestData, error: requestError } = await supabaseAdminClient
    .from("cancellation_requests")
    .select(
      `
            *,
            order_data:orders!order_id(
                id,
                invoice,
                total_amount,
                created_at,
                payment_type,
                delivery_method,
                ship_collect,
                ups_account_number,
                purchase_order,
                shipping_address_id,
                billing_address_id,
                order_items(
                    id,
                    quantity,
                    options,
                    item_price,
                    products(
                        id,
                        name,
                        price,
                        image
                    )
                )
            ),
            user_data:users!user_id(
                id,
                email,
                first_name,
                last_name
            ),
            customer_data:customers!customer_id(
                id
            )
        `,
    )
    .eq("id", parsedData.data.id)
    .single();

  if (requestError || !requestData) {
    return res
      .status(400)
      .json({ error: requestError?.message || "Request not found" });
  }

  // Update the cancellation request status
  const { error: updateError } = await supabaseAdminClient
    .from("cancellation_requests")
    .update({ status: parsedData.data.status })
    .eq("id", parsedData.data.id)
    .eq("order_id", parsedData.data.order_id);

  if (updateError) {
    return res.status(400).json({ error: updateError.message });
  }

  // Update the order_statuses table to add a new status

  if (parsedData.data.status === "approved") {
    // Update order status to canceled
    await supabaseAdminClient.from("order_statuses").insert({
      order_id: parsedData.data.order_id,
      status: "canceled",
      customer_id: requestData.customer_data?.id,
    });

    // Send email to customer
    const customerName = `${requestData.user_data?.first_name} ${requestData.user_data?.last_name}`;
    const orderDetailsUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/store/orders/${requestData.order_data?.id}`;

    try {
      // Get customer data for the email
      const { data: customerData } = await supabaseAdminClient
        .from("customers")
        .select(
          "company_name, company_website, customer_number, phone, primary_contact_name, role, shipping_notes",
        )
        .eq("id", requestData.customer_data?.id || "")
        .single();

      // Get shipping address for this order
      const { data: shippingAddress } = requestData.order_data
        ?.shipping_address_id
        ? await supabaseAdminClient
            .from("shipping_addresses")
            .select(
              "contact_name, address, city, state, zip_code, contact_number, country",
            )
            .eq("id", requestData.order_data.shipping_address_id)
            .single()
        : { data: null };

      // Get billing address for this order
      const { data: billingAddress } = requestData.order_data
        ?.billing_address_id
        ? await supabaseAdminClient
            .from("billing_addresses")
            .select("address, city, state, zip_code, country")
            .eq("id", requestData.order_data.billing_address_id)
            .single()
        : { data: null };

      // Format order items for the email
      const items =
        requestData.order_data?.order_items.map((item) => {
          // Format options to match expected structure
          const formattedOptions =
            item.options && Array.isArray(item.options)
              ? item.options.map((opt: any) => ({
                  name: opt.name || "Option",
                  value: String(opt.value || ""),
                  price: typeof opt.price === "number" ? opt.price : undefined,
                }))
              : [];

          return {
            name: item.products.name,
            quantity: item.quantity,
            price: item.item_price ?? item.products.price,
            options: formattedOptions,
          };
        }) || [];

      const emailTemplate = createCancellationApprovalTemplate({
        to: requestData.user_data?.email || "",
        name: customerName,
        firstName: requestData.user_data?.first_name || undefined,
        lastName: requestData.user_data?.last_name || undefined,
        companyName: customerData?.company_name || undefined,
        companyWebsite: customerData?.company_website || undefined,
        accountNumber:
          customerData?.customer_number ||
          requestData.customer_data?.id ||
          undefined,
        shippingNotes: customerData?.shipping_notes || undefined,
        orderId: requestData.order_data?.id || "",
        invoice: requestData.order_data?.invoice || "",
        requestDate: new Date(requestData.created_at).toLocaleDateString(),
        approvalDate: new Date().toLocaleDateString(),
        // Order items
        items: items,
        totalAmount: requestData.order_data?.total_amount ?? undefined,
        // Shipping information
        shippingAddress: shippingAddress
          ? {
              contactName: shippingAddress.contact_name || undefined,
              address: shippingAddress.address || undefined,
              city: shippingAddress.city || undefined,
              state: shippingAddress.state || undefined,
              zipCode: shippingAddress.zip_code || undefined,
              phone: shippingAddress.contact_number || undefined,
              country: shippingAddress.country || undefined,
            }
          : undefined,
        // Billing information
        billingAddress: billingAddress
          ? {
              contactName: customerName || undefined,
              address: billingAddress.address || undefined,
              city: billingAddress.city || undefined,
              state: billingAddress.state || undefined,
              zipCode: billingAddress.zip_code || undefined,
              phone: customerData?.phone || undefined,
              country: billingAddress.country || undefined,
            }
          : undefined,
        // Contact and payment info
        phoneNumber: customerData?.phone || undefined,
        paymentMethod: requestData.order_data?.payment_type || undefined,
        deliveryMethod: requestData.order_data?.delivery_method || undefined,
        shipCollect: requestData.order_data?.ship_collect || false,
        upsAccountNumber:
          requestData.order_data?.ups_account_number || undefined,
        // Purchase order
        poNumber: requestData.order_data?.purchase_order || undefined,
        // Keep refund amount for future use
        refundAmount: requestData.order_data?.total_amount ?? undefined,
        cancellationReason: requestData.comment || "",
        additionalInfo: undefined,
        orderDetailsUrl,
      });

      // Create mailer client and send email
      const mailerOptions = createMailerOptions();
      const mailer = createMailer(mailerOptions);
      await sendEmail(mailer, emailTemplate);
    } catch (emailError: any) {
      console.error("Failed to send cancellation approval email:", emailError);
      // Don't return error response as the cancellation was successful
    }
  }

  return res
    .status(200)
    .json({ data: "Cancellation request updated successfully" });
}
