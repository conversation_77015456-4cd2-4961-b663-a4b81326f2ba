import AdminLayout from "@/components/features/admin/layout";
import { SettingsLayout } from "@/components/features/admin/settings/layout";
import { SvgSpinners90Ring } from "@/components/common/icons";
import { DataTable } from "@/components/ui/data-table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog-shadcn";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/shadcn-button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useState } from "react";
import { toast } from "@/hooks/use-toast";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import Head from "next/head";
import {
  useGetTaxRatesQuery,
  useUpdateTaxRateMutation,
  UpdateTaxRateRequest,
} from "@/queries/tax-rate-queries";
import { NevadaTaxRate } from "@/supabase/types";
import { ColumnDef } from "@tanstack/react-table";

const updateTaxRateSchema = z.object({
  tax_rate: z
    .number()
    .min(0, "Tax rate cannot be negative")
    .max(100, "Tax rate cannot exceed 100%"),
  city: z.string().min(1, "City is required"),
});

type UpdateTaxRateFormValues = z.infer<typeof updateTaxRateSchema>;

// Table Actions Component
function TaxRateTableActions({
  taxRate,
  onEdit
}: {
  taxRate: NevadaTaxRate;
  onEdit: (taxRate: NevadaTaxRate) => void;
}) {
  return (
    <div className="flex justify-end">
      <Button
        variant="outline"
        size="sm"
        onClick={() => onEdit(taxRate)}
        className="h-8 w-8 p-0"
      >
        <Pencil className="h-4 w-4" />
        <span className="sr-only">Edit tax rate</span>
      </Button>
    </div>
  );
}

// Create column definitions function that takes the onEdit handler
const createColumns = (onEdit: (taxRate: NevadaTaxRate) => void): ColumnDef<NevadaTaxRate>[] => [
  {
    accessorKey: "city",
    header: "City",
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue("city") || "Unknown"}</div>
    ),
  },
  {
    accessorKey: "tax_rate",
    header: "Tax Rate",
    cell: ({ row }) => {
      const taxRate = row.getValue("tax_rate") as number;
      return <div>{taxRate ? `${taxRate.toFixed(3)}%` : "0.000%"}</div>;
    },
  },
  {
    accessorKey: "created_at",
    header: "Last Updated",
    cell: ({ row }) => {
      const createdAt = row.getValue("created_at") as string;
      return (
        <div className="text-muted-foreground">
          {createdAt ? new Date(createdAt).toLocaleDateString() : "Unknown"}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: () => <div className="text-right">Actions</div>,
    cell: ({ row }) => <TaxRateTableActions taxRate={row.original} onEdit={onEdit} />,
    meta: {
      className: "sticky right-0 bg-white dark:bg-neutral-950 shadow-[-4px_0_8px_rgba(0,0,0,0.1)] dark:shadow-[-4px_0_8px_rgba(255,255,255,0.1)] z-10 border-l border-neutral-200 dark:border-neutral-800",
    },
  },
];

export default function SettingsTaxRatesPage() {
  const [editingTaxRate, setEditingTaxRate] = useState<NevadaTaxRate | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  
  const { data: taxRates, isLoading, error } = useGetTaxRatesQuery();
  const updateTaxRateMutation = useUpdateTaxRateMutation();

  const form = useForm<UpdateTaxRateFormValues>({
    resolver: zodResolver(updateTaxRateSchema),
    defaultValues: {
      tax_rate: 0,
      city: "",
    },
  });

  const handleEditTaxRate = (taxRate: NevadaTaxRate) => {
    setEditingTaxRate(taxRate);
    form.reset({
      tax_rate: taxRate.tax_rate || 0,
      city: taxRate.city || "",
    });
    setIsEditDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsEditDialogOpen(false);
    setEditingTaxRate(null);
    form.reset();
  };

  const onSubmit = async (values: UpdateTaxRateFormValues) => {
    if (!editingTaxRate?.id) return;

    try {
      await updateTaxRateMutation.mutateAsync({
        city: editingTaxRate.city || "",
        data: values,
      });

      toast({
        title: "Tax rate updated",
        description: `Tax rate for ${values.city} has been updated successfully.`,
      });

      handleCloseDialog();
    } catch (error) {
      toast({
        title: "Error updating tax rate",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <Head>
          <title>Tax Rates - Settings</title>
        </Head>
        <SettingsLayout 
          title="Tax Rates Management" 
          description="Manage Nevada tax rates by city"
        >
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold">Nevada Tax Rates</h2>
                <p className="text-muted-foreground">
                  Manage Nevada tax rates by city
                </p>
              </div>
            </div>
            <div className="flex items-center justify-center py-12">
              <SvgSpinners90Ring className="h-8 w-8" />
            </div>
          </div>
        </SettingsLayout>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <Head>
          <title>Tax Rates - Settings</title>
        </Head>
        <SettingsLayout 
          title="Tax Rates Management" 
          description="Manage Nevada tax rates by city"
        >
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold">Nevada Tax Rates</h2>
                <p className="text-muted-foreground">
                  Manage Nevada tax rates by city
                </p>
              </div>
            </div>
            <Card>
              <CardContent className="pt-6">
                <p className="text-center text-muted-foreground">
                  Error loading tax rates. Please try again later.
                </p>
              </CardContent>
            </Card>
          </div>
        </SettingsLayout>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Tax Rates - Settings</title>
      </Head>
      <SettingsLayout 
        title="Tax Rates Management" 
        description="Manage Nevada tax rates for checkout calculations"
      >
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">Nevada Tax Rates</h2>
              <p className="text-muted-foreground">
                Tax rates are applied to orders shipping to Nevada addresses
              </p>
            </div>
            <Badge variant="outline" className="px-6 py-2 text-lg">
              <Settings className="mr-2 h-4 w-4" />
              <span>
                {taxRates?.length || 0} {(taxRates?.length || 0) === 1 ? "city" : "cities"}
              </span>
            </Badge>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Tax Rate Configuration</CardTitle>
              <CardDescription>
                Update tax rates for Nevada cities. These rates will be automatically applied during checkout.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <SvgSpinners90Ring className="h-8 w-8" />
                </div>
              ) : (
                <DataTable
                  columns={createColumns(handleEditTaxRate)}
                  data={taxRates || []}
                  filterColumn="city"
                  filterPlaceholder="Filter cities..."
                />
              )}
            </CardContent>
          </Card>

          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Edit Tax Rate</DialogTitle>
                <DialogDescription>
                  Update the tax rate for {editingTaxRate?.city}
                </DialogDescription>
              </DialogHeader>
              
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>City</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter city name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="tax_rate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tax Rate (%)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.001"
                            min="0"
                            max="100"
                            placeholder="0.000"
                            {...field}
                            value={field.value || ""}
                            onChange={(e) => {
                              const value = parseFloat(e.target.value);
                              field.onChange(isNaN(value) ? 0 : value);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCloseDialog}
                      disabled={updateTaxRateMutation.isPending}
                    >
                      Cancel
                    </Button>
                    <Button 
                      type="submit" 
                      disabled={updateTaxRateMutation.isPending}
                      className="min-w-[100px]"
                    >
                      {updateTaxRateMutation.isPending ? (
                        <SvgSpinners90Ring className="h-4 w-4" />
                      ) : (
                        "Update"
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </SettingsLayout>
    </AdminLayout>
  );
}