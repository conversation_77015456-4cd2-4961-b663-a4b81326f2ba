# Features Documentation

## Table of Contents
- [Store Dashboard (Customer)](#store-dashboard-customer)
  - [Cart Management](#cart-management)
  - [Authentication](#authentication)
  - [Order Management](#order-management)
  - [Address Management](#address-management)
  - [Product Browsing](#product-browsing)
  - [Customer Dashboard](#customer-dashboard)
- [Admin Dashboard](#admin-dashboard)
  - [Product Management](#product-management)
  - [Order Management](#order-management-1)
  - [Customer Management](#customer-management)
  - [User Management](#user-management)
  - [Category Management](#category-management)
  - [Group Management](#group-management)
  - [Dashboard and Analytics](#dashboard-and-analytics)
- [Feature Flows](#feature-flows)
  - [Authentication Flow](#authentication-flow)
  - [Cart Flow](#cart-flow)
  - [Order Flow](#order-flow)

## Store Dashboard (Customer)

### Cart Management
- Add items to cart with custom options
  - Support for product options
  - Price calculation including options
  - Quantity management
- Update item quantities
- Remove items from cart
- Calculate subtotal and additional amounts
- Persistent cart storage in localStorage

### Authentication
- User login/signup
- Session management with JWT
- Automatic token refresh
- Role-based access control
- Secure token storage in localStorage

### Order Management
- View order history
- Order details with status tracking
- Order cancellation requests
- Order status updates
- Download order invoices

### Address Management
- Multiple shipping addresses
  - Add/Edit/Delete shipping addresses
  - Set default shipping address
- Multiple billing addresses
  - Add/Edit/Delete billing addresses
  - Set default billing address

### Product Browsing
- Paginated product listing
- Category filtering
- Product search
- Product details view
- Product options selection

### Customer Dashboard
- Order statistics
- Recent orders
- Account settings
- Profile management

## Admin Dashboard

### Product Management
- CRUD operations for products
  - Create new products with details and images
  - Read product listings with pagination
  - Update product information and pricing
  - Delete products from the system
- Product categorization
  - Assign products to categories
  - Remove products from categories
  - Manage product category relationships
- Product options
  - Define product options (size, color, etc.)
  - Set pricing for different options
- Product pricing
  - Base price configuration
  - Bulk price updates
- Inventory management
  - Basic inventory tracking
- Permission-based access control
  - Role-based permissions for product management
  - Required permissions for create, read, update, and delete operations
  - User-friendly access denied screens with appropriate guidance
  - Permission verification through middleware
- Detailed documentation
  - See [Product Management](./PRODUCT_MANAGEMENT.md) for complete implementation details

### Order Management
- Order processing
  - View all orders with filters
  - Process new orders
  - Update order status
  - Add tracking information
- Order status updates
  - Change status (processing, shipped, delivered, etc.)
  - Send notifications on status changes
- Order cancellation handling
  - View cancellation requests
  - Approve/deny cancellation requests
  - Process refunds
- Customer communication
  - Send order updates
  - Respond to inquiries
  - Custom order notes

### Customer Management
- Customer list
  - View all customers with pagination
  - Search and filter customers
  - Customer status management
- Customer details
  - Profile information
  - Contact details
  - Purchase history
- Order history
  - View all orders by customer
  - Order details and status
- Address management
  - View customer addresses
  - Approve billing addresses
  - Manage shipping addresses

### User Management
- User approval
  - Approve new user registrations
  - Review pending users
- User status
  - Activate/deactivate accounts
  - Reset passwords

### Category Management
- CRUD operations for categories
  - Create categories with name and description
  - Read category listings
  - Update category information
  - Delete unused categories
- Category hierarchy
  - Parent-child relationships
  - Multi-level categories

### Group Management
- Create customer groups
- Assign customers to groups
- Group-based pricing

### Dashboard and Analytics
- Sales overview
- Order statistics
- Customer growth
- Product performance
- Revenue reports

## Feature Flows

### Authentication Flow
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as Auth API
    participant S as Supabase

    U->>F: Login/Signup
    F->>A: POST /api/auth
    A->>S: Authenticate
    S-->>A: User + Token
    A-->>F: JWT + User Data
    F->>F: Store in AuthStore
    
    Note over F: Token Expiry Check
    F->>A: POST /api/auth/refresh
    A->>S: Refresh Session
    S-->>A: New Token
    A-->>F: Updated JWT
```

### Cart Flow
```mermaid
sequenceDiagram
    participant U as User
    participant C as CartStore
    participant L as LocalStorage

    U->>C: Add Item
    C->>C: Check Existing Item
    C->>C: Update State
    C->>L: Persist Cart Data
    
    U->>C: Update Quantity
    C->>C: Find Item
    C->>C: Update State
    C->>L: Persist Cart Data
    
    U->>C: Remove Item
    C->>C: Filter Items
    C->>C: Update State
    C->>L: Persist Cart Data
```

### Order Flow
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant S as Supabase

    U->>F: Place Order
    F->>A: POST /api/orders
    A->>S: Create Order
    S-->>A: Order Data
    A-->>F: Order Confirmation
    
    U->>F: View Order
    F->>A: GET /api/orders/:id
    A->>S: Fetch Order
    S-->>A: Order Details
    A-->>F: Display Order
    
    U->>F: Cancel Order
    F->>A: POST /api/orders/:id/cancel
    A->>S: Update Status
    S-->>A: Updated Order
    A-->>F: Cancellation Confirmation
```

### Address Management Flow
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant S as Supabase

    U->>F: Add Address
    F->>A: POST /api/customers/:id/addresses
    A->>S: Store Address
    S-->>A: Address Data
    A-->>F: Update UI
    
    U->>F: Edit Address
    F->>A: PATCH /api/customers/:id/addresses/:addr_id
    A->>S: Update Address
    S-->>A: Updated Data
    A-->>F: Refresh UI
    
    U->>F: Delete Address
    F->>A: DELETE /api/customers/:id/addresses/:addr_id
    A->>S: Remove Address
    S-->>A: Confirmation
    A-->>F: Update UI
```

### Admin Order Management Flow
```mermaid
sequenceDiagram
    participant A as Admin
    participant F as Frontend
    participant API as API
    participant DB as Supabase

    A->>F: View Orders
    F->>API: GET /api/orders
    API->>DB: Fetch Orders
    DB-->>API: Orders Data
    API-->>F: Display Orders List
    
    A->>F: Update Order Status
    F->>API: PUT /api/orders/:id/status
    API->>DB: Update Status
    DB-->>API: Updated Order
    API-->>F: Confirmation
    
    A->>F: Process Cancellation
    F->>API: GET /api/orders/cancellation-requests
    API->>DB: Fetch Requests
    DB-->>API: Cancellation Data
    API-->>F: Display Requests
    
    A->>F: Approve Cancellation
    F->>API: PATCH /api/orders/cancellation-requests
    API->>DB: Update Status
    DB-->>API: Updated Request
    API-->>F: Confirmation
```

### Admin Product Management Flow
```mermaid
sequenceDiagram
    participant A as Admin
    participant F as Frontend
    participant API as API
    participant DB as Supabase

    A->>F: Create Product
    F->>API: POST /api/products
    API->>DB: Insert Product
    DB-->>API: Product Data
    API-->>F: Confirmation
    
    A->>F: Update Product
    F->>API: PATCH /api/products/:id
    API->>DB: Update Product
    DB-->>API: Updated Product
    API-->>F: Confirmation
    
    A->>F: Assign Category
    F->>API: POST /api/products/:id/categories
    API->>DB: Link Category
    DB-->>API: Updated Relationships
    API-->>F: Confirmation
    
    A->>F: Remove Category
    F->>API: DELETE /api/products/:id/categories/:cat_id
    API->>DB: Remove Link
    DB-->>API: Updated Relationships
    API-->>F: Confirmation
``` 