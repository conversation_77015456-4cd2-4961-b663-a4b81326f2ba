import AdminLayout from "@/components/features/admin/layout";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { DataTable, DataTableSkeleton } from "@/components/ui/data-table";
import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog-shadcn";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/shadcn-button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import {
  useCreateCustomerGroupMutation,
  useCreateGroupMutation,
  useDeleteCustomerGroupMutation,
  useGetCustomersQuery,
  useGetGroupsQuery,
} from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Divide, Loader2, MoreHorizontal, Plus, User, X } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { GroupWithMembers, createGroupSchema } from "../../api/groups";
import { checkUserHasPermission } from "@/middlewares/auth-middleware";
import Head from "next/head";

interface GroupMember {
  id: string;
  name: string;
  email: string;
  joinedDate: string;
  groupId: string;
}

export default function Groups() {
  return (
    <AdminLayout>
      <Head>
        <title>Groups</title>
      </Head>
      <GroupsDataTable />
    </AdminLayout>
  );
}

// Define the columns for the DataTable
const columns: ColumnDef<GroupWithMembers>[] = [
  {
    header: "Name",
    accessorKey: "name",
  },
  {
    header: "Description",
    accessorKey: "description",
    cell: ({ row }) => {
      const description = row.original.description || "N/A";
      return <div>{description}</div>;
    },
  },
  {
    header: "Members",
    accessorKey: "members",
    cell: ({ row }) => {
      const count = row.original.members?.length || 0;
      return (
        <div className="flex items-center gap-1">
          <User className="h-4 w-4" />
          <span>{count}{count >= 1000 ? '+' : ''}</span>
        </div>
      );
    },
  },
  {
    header: "Created",
    accessorKey: "created_at",
    cell: ({ row }) => {
      const date = new Date(row.original.created_at);
      return <div>{date.toISOString().split("T")[0]}</div>;
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <GroupActions group={row.original} />,
  },
];

function GroupsDataTable() {
  const [showAddGroup, setShowAddGroup] = useState(false);
  const accessToken = useAuthStore((state) => state.token);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const { data, isLoading } = useGetGroupsQuery(page, limit, accessToken);
  const permissions = useAuthStore((state) => state.permissions);
  const hasCreatePermission = checkUserHasPermission(
    permissions,
    "create:groups"
  );

  const table = useReactTable({
    data: data?.groups ?? [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    manualPagination: true,
    pageCount: Math.ceil((data?.total ?? 0) / limit),
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const newPagination = updater({
          pageIndex: page - 1,
          pageSize: limit,
        });
        setPage(newPagination.pageIndex + 1);
        setLimit(newPagination.pageSize);
      } else {
        setPage(updater.pageIndex + 1);
        setLimit(updater.pageSize);
      }
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    state: {
      sorting,
      columnFilters,
      pagination: {
        pageIndex: page - 1,
        pageSize: limit,
      },
    },
  });

  if (isLoading) return <DataTableSkeleton />;

  return (
    <Card className="container mx-auto border-none border-0 py-0">
      <CardHeader className="flex flex-row items-center justify-between px-0 py-0">
        <div>
          <h3 className="text-4xl font-bold">All Groups</h3>
          <p className="text-sm text-muted-foreground">
            Manage your customer groups and their discount settings.
          </p>
        </div>
        {hasCreatePermission && (
          <Button
            onClick={() => setShowAddGroup(true)}
            size="sm"
            className="gap-2"
          >
            <Plus className="h-4 w-4" /> Add New Group
          </Button>
        )}
      </CardHeader>
      <CardContent className="px-0">
        <DataTable
          columns={columns}
          data={data?.groups ?? []}
          filterColumn="name"
          filterPlaceholder="Search groups..."
          table={table}
        />
      </CardContent>
      {hasCreatePermission && (
        <AddGroupDialog
          open={showAddGroup}
          onOpenChange={setShowAddGroup}
          token={accessToken}
        />
      )}
    </Card>
  );
}

function GroupActions({ group }: { group: GroupWithMembers }) {
  const [showMembers, setShowMembers] = useState(false);
  const [showAddMembers, setShowAddMembers] = useState(false);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => setShowMembers(true)}>
            View Members
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setShowAddMembers(true)}>
            Add Members
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <MembersDialog
        open={showMembers}
        onOpenChange={setShowMembers}
        group={group}
      />

      <AddMembersDialog
        open={showAddMembers}
        onOpenChange={setShowAddMembers}
        group={group}
      />
    </>
  );
}

function MembersDialog({
  open,
  onOpenChange,
  group,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  group: GroupWithMembers;
}) {
  // Format member data to match the structure
  const members =
    group.members?.map((member) => ({
      id: member.id,
      name: `${member.customer_data.user_data.first_name} ${member.customer_data.user_data.last_name}`,
      email: member.customer_data.user_data.email,
      joinedDate: member.customer_data.created_at,
      groupId: group.id,
    })) || [];

  // Define columns for the members table
  const memberColumns: ColumnDef<GroupMember>[] = [
    {
      header: "Name",
      accessorKey: "name",
    },
    {
      header: "Email",
      accessorKey: "email",
    },
    {
      header: "Joined Group",
      accessorKey: "joinedDate",
      cell: ({ row }) => {
        const date = new Date(row.original.joinedDate);
        return <div>{date.toISOString().split("T")[0]}</div>;
      },
    },
    {
      id: "actions",
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => {
        const accessToken = useAuthStore((state) => state.token);
        const deleteCustomerGroupMutation =
          useDeleteCustomerGroupMutation(accessToken);
        const permissions = useAuthStore((state) => state.permissions);
        const hasPermission = checkUserHasPermission(
          permissions,
          "delete:customer_groups"
        );

        const handleRemoveMember = async () => {
          if (!hasPermission) {
            toast({
              title: "Error",
              description:
                "You are not authorized to remove members from groups",
              variant: "destructive",
              duration: 3000,
            });
            return;
          }

          try {
            await deleteCustomerGroupMutation.mutateAsync({
              id: row.original.id,
            });

            toast({
              title: "Success",
              description: "Member removed from group successfully.",
              variant: "default",
              duration: 3000,
            });
          } catch (error) {
            console.error("Error removing member:", error);
            toast({
              title: "Error",
              description:
                "Failed to remove member from group, please try again.",
              variant: "destructive",
              duration: 3000,
            });
          }
        };

        return (
          <div className="text-right">
            <Button
              variant="ghost"
              size="sm"
              className="text-destructive hover:text-destructive"
              onClick={handleRemoveMember}
              disabled={deleteCustomerGroupMutation.isPending || !hasPermission}
            >
              {deleteCustomerGroupMutation.isPending ? "Removing..." : "Remove"}
              {deleteCustomerGroupMutation.isPending && (
                <Loader2 className="h-4 w-4 ml-2 animate-spin" />
              )}
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>{group.name} - Members</DialogTitle>
          <p className="text-sm text-muted-foreground">
            Viewing all customers in this group.
          </p>
        </DialogHeader>

        <div className="py-4">
          <DataTable
            columns={memberColumns}
            data={members}
            filterColumn="email"
            filterPlaceholder="Search members..."
          />
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

function AddGroupDialog({
  open,
  onOpenChange,
  token,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  token: string;
}) {
  // Use React Hook Form with zod validation
  const form = useForm<z.infer<typeof createGroupSchema>>({
    resolver: zodResolver(createGroupSchema),
    defaultValues: {
      name: "",
      description: "",
    },
  });

  // Create mutation hook
  const createGroupMutation = useCreateGroupMutation(
    {
      name: "",
      description: "",
    },
    token
  );

  async function onSubmit(values: z.infer<typeof createGroupSchema>) {
    try {
      // Pass the form values to the mutation function
      await createGroupMutation.mutateAsync(values);

      toast({
        title: "Success",
        description: "Group created successfully.",
        variant: "default",
        duration: 3000,
      });

      onOpenChange(false);
      form.reset();
    } catch (error) {
      console.error("Error creating group:", error);
      toast({
        title: "Error",
        description: "Failed to create group, please try again.",
        variant: "destructive",
        duration: 3000,
      });
    }
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        if (!newOpen) form.reset();
        onOpenChange(newOpen);
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add New Group</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4 py-4"
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Group Name *</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter group name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter group description"
                      rows={3}
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormDescription>
                    Brief description of the customer group and its purpose.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={createGroupMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={
                  createGroupMutation.isPending || form.formState.isSubmitting
                }
              >
                {createGroupMutation.isPending ? "Creating..." : "Create Group"}
                {createGroupMutation.isPending && (
                  <Loader2 className="h-4 w-4 ml-2 animate-spin" />
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function AddMembersDialog({
  open,
  onOpenChange,
  group,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  group: GroupWithMembers;
}) {
  const accessToken = useAuthStore((state) => state.token);
  const { data: customers, isLoading } = useGetCustomersQuery(
    1,
    100,
    accessToken
  );

  // Filter out customers that are already in the group
  const availableCustomers =
    customers?.customers?.filter((customer) => customer.group === null) || [];

  // Define the form
  const form = useForm<{ customer_ids: string[] }>({
    defaultValues: {
      customer_ids: [],
    },
  });

  // Create mutation hook
  const createCustomerGroupMutation =
    useCreateCustomerGroupMutation(accessToken);

  const onSubmit = async (values: { customer_ids: string[] }) => {
    if (!values.customer_ids.length) return;

    try {
      // Use batch request instead of individual requests
      await createCustomerGroupMutation.mutateAsync({
        customer_ids: values.customer_ids,
        group_id: group.id,
      });

      toast({
        title: "Success",
        description: `Added ${values.customer_ids.length} member(s) to the group.`,
        variant: "default",
        duration: 3000,
      });

      onOpenChange(false);
      form.reset();
    } catch (error) {
      console.error("Error adding members to group:", error);
      toast({
        title: "Error",
        description: "Failed to add members to group, please try again.",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        if (!newOpen) form.reset();
        onOpenChange(newOpen);
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Members to {group.name}</DialogTitle>
          <p className="text-sm text-muted-foreground">
            Select customers to add to this group.
          </p>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4 py-4"
          >
            <FormField
              control={form.control}
              name="customer_ids"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customers</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      const currentValue = field.value || [];
                      if (currentValue.includes(value)) {
                        field.onChange(
                          currentValue.filter((id) => id !== value)
                        );
                      } else {
                        field.onChange([...currentValue, value]);
                      }
                    }}
                    value={field.value?.[0] || ""}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select customers" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {isLoading ? (
                        <SelectItem value="loading" disabled>
                          Loading customers...
                        </SelectItem>
                      ) : availableCustomers.length === 0 ? (
                        <SelectItem value="none" disabled>
                          No available customers
                        </SelectItem>
                      ) : (
                        availableCustomers.map((customer) => {
                          // Skip customers with no id
                          if (typeof customer.id !== "string") return null;

                          // Use a non-null assertion since we've checked it above
                          const userId = customer.id;

                          return (
                            <SelectItem
                              key={customer.id}
                              value={userId}
                              className="flex items-center gap-2 py-2"
                            >
                              <input
                                type="checkbox"
                                checked={field.value?.includes(userId)}
                                onChange={() => {
                                  const currentValue = field.value || [];
                                  if (currentValue.includes(userId)) {
                                    field.onChange(
                                      currentValue.filter((id) => id !== userId)
                                    );
                                  } else {
                                    field.onChange([...currentValue, userId]);
                                  }
                                }}
                                className="h-4 w-4 rounded-full border-gray-300 text-primary focus:ring-primary"
                              />
                              {customer.primary_contact_name} (
                              {customer.company_name || "No company"})
                            </SelectItem>
                          );
                        })
                      )}
                    </SelectContent>
                  </Select>
                  {field.value?.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-2">
                      {field.value.map((userId) => {
                        const customer = availableCustomers.find(
                          (c) => c.id === userId
                        );
                        return (
                          <div
                            key={userId}
                            className="flex items-center gap-1 bg-accent px-2 py-1 rounded-md"
                          >
                            <span className="text-sm">
                              {customer?.primary_contact_name}
                            </span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0"
                              onClick={() => {
                                field.onChange(
                                  field.value.filter((id) => id !== userId)
                                );
                              }}
                            >
                              <X className="h-3 w-3 text-red-500" />
                            </Button>
                          </div>
                        );
                      })}
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={createCustomerGroupMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={
                  createCustomerGroupMutation.isPending ||
                  !form.watch("customer_ids")?.length
                }
              >
                {createCustomerGroupMutation.isPending
                  ? "Adding..."
                  : "Add Members"}
                {createCustomerGroupMutation.isPending && (
                  <Loader2 className="h-4 w-4 ml-2 animate-spin" />
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
