# Product Management

## Overview
The product management system allows administrators to create, view, edit, and delete products. The system includes role-based permissions to ensure only authorized users can perform specific actions.

## Product Creation
Administrators with the appropriate permissions can create new products using the product creation form located at `/admin/products/new`. The form allows for comprehensive product information input, including:

- Basic product details
- Pricing information
- Inventory management
- Product categorization
- Product images and media

### Permission Requirements
- Users must have the `create:products` permission to access the product creation functionality
- Users without this permission will see an error message and a link to return to the products list

### Implementation Details
The product creation page is implemented in `pages/admin/products/new.tsx` and uses the following components:

- `ProductForm` from `@/components/features/admin/add-new-product`
- Permission checks via `checkUserHasPermission` from `@/middlewares/auth-middleware`
- User permissions stored in `useAuthStore`

## User Interface

### Successful Access
When a user with the appropriate permissions accesses the page, they will see:
- A product form with all necessary fields to create a new product

### Access Denied
When a user without the appropriate permissions accesses the page, they will see:
- A warning icon
- Text informing them they don't have permission
- A suggestion to contact their administrator
- A button to return to the products list

## Technical Implementation
The page checks for user permissions using the `checkUserHasPermission` function, which validates if the user has the required `create:products` permission. This permission is stored in the auth store and is typically set during user login based on their role.

The product form component encapsulates all the form functionality, including validation, submission handling, and error management. 
