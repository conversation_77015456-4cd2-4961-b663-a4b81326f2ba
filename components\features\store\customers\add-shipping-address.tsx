import { SvgSpinners90Ring } from "@/components/common/icons";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog-shadcn";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Button } from "@/components/ui/shadcn-button";
import { PhoneInput } from "@/components/ui/phone-input";
import { toast } from "@/hooks/use-toast";
import { useAddShippingAddressMutation } from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { CountrySelect, StateSelect, CitySelect } from "react-country-state-city";
import "react-country-state-city/dist/react-country-state-city.css";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { z } from "zod";
import { Country, State } from "react-country-state-city/dist/esm/types";
import { useLocationData } from "@/hooks/use-location-data";

// Define the schema for shipping address
const shippingAddressSchema = z.object({
  address: z.string().min(1, "Address is required"),
  address_2: z.string().optional(),
  address_type: z.enum(["residential", "commercial"], {
    required_error: "Address type is required",
  }),
  city: z.string().min(1, "City is required"),
  country: z.string().min(1, "Country is required"),
  state: z.string().min(1, "State is required"),
  zip_code: z.string().min(1, "Zip code is required"),
  contact_email: z.string().email("Invalid email address").optional().or(z.literal('')),
  contact_name: z.string().optional(),
  contact_number: z.string().optional().or(z.literal('')).refine(val => val === '' || /^[0-9+\-\(\)\s]*$/.test(val ?? ""), { message: "Phone number can only contain numbers, +, -, (, ), and spaces" }),
  option_name: z.string().optional(),
  default: z.boolean().default(false),
});

// Define the type from schema
type ShippingAddressFormValues = z.infer<typeof shippingAddressSchema>;

export function AddShippingAddressDialog() {
  const userData = useAuthStore((state) => state.data);
  const userId = userData.id;
  const addShippingAddressMutation = useAddShippingAddressMutation(userId);
  const [openShippingAddressDialog, setOpenShippingAddressDialog] = useState(false);

  const [hasStates, setHasStates] = useState(true);
  const [hasCities, setHasCities] = useState(true);

  // Get location data from our custom hook with United States as default
  const {
    countryId, stateId,
    selectedCountry, selectedState, selectedCity,
    updateCountry, updateState, updateCity,
    isLoading
  } = useLocationData({});

  const form = useForm<ShippingAddressFormValues>({
    resolver: zodResolver(shippingAddressSchema),
    defaultValues: {
      address: "",
      address_2: "",
      address_type: "residential",
      city: "",
      country: "United States",
      state: "",
      zip_code: "",
      default: false,
      contact_email: "",
      contact_name: "",
      contact_number: "",
      option_name: "",
    },
  });

  const addNewShippingAddress = (data: ShippingAddressFormValues) => {
    addShippingAddressMutation
      .mutateAsync(data)
      .then(() => {
        form.reset();
        form.clearErrors();

        toast({
          title: "Success",
          description: "Shipping address added successfully",
          duration: 3000,
          variant: "success",
        });
        toast({
          title: "Success",
          description: "Shipping address added successfully",
          duration: 3000,
          variant: "success",
        });

        setOpenShippingAddressDialog(false);
      })
      .catch(e => {
        console.log(e);
      });
  }

  const cancel = () => {
    form.reset();
    form.clearErrors();
    setOpenShippingAddressDialog(false);
  }

  const openShippingAddressDialogHandler = () => {
    setOpenShippingAddressDialog(true);
  }

  const submit = () => {
    form.handleSubmit(addNewShippingAddress)();
  }

  return <Dialog open={openShippingAddressDialog} onOpenChange={setOpenShippingAddressDialog}>
    <DialogTrigger asChild>
      <Button
        variant="outline"
        className="relative w-full h-full flex flex-col items-center justify-center gap-4 font-medium text-base min-h-[240px]"
        onClick={openShippingAddressDialogHandler}
      >
        <Plus size={48} strokeWidth={3} />
        <div>Add Shipping Address</div>
      </Button>
    </DialogTrigger>
    <DialogContent className="max-w-6xl w-full max-h-[90vh] overflow-y-auto">
      <Form {...form}>
        <DialogHeader>
          <DialogTitle>Add new Shipping address</DialogTitle>
          <DialogDescription>
            Add a new shipping address for your account.
          </DialogDescription>
        </DialogHeader>
        <div className="w-full py-4">
          <form className="space-y-6">
            <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="contact_name"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Contact Name</FormLabel>
                      <FormControl>
                        <Input placeholder="John Doe" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="contact_email"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Contact Email (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="contact_number"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Contact Number (Optional)</FormLabel>
                      <FormControl>
                        <PhoneInput
                          placeholder="Enter phone number (optional)"
                          defaultCountry="us"
                          value={field.value || ''}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="option_name"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Option Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Home, Office, etc." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="address_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select address type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="commercial">Commercial</SelectItem>
                          <SelectItem value="residential">Residential</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />
              </div>
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address Line 1</FormLabel>
                      <FormControl>
                        <Input placeholder="123 Main St" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="address_2"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address Line 2</FormLabel>
                      <FormControl>
                        <Input placeholder="Apartment, suite, unit, etc." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="country"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Country</FormLabel>
                      <FormControl>
                        {isLoading ? (
                          <div className="flex items-center h-10 px-3 border rounded-md">
                            <SvgSpinners90Ring className="w-4 h-4 mr-2" />
                            <span className="text-sm text-muted-foreground">Loading countries...</span>
                          </div>
                        ) : (
                          <CountrySelect
                            onChange={(e: Country) => {
                              field.onChange(e.name);
                              setHasStates(e.hasStates);
                              if (!e.hasStates) {
                                setHasCities(false);
                              } else {
                                setHasCities(true);
                              }
                              // Update country and reset dependent fields
                              updateCountry(e);
                              // Reset form values
                              form.setValue("state", "");
                              form.setValue("city", "");
                            }}
                            placeHolder="Select Country"
                            defaultValue={selectedCountry}
                          />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="state"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>State</FormLabel>
                        <FormControl>
                          {isLoading ? (
                            <div className="flex items-center h-10 px-3 border rounded-md">
                              <SvgSpinners90Ring className="w-4 h-4 mr-2" />
                              <span className="text-sm text-muted-foreground">Loading states...</span>
                            </div>
                          ) : (
                            <>
                              {
                                hasStates ? (
                                  <StateSelect
                                    countryid={countryId}
                                    onChange={(e: State) => {
                                      field.onChange(e.name);
                                      setHasCities(e.hasCities);
                                      // Update state and reset city
                                      updateState(e);
                                      // Reset city form value
                                      form.setValue("city", "");
                                    }}
                                    placeHolder="Select State"
                                    defaultValue={selectedState}
                                  />
                                ) : (
                                  <Input type="text" placeholder="Enter State" {...field} />
                                )
                              }
                            </>
                          )}
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>City</FormLabel>
                        <FormControl>
                          {isLoading ? (
                            <div className="flex items-center h-10 px-3 border rounded-md">
                              <SvgSpinners90Ring className="w-4 h-4 mr-2" />
                              <span className="text-sm text-muted-foreground">Loading cities...</span>
                            </div>
                          ) : (
                            <>
                              {
                                hasCities ? (
                                  <CitySelect
                                    countryid={countryId}
                                    stateid={stateId}
                                    onChange={(e) => {
                                      field.onChange(e.name);
                                      // Update city
                                      updateCity(e);
                                    }}
                                    placeHolder="Select City"
                                    defaultValue={selectedCity}
                                  />
                                ) : (
                                  <Input type="text" placeholder="Enter City" {...field} />
                                )
                              }
                            </>
                          )}
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={form.control}
                  name="zip_code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Zip Code</FormLabel>
                      <FormControl>
                        <Input placeholder="123456" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="default"
                  render={({ field }) => (
                    <FormItem className="pt-2">
                      <div className="flex items-center gap-x-2">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel className="font-normal">Set as default shipping address</FormLabel>
                      </div>
                      <FormDescription className="pl-6">
                        This will be used as your default shipping address for orders.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {
              addShippingAddressMutation.isError ? (
                <div className="w-full">
                  <p className="text-red-500">{addShippingAddressMutation.error.message}</p>
                </div>
              ) : null
            }

            <DialogFooter className="pt-4">
              <DialogClose asChild>
                <Button
                  onClick={cancel}
                  variant="destructive"
                  type="button"
                >
                  Cancel
                </Button>
              </DialogClose>
              <Button
                type="button"
                disabled={addShippingAddressMutation.isPending}
                onClick={submit}
              >
                Add Shipping Address
                {addShippingAddressMutation.isPending && <SvgSpinners90Ring className="w-4 h-4 ml-2" />}
              </Button>
            </DialogFooter>
          </form>
        </div>
      </Form>
    </DialogContent>
  </Dialog>
}
