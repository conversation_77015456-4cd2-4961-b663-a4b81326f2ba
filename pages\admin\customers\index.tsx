import AdminLayout from "@/components/features/admin/layout";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { CheckboxGroup, CheckboxItem } from "@/components/ui/checkbox-group";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  DataTable,
  DataTableFilter,
  DataTableSkeleton,
  DataTableToolbar,
} from "@/components/ui/data-table";
import { DataTableColumnHeader } from "@/components/ui/data-table-column-header";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog-shadcn";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/shadcn-button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  TableBody,
  TableCell,
  Table as TableComponent,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from "@/hooks/use-toast";
import { PublicUserWithCustomer } from "@/pages/api/users/index";
import {
  useAddCustomerCategoryMutation,
  useCreateCustomerGroupMutation,
  useDeleteCustomerCategoryMutation,
  useGetAllCategoriesQuery,
  useGetAllUsersQuery,
  useGetCustomerCategoriesQuery,
  useGetGroupsQuery,
  useSearchUsersQuery,
  useUpdateUserNotesMutation,
} from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { Group, UserStatus } from "@/supabase/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQueryClient } from "@tanstack/react-query";
import {
  ColumnDef,
  ColumnFiltersState,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  Row,
  SortingState,
  Table,
  useReactTable,
} from "@tanstack/react-table";
import {
  Folder,
  Loader2,
  MoreHorizontal,
  PlusSquare,
  UserPlus,
  User,
  StickyNote,
} from "lucide-react";
import Link from "next/link";
import React, { useEffect, useMemo, useState, useRef } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { checkUserHasPermission } from "@/middlewares/auth-middleware";
import Head from "next/head";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { SvgSpinners90Ring } from "@/components/common/icons";
import { Input } from "@/components/ui/input";

// Add these type definitions
interface BusinessDetails {
  id: string;
  user_id: string;
  website?: string;
  buyer_name?: string;
  created_at: string;
  [key: string]: any;
}

// Extend the existing types to include business_details
declare module "@/pages/api/users/index" {
  interface PublicUserWithCustomer {
    business_details?: BusinessDetails | null;
    notes?: string | null;
  }

  interface CustomerWithGroup {
    categories?: Array<{
      id: string;
      category_id: string;
      category_data?: {
        id: string;
        name: string;
      };
    }>;
    billing_addresses?: Array<{
      address?: string;
      city?: string;
      state?: string;
    }>;
  }
}

const columns: ColumnDef<PublicUserWithCustomer>[] = [
  {
    header: "Company Name",
    accessorKey: "companyName",
    cell: ({ row }) => {
      const customer = row.original?.customer_data?.[0];
      return (
        <div className="flex items-center gap-2">
          {customer?.company_name?.trim() ? customer?.company_name : "N/A"}
        </div>
      );
    },
  },
  {
    id: "name",
    header: "Name",
    accessorFn: (row) => `${row.first_name} ${row.last_name}`, // Custom accessor function
    cell: ({ row }) => {
      const customer = row.original;

      return (
        <div className="flex items-center gap-2">
          <Avatar>
            <AvatarFallback>{customer.first_name?.charAt(0)}</AvatarFallback>
          </Avatar>
          {customer.first_name} {customer.last_name}
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const customer = row.original;
      return <CustomerStatusBadge status={customer.status} />;
    },
    enableSorting: true,
  },
  {
    header: "Email",
    accessorKey: "email",
    cell: ({ row }) => {
      const customer = row.original;
      return <div className="flex items-center gap-2">{customer.email}</div>;
    },
  },
  {
    header: "Address",
    accessorKey: "address",
    cell: ({ row }) => {
      const customer = row.original;
      return (
        <div className="flex items-center gap-2">
          {customer.customer_data?.[0]?.billing_addresses?.[0]?.address}
        </div>
      );
    },
  },
  {
    header: "City/State",
    accessorKey: "city",
    cell: ({ row }) => {
      const address = row.original.customer_data?.[0]?.billing_addresses?.[0];
      return (
        <div className="flex items-center gap-2">
          {address?.city} / {address?.state}
        </div>
      );
    },
  },
  {
    header: "Contact Info",
    accessorKey: "contactInfo",
    cell: ({ row }) => {
      const phone = row.original.customer_data?.[0]?.phone?.trim();
      const customerNumber =
        row.original.customer_data?.[0]?.customer_number?.trim();

      return (
        <div className="flex items-center gap-2">
          {customerNumber ? customerNumber : phone ? phone : "N/A"}
        </div>
      );
    },
  },
  {
    header: "Group",
    accessorKey: "customer_data",
    cell: ({ row }) => {
      const customer = row.original;
      const firstCustomerData = customer.customer_data?.[0];
      // Access the first group from the groups array
      const firstGroup = firstCustomerData?.group_data?.name;
      return (
        <div className="flex items-center gap-2">{firstGroup || "None"}</div>
      );
    },
    filterFn: (row, _columnId, filterValue) => {
      if (!filterValue) return true;

      const customerData = row.original.customer_data;
      if (!customerData || customerData.length === 0) return false;

      // Check if any customer data entry has a group_data with matching group ID
      return customerData.some((item) => {
        // If group_data is null or not defined, it's not a match
        if (!item?.group_data) return false;

        // Access the group ID correctly from the nested data structure
        const groupId = item.group_data?.id;
        return groupId === filterValue;
      });
    },
  },
  {
    header: "Notes",
    accessorKey: "notes",
    cell: ({ row }) => {
      const customer = row.original;
      return (
        <div className="flex items-center gap-2">
          {customer.notes
            ? customer.notes.length > 20
              ? `${customer.notes.slice(0, 20)}...`
              : customer.notes
            : "N/A"}
        </div>
      );
    },
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created At" />
    ),
    cell: ({ row }) => {
      const createdAt = row.getValue("created_at") as string;
      const date = new Date(createdAt);
      return (
        <div>
          {date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
          })}
        </div>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: "updated_at",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Updated At" />
    ),
    cell: ({ row }) => {
      const createdAt = row.getValue("updated_at") as string;
      const date = new Date(createdAt);
      return (
        <div>
          {date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
          })}
        </div>
      );
    },
    enableSorting: true,
  },

  {
    header: "Actions",
    accessorKey: "actions",
    cell: UserTableActions,
  },
];

function CustomerSearchFilter({
  onSearch,
}: {
  onSearch: (value: string) => void;
}) {
  const [searchValue, setSearchValue] = useState("");
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);

    // Debounce the search callback to avoid rapid pagination changes
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      onSearch(value);
    }, 300);
  };

  // Clean up debounce timer
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return (
    <Input
      placeholder="Search customers..."
      value={searchValue}
      onChange={handleSearchChange}
      className="max-w-md rounded-full"
    />
  );
}

export default function AdminCustomers() {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchQuery, setSearchQuery] = useState("");
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "status",
      desc: false, // false means ascending, so "approved" will come before "pending"
    },
  ]);
  const [rowSelection, setRowSelection] = useState({});

  const token = useAuthStore((state) => state.token);

  // Regular query for all users when not searching
  const {
    data: regularData,
    isLoading: isRegularLoading,
    isError: isRegularError,
  } = useGetAllUsersQuery(page, pageSize, token);

  const { data: groupsData, isLoading: isGroupsLoading } = useGetGroupsQuery(
    1,
    100,
    token
  );

  const {
    data: searchData,
    isLoading: isSearchLoading,
    isError: isSearchError,
  } = useSearchUsersQuery(searchQuery, page, pageSize, token);

  const data = searchQuery ? searchData : regularData;
  const isLoading = searchQuery ? isSearchLoading : isRegularLoading;
  const isError = searchQuery ? isSearchError : isRegularError;

  const customers = useMemo(() => {
    return data?.data ?? [];
  }, [data]);

  const totalItems = data?.total ?? 0;
  const totalPages = Math.max(1, Math.ceil(totalItems / pageSize));

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);

    if (page !== 1) {
      setPage(1);
    }
  };

  // Create a custom filter function to search name and email
  function filterCustomerData(
    row: Row<PublicUserWithCustomer>,
    columnId: string,
    value: string
  ) {
    const searchValue = value.toLowerCase();

    if (columnId === "name") {
      const name = row.getValue(columnId) as string;
      return name.toLowerCase().includes(searchValue);
    }

    if (columnId === "email") {
      const email = String(row.original.email || "").toLowerCase();
      return email.includes(searchValue);
    }

    const cellValue = row.getValue(columnId);
    return cellValue !== undefined
      ? String(cellValue).toLowerCase().includes(searchValue)
      : false;
  }

  const table = useReactTable({
    data: customers,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onGlobalFilterChange: setGlobalFilter,
    onColumnFiltersChange: setColumnFilters,
    onSortingChange: setSorting,
    globalFilterFn: filterCustomerData,
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const newPagination = updater({
          pageIndex: page - 1,
          pageSize,
        });
        setPage(newPagination.pageIndex + 1);
        setPageSize(newPagination.pageSize);
      } else {
        setPage(updater.pageIndex + 1);
        setPageSize(updater.pageSize);
      }
    },
    state: {
      sorting,
      rowSelection,
      columnFilters,
      pagination: {
        pageIndex: page - 1,
        pageSize,
      },
    },
    manualPagination: true,
    pageCount: totalPages,
  });

  return (
    <AdminLayout>
      <Head>
        <title>Customers</title>
      </Head>
      <div className="space-y-4">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h2 className="text-4xl font-bold tracking-tight">Customers</h2>
            <p className="text-zinc-500 text-sm">
              You can manage your customers here.
            </p>
          </div>
          <Button variant="outline" className="w-full md:w-auto" asChild>
            <Link href="/admin/customers/pending">
              Manage Pending Customers
            </Link>
          </Button>
        </div>
        <CustomerSearchFilter onSearch={handleSearchChange} />
        {isLoading ? (
          <DataTableSkeleton />
        ) : (
          <DataTable
            data={customers}
            columns={columns}
            table={table}
            renderToolbar={(table) => (
              <DataTableToolbar table={table}>
                <div className="flex gap-2">
                  <CustomerStatusFilter table={table} />
                  {!isGroupsLoading && groupsData?.groups && (
                    <CustomerGroupFilter
                      table={table}
                      groups={groupsData?.groups ?? []}
                    />
                  )}
                </div>
              </DataTableToolbar>
            )}
          />
        )}
        <div className="flex items-center justify-end py-4">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage((prev) => Math.max(1, prev - 1))}
              disabled={page === 1}
            >
              Previous
            </Button>
            <div className="text-sm">
              Page {page} of {totalPages}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage((prev) => Math.min(totalPages, prev + 1))}
              disabled={page === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}

export function CustomerStatusBadge({ status }: { status: UserStatus }) {
  const variant =
    status === "approved"
      ? "outline"
      : status === "pending"
        ? "pending"
        : "destructive";

  return (
    <Badge variant={variant} className="uppercase">
      {status}
    </Badge>
  );
}

function UserTableActions({ row }: { row: Row<PublicUserWithCustomer> }) {
  const user = row.original;
  const isPendingUser = row.original.status === "pending";
  const isApprovedUser = row.original.status === "approved";

  const token = useAuthStore((state) => state.token);
  const { data: groups, isLoading: isGroupsLoading } = useGetGroupsQuery(
    1,
    100,
    token
  );

  return (
    <div className="flex items-center gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuItem
            onClick={() => navigator.clipboard.writeText(user.id)}
          >
            Copy user ID
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          {isApprovedUser ? (
            <>
              <ViewUserDetails user={user} />
              <AddUserToGroupDialog user={user} groups={groups?.groups ?? []} />
              <ViewCustomerCategoriesDialog user={user} />
              <AddCustomerCategoryDialog user={user} />
              <AddUserNotes user={user} />
            </>
          ) : null}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

function AddUserNotes({ user }: { user: PublicUserWithCustomer }) {
  const [open, setOpen] = useState(false);
  const [notes, setNotes] = useState(user.notes || "");

  const token = useAuthStore((state) => state.token);
  const updateNotesMutation = useUpdateUserNotesMutation(user.id, token);

  useEffect(() => {
    if (open) {
      setNotes(user.notes || "");
    }
  }, [open, user.notes]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    updateNotesMutation.mutate(
      { notes },
      {
        onSuccess: () => {
          toast({
            title: "Success",
            description: "User notes updated successfully",
            variant: "success",
          });
          setOpen(false);
        },
        onError: (error) => {
          toast({
            title: "Error",
            description: error.toString(),
            variant: "destructive",
          });
        },
      }
    );
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
          <StickyNote className="mr-2 h-4 w-4" />
          <span>{user.notes ? "Update" : "Add"} User Notes</span>
        </DropdownMenuItem>
      </DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>User Notes</DialogTitle>
          <DialogDescription>
            {user.notes ? "Update" : "Add"} notes for {user.first_name}{" "}
            {user.last_name}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                className="min-h-[200px]"
                placeholder="Enter notes about this user..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={updateNotesMutation.isPending}>
              {updateNotesMutation.isPending ? (
                <>
                  <SvgSpinners90Ring className="mr-2 h-4 w-4" />
                  Saving...
                </>
              ) : (
                "Save Notes"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

function ViewUserDetails({ user }: { user: PublicUserWithCustomer }) {
  const [open, setOpen] = useState(false);

  const customerData = user.customer_data?.[0];

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
          <User className="mr-2 h-4 w-4" />
          <span>View Details</span>
        </DropdownMenuItem>
      </DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>User Details</DialogTitle>
          <DialogDescription>
            Comprehensive information about {user.first_name} {user.last_name}
          </DialogDescription>
        </DialogHeader>

        <div className="gap-6 py-4">
          {/* Personal Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Personal Information</h3>
            <div className="grid grid-cols-2 gap-2">
              <div className="text-sm font-medium">ID</div>
              <div className="text-sm text-gray-500">{user.id}</div>

              <div className="text-sm font-medium">Name</div>
              <div className="text-sm text-gray-500">
                {user.first_name} {user.last_name}
              </div>

              <div className="text-sm font-medium">Email</div>
              <div className="text-sm text-gray-500">{user.email}</div>

              <div className="text-sm font-medium">Role</div>
              <div className="text-sm text-gray-500">{user.role}</div>

              <div className="text-sm font-medium">Status</div>
              <div className="text-sm text-gray-500">
                <CustomerStatusBadge status={user.status} />
              </div>
              <div className="text-sm font-medium">Created At</div>
              <div className="text-sm text-gray-500">
                {new Date(user.created_at).toLocaleDateString()}
              </div>
              <div className="text-sm font-medium">Notes</div>
              <div className="text-sm text-gray-500">{user.notes}</div>
            </div>
          </div>
        </div>

        {/* Business Details */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Business Details</h3>
          {user?.business_details ? (
            <div className="grid grid-cols-2 gap-2">
              <div className="text-sm font-medium">Website</div>
              <div className="text-sm text-gray-500">
                {user?.business_details?.website ? (
                  <a
                    href={user?.business_details?.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    {user?.business_details?.website}
                  </a>
                ) : (
                  "N/A"
                )}
              </div>

              <div className="text-sm font-medium">Buyer Name</div>
              <div className="text-sm text-gray-500">
                {user?.business_details?.buyer_name || "N/A"}
              </div>

              {Object.entries(user?.business_details)
                .filter(
                  ([key]) =>
                    ![
                      "id",
                      "user_id",
                      "created_at",
                      "website",
                      "buyer_name",
                    ].includes(key)
                )
                .map(([key, value]) => (
                  <React.Fragment key={key}>
                    <div className="text-sm font-medium">
                      {key
                        .split("_")
                        .map(
                          (word) => word.charAt(0).toUpperCase() + word.slice(1)
                        )
                        .join(" ")}
                    </div>
                    <div className="text-sm text-gray-500">
                      {String(value || "N/A")}
                    </div>
                  </React.Fragment>
                ))}
            </div>
          ) : (
            <div className="text-sm text-gray-500">
              No business details available
            </div>
          )}
        </div>

        {/* Customer Information */}
        {customerData && (
          <div className="space-y-4 border-t pt-4">
            <h3 className="text-lg font-semibold">Customer Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="grid grid-cols-2 gap-2">
                <div className="text-sm font-medium">Company Name</div>
                <div className="text-sm text-gray-500">
                  {customerData.company_name || "N/A"}
                </div>

                <div className="text-sm font-medium">Contact Name</div>
                <div className="text-sm text-gray-500">
                  {customerData.primary_contact_name || "N/A"}
                </div>

                <div className="text-sm font-medium">Phone</div>
                <div className="text-sm text-gray-500">
                  {customerData.phone || "N/A"}
                </div>

                <div className="text-sm font-medium">Customer Number</div>
                <div className="text-sm text-gray-500">
                  {customerData.customer_number || "N/A"}
                </div>

                <div className="text-sm font-medium">Credit Limit</div>
                <div className="text-sm text-gray-500">
                  {customerData.credit_limit !== null
                    ? `$${customerData.credit_limit.toFixed(2)}`
                    : "N/A"}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div className="text-sm font-medium">Company Website</div>
                <div className="text-sm text-gray-500">
                  {customerData.company_website ? (
                    <a
                      href={customerData.company_website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {customerData.company_website}
                    </a>
                  ) : (
                    "N/A"
                  )}
                </div>

                <div className="text-sm font-medium">Customer Role</div>
                <div className="text-sm text-gray-500">
                  {customerData.role || "N/A"}
                </div>

                <div className="text-sm font-medium">Status</div>
                <div className="text-sm text-gray-500">
                  <CustomerStatusBadge status={customerData.status} />
                </div>

                <div className="text-sm font-medium">Group</div>
                <div className="text-sm text-gray-500">
                  {customerData.group_data?.name || "None"}
                </div>

                {/* <div className="text-sm font-medium">Categories</div>
                <div className="text-sm text-gray-500 flex flex-wrap gap-1">
                  {customerData.categories && customerData.categories.length > 0
                    ? customerData.categories.map((category) => (
                        <Badge
                          key={category.id}
                          variant="outline"
                          className="mr-1"
                        >
                          {category.category_data?.name || "Unknown"}
                        </Badge>
                      ))
                    : "None"}
                </div> */}
              </div>
            </div>

            {/* Shipping Notes */}
            {customerData.shipping_notes && (
              <div className="mt-4">
                <div className="text-sm font-medium">Shipping Notes</div>
                <div className="text-sm text-gray-500 mt-1 p-2 border rounded bg-gray-50">
                  {customerData.shipping_notes}
                </div>
              </div>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

function AddUserToGroupDialog({
  user,
  groups,
}: {
  user: PublicUserWithCustomer;
  groups: Group[];
}) {
  const [open, setOpen] = useState(false);
  const token = useAuthStore((state) => state.token);

  // Get current user group
  const firstCustomerData = user.customer_data?.[0];
  const firstGroup = firstCustomerData?.group_data?.name;
  const currentGroupName = firstGroup || "None";
  const hasGroup = !!firstGroup;

  const formSchema = z.object({
    group_id: z.string().min(1, "Please select a group"),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      group_id: "",
    },
  });

  // Reset form when dialog opens/closes
  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset();
    }
    setOpen(newOpen);
  };

  const groupId = form.watch("group_id");
  const createCustomerGroupMutation = useCreateCustomerGroupMutation(token);

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    if (!groupId) {
      toast({
        title: "Error",
        description: "Please select a group",
        variant: "destructive",
      });
      return;
    }

    const values = {
      customer_ids: [firstCustomerData?.id ?? user.id ?? ""],
      group_id: data.group_id,
    };

    createCustomerGroupMutation.mutate(values);
  };

  useEffect(
    function showToast() {
      if (createCustomerGroupMutation.isSuccess) {
        toast({
          title: "Success",
          description: "User added to group successfully",
          variant: "default",
          duration: 2000,
        });
        setOpen(false);
        return;
      }

      if (createCustomerGroupMutation.isError) {
        toast({
          title: "Error",
          description:
            createCustomerGroupMutation.error?.message ??
            "Failed to add user to group",
          variant: "destructive",
          duration: 2000,
        });
        setOpen(false);
      }
    },
    [createCustomerGroupMutation.isSuccess, createCustomerGroupMutation.isError]
  );

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
          <UserPlus className="mr-2 h-4 w-4" />
          <span>{hasGroup ? "Edit user group" : "Add to group"}</span>
        </DropdownMenuItem>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {hasGroup ? "Edit User Group" : "Add User to Group"}
          </DialogTitle>
        </DialogHeader>
        <DialogDescription>
          Select a group for {user.first_name} {user.last_name}.
        </DialogDescription>

        {hasGroup && (
          <div className="py-2 text-sm">
            <p className="font-medium">
              This user is currently in the{" "}
              <Badge variant="outline">{currentGroupName}</Badge> group
            </p>
          </div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="group_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Group</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={createCustomerGroupMutation.isPending}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select group" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {groups.map((group) => (
                        <SelectItem key={group.id} value={group.id}>
                          {group.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setOpen(false)}
                type="button"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createCustomerGroupMutation.isPending}
              >
                {createCustomerGroupMutation.isPending
                  ? "Adding..."
                  : "Add User to Group"}
                {createCustomerGroupMutation.isPending ? (
                  <Loader2 className="h-4 w-4 ml-2 animate-spin" />
                ) : null}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function CustomerStatusFilter({
  table,
}: {
  table: Table<PublicUserWithCustomer>;
}) {
  // Only show approved and pending in the filter
  const statusOptions: UserStatus[] = ["pending", "approved"];

  const handleStatusChange = (status: string) => {
    if (status === "all") {
      table.getColumn("status")?.setFilterValue(undefined);
    } else {
      table.getColumn("status")?.setFilterValue(status);
    }
  };

  return (
    <Select onValueChange={handleStatusChange} defaultValue="all">
      <SelectTrigger className="uppercase rounded-full min-w-40">
        <SelectValue placeholder="Filter by status" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Statuses</SelectItem>
        {statusOptions.map((status) => (
          <SelectItem key={status} value={status} className="capitalize">
            <CustomerStatusBadge status={status as UserStatus} />
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

function CustomerGroupFilter({
  table,
  groups,
}: {
  table: Table<PublicUserWithCustomer>;
  groups: Group[];
}) {
  const handleGroupChange = (groupId: string) => {
    if (groupId === "all") {
      table.getColumn("customer_data")?.setFilterValue(undefined);
    } else {
      table.getColumn("customer_data")?.setFilterValue(groupId);
    }
  };

  return (
    <Select onValueChange={handleGroupChange} defaultValue="all">
      <SelectTrigger className="w-full rounded-full min-w-40 uppercase">
        <SelectValue placeholder="Filter by group" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Groups</SelectItem>
        {groups?.map((group) => (
          <SelectItem key={group.id} value={group.id}>
            {group.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

function ViewCustomerCategoriesDialog({
  user,
}: {
  user: PublicUserWithCustomer;
}) {
  const [open, setOpen] = useState(false);
  const token = useAuthStore((state) => state.token);

  // Get customer ID from the first customer_data entry
  const customerId = user.customer_data?.[0]?.id;

  const {
    data: customerCategories,
    isLoading,
    error,
    refetch,
  } = useGetCustomerCategoriesQuery(customerId ?? "", token);

  const deleteCustomerCategoryMutation =
    useDeleteCustomerCategoryMutation(token);
  const permissions = useAuthStore((state) => state.permissions);
  const hasPermission = checkUserHasPermission(
    permissions,
    "delete:customer_categories"
  );

  const handleDelete = async (categoryId: string) => {
    if (!customerId) return;

    if (!hasPermission) {
      toast({
        title: "Error",
        description: "You are not authorized to remove customer categories",
        variant: "destructive",
        duration: 3000,
      });
      return;
    }

    try {
      await deleteCustomerCategoryMutation.mutateAsync({
        customerId,
        categoryId,
      });

      toast({
        title: "Success",
        description: "Category removed successfully",
        variant: "default",
        duration: 2000,
      });

      refetch();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to remove category",
        variant: "destructive",
        duration: 2000,
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
          <Folder className="mr-2 h-4 w-4" />
          <span>View assigned categories</span>
        </DropdownMenuItem>
      </DialogTrigger>
      <DialogContent className="sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle>Customer Categories</DialogTitle>
          <DialogDescription>
            Categories assigned to {user.first_name} {user.last_name}
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center py-4">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : error ? (
          <div className="text-red-500 py-2">
            Error loading categories: {(error as Error).message}
          </div>
        ) : customerCategories && customerCategories.length > 0 ? (
          <ScrollArea className="h-[300px]">
            <TableComponent>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50%]">Name</TableHead>
                  <TableHead className="w-[30%]">Value</TableHead>
                  <TableHead className="text-right w-[20%]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {customerCategories.map((item: any) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">
                      {item.category?.name || "N/A"}
                    </TableCell>
                    <TableCell>
                      {item.category?.value ? (
                        <Badge
                          variant="outline"
                          className="font-mono text-xs bg-muted text-muted-foreground"
                        >
                          {item.category.value}
                        </Badge>
                      ) : (
                        "N/A"
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(item.category_id)}
                        disabled={
                          deleteCustomerCategoryMutation.isPending ||
                          !hasPermission
                        }
                      >
                        Remove
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </TableComponent>
          </ScrollArea>
        ) : (
          <div className="py-4 text-center text-muted-foreground">
            No categories assigned to this customer.
          </div>
        )}

        <DialogFooter className="gap-2">
          <Button type="button" onClick={() => setOpen(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

function AddCustomerCategoryDialog({ user }: { user: PublicUserWithCustomer }) {
  const [open, setOpen] = useState(false);
  const token = useAuthStore((state) => state.token);
  const queryClient = useQueryClient();

  // Get customer ID from the first customer_data entry
  const customerId = user.customer_data?.[0]?.id;

  // Get all available categories
  const { data: categoriesData, isLoading: isCategoriesLoading } =
    useGetAllCategoriesQuery(1, 100, token);

  // Get already assigned categories to exclude them
  const {
    data: customerCategories,
    isLoading: isCustomerCategoriesLoading,
    refetch,
  } = useGetCustomerCategoriesQuery(customerId ?? "", token);

  const hasAllCategories = customerCategories?.length === categoriesData?.categories?.length;
  const hasSupportCategories = !hasAllCategories;

  const addCustomerCategoryMutation = useAddCustomerCategoryMutation(token);

  const formSchema = z.object({
    category_type: z.enum(["all_products", "support_products"], {
      required_error: "Please select a category type",
    }),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      category_type: undefined,
    },
  });

  if (hasAllCategories) {
    form.setValue("category_type", "all_products");
  }

  if (hasSupportCategories) {
    form.setValue("category_type", "support_products");
  }

  // Reset form when dialog opens/closes
  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset();
    }
    setOpen(newOpen);
  };

  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    if (!customerId) return;

    try {
      await addCustomerCategoryMutation.mutateAsync({
        customerId,
        categoryType: data.category_type,
      });

      toast({
        title: "Success",
        description: "Categories updated successfully",
        variant: "default",
        duration: 2000,
      });

      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({
        queryKey: ["customer-categories", customerId],
      });

      setOpen(false);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update categories",
        variant: "destructive",
        duration: 2000,
      });
    }
  };

  const isLoading =
    isCategoriesLoading ||
    isCustomerCategoriesLoading ||
    addCustomerCategoryMutation.isPending;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
          <PlusSquare className="mr-2 h-4 w-4" />
          <span>Update Categories</span>
        </DropdownMenuItem>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add Categories</DialogTitle>
          <DialogDescription>
            Select category type to assign to {user.first_name} {user.last_name}.
            <br />
            <div className="text-sm mt-2 flex flex-col gap-2">
              <div>
                <strong>All Products:</strong> Assigns all available categories
              </div>
              <div>
                <strong>Support Products:</strong> Assigns all categories except Valves and its subcategories
              </div>
            </div>
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {isLoading ? (
              <div className="flex justify-center py-4">
                <p className="text-sm text-zinc-700">
                  This might take a few seconds. Please wait...
                </p>
              </div>
            ) : (
              <FormField
                control={form.control}
                name="category_type"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>Category Type</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="space-y-3"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="all_products" id="all_products" />
                          <Label htmlFor="all_products" className="font-medium">
                            All Products
                          </Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="support_products" id="support_products" />
                          <Label htmlFor="support_products" className="font-medium">
                            Support Products
                          </Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter className="gap-2">
              <Button
                variant="outline"
                onClick={() => setOpen(false)}
                type="button"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
              >
                {
                  isLoading ? 'Updating...' : 'Update Categories'
                }
                {isLoading && <Loader2 className="h-4 w-4 ml-2 animate-spin" />}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
