import { supabaseClient } from "@/supabase";
import { Notification } from "@/supabase/types";
import { create } from "zustand";

export interface NotificationStore {
    notifications: Notification[];
    /** Subscribes to supabase notifications channel
    * and returns unsubscribe function
    * to dispose the created subscription
    */
    subscribe: (userId: string) =>
        () => void;
    addNotification: (notification: Notification) => void;
    removeNotification: (notificationId: string) => void;
}

export const useNotificationsStore = create<NotificationStore>((set) => ({
    notifications: [],
    subscribe: (userId: string) => {
        const subscription = supabaseClient.channel("notifications")
            .on("postgres_changes", { event: "INSERT", schema: "public", table: "notifications" },
                function cb(payload) {
                    const newNotification = payload.new as Notification;

                    if (newNotification.user_id !== userId) return;

                    set((state) => ({
                        notifications: [...state.notifications, newNotification],
                    }));
                })
            .subscribe();

        return () => {
            subscription.unsubscribe();
            supabaseClient.removeChannel(subscription);
        }
    },
    addNotification: (notification) =>
        set((state) => {
            const hasNotification = state.notifications.find(n => n.id === notification.id);
            if (hasNotification) return state;

            return {
                notifications: [...state.notifications, notification],
            }
        }),
    removeNotification: (notificationId) =>
        set((state) => ({
            notifications: state.notifications.filter(
                (notification) => notification.id !== notificationId
            ),
        })),
}));