import { Button } from "@/components/ui/shadcn-button";
import { cn } from "@/lib/utils";
import useAuthStore from "@/stores/auth-store";
import {
  BarChart3,
  BellDot,
  ChartNoAxesColumnIncreasing,
  FileTextIcon,
  Group,
  HomeIcon,
  Menu,
  PackageSearch,
  Receipt,
  SettingsIcon,
  SquareStack,
  Store,
  Users2,
  UsersIcon,
  X
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { CartContent } from "./customers/cart";
import { NavUser } from "./nav-user";

interface Nav {
  title: string;
  link?: string;
  icon?: React.ComponentType<{ className?: string }>;
  children?: Nav[];
  badge?: string | number;
}

interface NavData {
  admin?: {
    nav: Nav[];
  },
  manager?: {
    nav: Nav[];
  };
}

export const navData: NavData = {
  manager: {
    nav: [
      {
        title: "Dashboard",
        link: "/store/dashboard",
        icon: HomeIcon
      },
      {
        title: "My Orders",
        link: "/store/orders",
        icon: ChartNoAxesColumnIncreasing
      },
      {
        title: "My Account",
        link: "/store/account",
        icon: SettingsIcon
      },
    ]
  },
  admin: {
    nav: [
      {
        title: "Home",
        link: "/admin/dashboard",
        icon: HomeIcon
      },
      {
        title: "Sales",
        icon: Receipt,
        children: [
          {
            title: "All Orders",
            link: "/admin/orders",
            icon: FileTextIcon
          },
          {
            title: "Cancellation Requests",
            link: "/admin/orders/cancellations",
            icon: BellDot
          }
        ]
      },
      {
        title: "Customers",
        icon: UsersIcon,
        children: [
          {
            title: "All Customers",
            link: "/admin/customers",
            icon: Users2
          },
          {
            title: "Pending Users",
            link: "/admin/customers/pending",
            icon: BellDot,
          },
          {
            title: "Pending Billing",
            link: "/admin/customers/pending-billing",
            icon: BellDot,
          },
          {
            title: "Groups",
            link: "/admin/customers/groups",
            icon: Group
          }
        ]
      },
      {
        title: "Products",
        icon: PackageSearch,
        children: [
          {
            title: "All Products",
            link: "/admin/products",
            icon: PackageSearch
          },
          {
            title: "Categories",
            link: "/admin/products/categories",
            icon: SquareStack
          }
        ]
      },
      {
        title: "Reports",
        link: "/admin/reports",
        icon: BarChart3
      },
      // TODO: show settings when we have them
      // {
      //   title: "Settings",
      //   icon: SettingsIcon,
      //   children: [
      //     {
      //       title: "Store",
      //       link: "/admin/settings/store",
      //       icon: Store
      //     },
      //     {
      //       title: "Users",
      //       link: "/admin/settings/users",
      //       icon: Users2
      //     }
      //   ]
      // }
    ]
  }
};

export default function StoreNav() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const userData = useAuthStore((state) => state.data);
  const role = useAuthStore((state) => state.getRole)();
  const currentNav = navData.manager?.nav || [];

  return (
    <div className="sticky top-0 z-50 w-full border-b bg-transparent">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo and mobile menu toggle */}
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden mr-2"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
            <Link href="/store/dashboard" className="flex items-center">
              <h1 className="text-xl font-bold">Maxton</h1>
            </Link>
          </div>

          {/* Desktop navigation */}
          <nav className="hidden md:flex items-center space-x-4">
            {currentNav.map((item) => (
              <Link
                key={item.title}
                href={item.link || "#"}
                className="flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-accent hover:text-accent-foreground"
              >
                {item.icon && <item.icon className="mr-2 h-4 w-4 text-primary" />}
                <span>{item.title}</span>
              </Link>
            ))}
          </nav>

          {/* User and cart */}
          <div className="flex items-center space-x-4">
            <CartContent />
            <div className="hidden md:block">
              <NavUser user={{
                name: userData?.email?.split('@')[0] || "Customer",
                email: userData?.email || "",
                avatar: ""
              }} />
            </div>
          </div>
        </div>
      </div>

      {/* Mobile navigation */}
      <div className={cn(
        "fixed inset-0 top-16 z-50 w-full bg-background md:hidden transform transition-transform duration-200 ease-in-out",
        mobileMenuOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="container mx-auto p-4">
          <nav className="flex flex-col space-y-2">
            {currentNav.map((item) => (
              <Link
                key={item.title}
                href={item.link || "#"}
                className="flex items-center px-3 py-4 text-base font-medium rounded-md hover:bg-accent hover:text-accent-foreground"
                onClick={() => setMobileMenuOpen(false)}
              >
                {item.icon && <item.icon className="mr-3 h-5 w-5 text-primary" />}
                <span>{item.title}</span>
              </Link>
            ))}
            <div className="md:hidden pt-4 border-t mt-4">
              <NavUser user={{
                name: userData?.email?.split('@')[0] || "Customer",
                email: userData?.email || "",
                avatar: ""
              }} />
            </div>
          </nav>
        </div>
      </div>
    </div>
  );
}
