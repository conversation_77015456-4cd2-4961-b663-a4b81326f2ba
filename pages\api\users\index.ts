import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient, supabaseClient } from "@/supabase/index";
import { Customer, Group, PublicUser } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";
import { createMailer, createMailerOptions } from "@/lib/mailer";
import { sendEmail } from "@/lib/mailer/sender";
import { createRegistrationConfirmationTemplate } from "@/lib/mailer/templates";

export default matchRoute({
  GET: checkAdmin(getAllUsersHandler),
  POST: createNewUserHandler,
});

const createPublicUserSchema = z.object({
  user_details: z.object({
    first_name: z.string().min(1, { message: "First name is required" }),
    last_name: z.string().min(1, { message: "Last name is required" }),
    email: z.string().email({ message: "Invalid email" }),
    telephone: z.string().min(1, { message: "Telephone is required" }),
    company_name: z.string().min(1, { message: "Company name is required" }),
    address: z.string().min(1, { message: "Address is required" }),
    address_2: z.string().min(1, { message: "Address line 2 is required" }),
    address_type: z
      .enum(["residential", "commercial"])
      .refine((value) => value === "residential" || value === "commercial", {
        message:
          "Invalid address type, must be one of 'residential' or 'commercial'",
      }),
    city: z.string().min(1, { message: "City is required" }),
    state: z.string().min(1, { message: "State is required" }),
    zip_code: z.string().min(1, { message: "Zip code is required" }),
    country: z.string().min(1, { message: "Country is required" }),
    password: z.string().min(1, { message: "Password is required" }),
    subscribe_newsletter: z.boolean().default(false),
  }),
  business_details: z.object({
    business_type: z.string().optional(),
    business_nature: z.string().optional(),
    website: z.string().optional(),
    maxton_account: z.string().optional(),
    has_mechanic: z.enum(["yes", "no", "use service company"], {
      required_error: "Mechanic status is required",
    }),
    number_of_elevators: z.coerce.number().optional(),
    authorized_contact_name: z.string().optional(),
    buyer_name: z.string().optional(),
    buyer_phone: z.string().optional(),
    technical_contact: z.string().optional(),
    technical_contact_phone: z.string().optional(),
    account_payable_contact: z.string().optional(),
    account_payable_phone: z.string().optional(),
    technician: z.string().optional(),
  }),
});

export type CreateUserDTO = z.infer<typeof createPublicUserSchema>;

export interface CreateUserResponse {
  data: { id: string };
  error?: string;
}

async function createNewUserHandler(req: NextApiRequest, res: NextApiResponse) {
  const { data, error } = createPublicUserSchema.safeParse(req.body);

  if (error) {
    return res.status(400).json({
      error: error.errors.at(0)?.message,
    });
  }

  const { user_details, business_details }: CreateUserDTO = data;

  const {
    first_name,
    last_name,
    email,
    telephone,
    password,
    address,
    address_2,
    address_type,
    company_name,
    city,
    state,
    zip_code,
    country,
    subscribe_newsletter,
  } = user_details;

  const supabaseAdminClient = createSupabaseAdminClient();

  const getUser = await supabaseAdminClient
    .schema("public")
    .from("users")
    .select("id")
    .eq("email", email)
    .single();

  if (!!getUser.data?.id) {
    return res.status(400).json({
      error: "User already exists",
    });
  }

  const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL;

  const signup = await supabaseClient.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${SITE_URL}/auth/callback`,
    },
  });

  if (signup.error) {
    return res.status(400).json({
      error: signup.error.message,
    });
  }

  const id = signup.data.user?.id;

  if (!id) {
    console.log("signup error", signup.error);

    return res.status(400).json({
      error: "Something went wrong, please contact support",
    });
  }

  // It's not possible to add the phone number in the signUp method
  // so we need to add it after the user is created
  // and update the user's phone number
  const updateUserPhone = await supabaseAdminClient.auth.admin.updateUserById(
    id,
    {
      phone: telephone,
    },
  );

  if (updateUserPhone.error) {
    console.error("updateUserPhone error", updateUserPhone.error);

    await supabaseAdminClient.auth.admin.deleteUser(id);

    return res.status(400).json({
      error: "Phone number is invalid, please use a valid phone number.",
    });
  }

  const newUser = await supabaseAdminClient
    .schema("public")
    .from("users")
    .insert({
      id,
      first_name,
      last_name,
      role: "manager",
      status: "pending",
      email: email,
      has_subscribed_to_newsletter: subscribe_newsletter,
    });

  if (
    newUser.error &&
    newUser.error.message.includes("violates foreign key constraint")
  ) {
    console.log("newUser error violates foreign key constraint", newUser.error);

    await supabaseAdminClient.auth.admin.deleteUser(id);

    return res.status(400).json({
      error: "User doesn't exist",
    });
  }

  if (
    newUser.error &&
    newUser.error.message.includes(
      "duplicate key value violates unique constraint",
    )
  ) {
    console.log(
      "newUser error duplicate key value violates unique constraint",
      newUser.error,
    );

    await supabaseAdminClient.auth.admin.deleteUser(id);

    return res.status(400).json({
      error: "User already exists",
    });
  }

  if (newUser.error) {
    console.log("newUser error", newUser.error);

    await supabaseAdminClient.auth.admin.deleteUser(id);

    return res.status(400).json({
      error: "Something went wrong, please contact support",
    });
  }

  const tempCustomer = await supabaseAdminClient
    .schema("public")
    .from("customers")
    .insert({
      user_id: id,
      primary_contact_name: `${first_name} ${last_name}`,
      status: "pending",
      role: "customer",
      company_name: company_name,
      credit_limit: 0,
    })
    .select("id")
    .single();

  if (tempCustomer.error) {
    console.log("tempCustomer error", tempCustomer.error);

    await supabaseAdminClient.auth.admin.deleteUser(id);

    return res.status(400).json({
      error: "Something went wrong, please contact support",
    });
  }

  const billingInfo = await supabaseAdminClient
    .schema("public")
    .from("billing_addresses")
    .insert({
      customer_id: tempCustomer.data.id,
      address,
      address_2,
      address_type,
      company_name,
      city,
      state,
      zip_code,
      country,
      default: true,
      approved: true,
    });

  if (billingInfo.error) {
    console.log("billingInfo error", billingInfo.error);

    await supabaseAdminClient.auth.admin.deleteUser(id);

    return res.status(400).json({
      error: "Something went wrong, please contact support",
    });
  }

  const businessDetails = await supabaseAdminClient
    .from("business_details")
    .insert({
      ...business_details,
      user_id: id,
    });

  if (businessDetails.error) {
    console.log("businessDetails error", businessDetails.error);

    await supabaseAdminClient.auth.admin.deleteUser(id);

    return res.status(400).json({
      error: "Something went wrong, please contact support",
    });
  }

  // Send registration confirmation email
  try {
    const loginUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/log-in`;

    const emailTemplate = createRegistrationConfirmationTemplate({
      to: email,
      name: `${first_name} ${last_name}`,
      email: email,
      address: address,
      city: city,
      state: state,
      zip_code: zip_code,
      country: country,
      companyName: company_name,
      businessType: business_details.business_type,
      businessNature: business_details.business_nature,
      website: business_details.website,
      maxtonAccount: business_details.maxton_account,
      numberOfElevators: business_details.number_of_elevators,
      registrationDate: new Date().toISOString(),
      loginUrl: loginUrl,
      additionalNotes:
        "Our team is reviewing your application. Thank you for your patience.",
    });

    const mailerOptions = createMailerOptions();
    const mailer = createMailer(mailerOptions);
    await sendEmail(mailer, emailTemplate);
  } catch (emailError) {
    console.error("Error sending registration confirmation email:", emailError);
    // Continue with the response even if the email fails
  }

  return res.status(200).json({
    data: { id },
  });
}

export interface CustomerWithGroup extends Customer {
  group_data?: { data: Group };
}

export interface PublicUserWithCustomer extends Omit<PublicUser, "notes"> {
  notes?: string | null;
  customer_data?: CustomerWithGroup[];
}

export interface GetAllUsersResponse {
  data?: PublicUserWithCustomer[];
  error?: string;
  total?: number;
}

async function getAllUsersHandler(
  req: NextApiRequest,
  res: NextApiResponse<GetAllUsersResponse>,
) {
  const query = req.query;
  const page = query.page ? parseInt(query.page as string) : 1;
  const limit = query.limit ? parseInt(query.limit as string) : 10;

  const supabaseAdminClient = createSupabaseAdminClient();

  try {
    // Get total count and paginated users concurrently
    const [countResult, usersResult] = await Promise.all([
      supabaseAdminClient
        .from("users")
        .select("id", { count: "exact", head: true })
        .filter("status", "neq", "rejected"),
      supabaseAdminClient
        .from("users")
        .select(
          `
          id,
          created_at,
          updated_at,
          status,
          role,
          first_name,
          last_name,
          email,
          notes
        `,
        )
        .filter("status", "neq", "rejected")
        .order("created_at", { ascending: false })
        .range((page - 1) * limit, page * limit - 1),
    ]);

    const { count } = countResult;
    const { data: allUsers, error: usersError } = usersResult;

    if (usersError) {
      return res.status(400).json({
        error: usersError.message,
      });
    }

    if (!allUsers?.length) {
      return res.status(200).json({
        data: [],
        total: count ?? 0,
      });
    }

    // Custom sorting logic: Sort by created_at first, then insert based on updated_at
    const sortedUsers = allUsers.sort((a, b) => {
      const aCreatedAt = new Date(a.created_at).getTime();
      const bCreatedAt = new Date(b.created_at).getTime();
      const aUpdatedAt = a.updated_at ? new Date(a.updated_at).getTime() : aCreatedAt;
      const bUpdatedAt = b.updated_at ? new Date(b.updated_at).getTime() : bCreatedAt;

      // Use the more recent of created_at or updated_at for each user
      const aEffectiveDate = Math.max(aCreatedAt, aUpdatedAt);
      const bEffectiveDate = Math.max(bCreatedAt, bUpdatedAt);

      // Sort by effective date in descending order (most recent first)
      return bEffectiveDate - aEffectiveDate;
    });

    // Apply pagination to the sorted users
    const users = sortedUsers;

    const userIds = users.map((user) => user.id);

    // Get business details and customers concurrently
    const [businessDetailsResult, customersResult] = await Promise.all([
      supabaseAdminClient
        .from("business_details")
        .select(
          `
          id,
          user_id,
          business_type,
          business_nature,
          website,
          maxton_account,
          number_of_elevators,
          authorized_contact_name,
          buyer_name,
          buyer_phone,
          technical_contact,
          technical_contact_phone,
          account_payable_contact,
          account_payable_phone,
          technician
        `,
        )
        .in("user_id", userIds),
      supabaseAdminClient
        .from("customers")
        .select(
          `
          id,
          role,
          phone,
          status,
          user_id,
          created_at,
          company_logo,
          company_name,
          credit_limit,
          shipping_notes,
          company_website,
          cost_percentage,
          customer_number,
          payment_options,
          primary_contact_name
        `,
        )
        .in("user_id", userIds),
    ]);

    const { data: businessDetails } = businessDetailsResult;
    const { data: customers } = customersResult;

    if (!customers?.length) {
      return res.status(200).json({
        data: users.map((user) => ({
          ...user,
          business_details:
            businessDetails?.find((bd) => bd.user_id === user.id) || null,
          customer_data: [],
        })) as unknown as PublicUserWithCustomer[],
        total: count ?? 0,
      });
    }

    const customerIds = customers.map((c) => c.id);

    // Get all related data for customers concurrently
    const [
      customerGroupsResult,
      billingAddressesResult,
      customerCategoriesResult,
    ] = await Promise.all([
      supabaseAdminClient
        .from("customer_groups")
        .select(
          `
          customer_id,
          groups (
            id,
            name,
            description
          )
        `,
        )
        .in("customer_id", customerIds),
      supabaseAdminClient
        .from("billing_addresses")
        .select(
          `
          id,
          city,
          state,
          address,
          country,
          default,
          approved,
          zip_code,
          created_at,
          customer_id
        `,
        )
        .in("customer_id", customerIds),
      supabaseAdminClient
        .from("customer_categories")
        .select(
          `
          customer_id,
          categories (
            id,
            name,
            description
          )
        `,
        )
        .in("customer_id", customerIds),
    ]);

    const { data: customerGroupsData } = customerGroupsResult;
    const { data: billingAddressesData } = billingAddressesResult;
    const { data: customerCategoriesData } = customerCategoriesResult;

    // Rebuild the nested structure to match the original response format
    const customersWithDetails = users.map((user) => {
      // Find business details for this user
      const userBusinessDetails = businessDetails?.find(
        (bd) => bd.user_id === user.id,
      );

      // Find customers for this user
      const userCustomers = customers
        ?.filter((c) => c.user_id === user.id)
        .map((customer) => {
          // Find related data for this customer
          const customerGroups = customerGroupsData?.filter(
            (cg) => cg.customer_id === customer.id,
          );
          const customerBillingAddresses = billingAddressesData?.filter(
            (ba) => ba.customer_id === customer.id,
          );
          const customerCategories = customerCategoriesData?.filter(
            (cc) => cc.customer_id === customer.id,
          );

          return {
            ...customer,
            group_data: customerGroups?.length
              ? customerGroups[0].groups
              : null,
            billing_addresses: customerBillingAddresses || [],
            categories:
              customerCategories?.map((cc) => ({
                category_data: cc.categories,
              })) || [],
          };
        });

      return {
        ...user,
        business_details: userBusinessDetails || null,
        customer_data: userCustomers || [],
      };
    }) as unknown as PublicUserWithCustomer[];

    return res.status(200).json({
      data: customersWithDetails,
      total: count ?? 0,
    });
  } catch (error) {
    console.error("Error in getAllUsersHandler:", error);
    return res.status(400).json({
      error: "An error occurred while fetching users data",
    });
  }
}
