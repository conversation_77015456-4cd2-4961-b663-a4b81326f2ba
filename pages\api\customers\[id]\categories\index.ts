import { checkAdmin, checkPermission } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { CustomerCategory } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default checkAdmin(
  matchRoute({
    GET: getCustomerCategoriesHandler,
    POST: checkPermission(
      "create:customer_categories",
      addCustomerCategoryHandler,
    ),
    DELETE: checkPermission(
      "delete:customer_categories",
      deleteCustomerCategoryHandler,
    ),
  }),
);

// Schema for adding a new customer category
export const addCustomerCategorySchema = z.object({
  category_type: z.enum(["all_products", "support_products"], {
    required_error: "Category type is required",
  }),
});

export type AddCustomerCategoryRequest = z.infer<
  typeof addCustomerCategorySchema
>;

export interface CustomerCategoriesResponse {
  error?: string;
  customer_categories?: CustomerCategory[];
  customer_category?: CustomerCategory;
  message?: string;
}

// Get all categories for a specific customer
async function getCustomerCategoriesHandler(
  req: NextApiRequest,
  res: NextApiResponse<CustomerCategoriesResponse>,
) {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ error: "Customer ID is required" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const { data, error } = await supabaseAdminClient
    .from("customer_categories")
    .select(
      `
            *,
            category:category_id (
                id,
                name,
                value,
                parent_category_id
            )
        `,
    )
    .eq("customer_id", id as string);

  if (error) {
    return res.status(500).json({ error: error.message });
  }

  return res.status(200).json({ customer_categories: data });
}

// Add categories to a customer based on category type
async function addCustomerCategoryHandler(
  req: NextApiRequest,
  res: NextApiResponse<CustomerCategoriesResponse>,
) {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ error: "Customer ID is required" });
  }

  const parsedData = addCustomerCategorySchema.safeParse(req.body);

  if (!parsedData.success) {
    return res.status(400).json({ error: parsedData.error.message });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const customer = await supabaseAdminClient
    .from("customers")
    .select("*")
    .eq("id", id as string)
    .single();

  if (customer.error) {
    return res.status(500).json({ error: customer.error.message });
  }

  // Get all available categories
  const { data: allCategories, error: categoriesError } =
    await supabaseAdminClient
      .from("categories")
      .select("id, name, parent_category_id");

  if (categoriesError) {
    return res.status(500).json({ error: categoriesError.message });
  }

  // Determine which categories to assign based on category type
  let categoryIdsToAssign: string[] = [];

  if (parsedData.data.category_type === "all_products") {
    // Assign all categories
    categoryIdsToAssign = allCategories.map((cat) => cat.id);
  } else if (parsedData.data.category_type === "support_products") {
    // Assign all categories except Valves and their subcategories
    const valvesCategory = allCategories.find((cat) => cat.name === "Valves");

    categoryIdsToAssign = allCategories
      .filter((category) => {
        // Exclude if it's the Valves category itself
        if (valvesCategory && category.id === valvesCategory.id) {
          return false;
        }

        // Exclude if it's a subcategory of Valves (check if parent is valves)
        if (category.parent_category_id) {
          const parentCategory = allCategories.find(
            (cat) => cat.id === category.parent_category_id,
          );
          if (
            parentCategory &&
            parentCategory.name?.toLowerCase()?.includes("valve")
          ) {
            return false;
          }
        }

        return true;
      })
      .map((cat) => cat.id);
  }

  // Remove all existing categories for this customer first
  const { error: deleteError } = await supabaseAdminClient
    .from("customer_categories")
    .delete()
    .eq("customer_id", id as string);

  if (deleteError) {
    return res.status(500).json({ error: deleteError.message });
  }

  // Prepare data for insertion
  const categoriesToInsert = categoryIdsToAssign.map((categoryId) => ({
    customer_id: id as string,
    category_id: categoryId,
    user_id: customer.data.user_id,
  }));

  // Add the new categories to the customer
  const { data, error } = await supabaseAdminClient
    .from("customer_categories")
    .insert(categoriesToInsert).select(`
            *,
            category:category_id (
                id,
                name,
                value,
                parent_category_id
            )
        `);

  if (error) {
    return res.status(500).json({ error: error.message });
  }

  const now = new Date();
  const updated_at = now.toISOString();

  const updateCustomer = await supabaseAdminClient
    .from("customers")
    .update({ updated_at })
    .eq("id", id as string)
    .select("user_id")
    .single();

  const userId = updateCustomer?.data?.user_id;

  if (userId) {
    await supabaseAdminClient
      .from("users")
      .update({ updated_at })
      .eq("id", userId);
  }

  return res.status(201).json({ customer_categories: data });
}

// Delete a customer category association
async function deleteCustomerCategoryHandler(
  req: NextApiRequest,
  res: NextApiResponse<CustomerCategoriesResponse>,
) {
  const { id } = req.query;
  const { category_id } = req.body;

  if (!id) {
    return res.status(400).json({ error: "Customer ID is required" });
  }

  if (!category_id) {
    return res.status(400).json({ error: "Category ID is required" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const { error } = await supabaseAdminClient
    .from("customer_categories")
    .delete()
    .eq("customer_id", id as string)
    .eq("category_id", category_id);

  if (error) {
    return res.status(500).json({ error: error.message });
  }

  return res
    .status(200)
    .json({ message: "Category removed from customer successfully" });
}
