import { OrderDataWithStatusAndItems } from "@/components/features/store/orders-table";
import { GetCustomerResponse } from "@/pages/api/customers/[id]";
import {
  AddBillingAddress,
  AddBillingAddressResponse,
  GetBillingAddressResponse,
} from "@/pages/api/customers/[id]/billing-address";
import {
  DeleteBillingAddressResponse,
  UpdateBillingAddress,
  UpdateBillingAddressResponse,
} from "@/pages/api/customers/[id]/billing-address/[billing_id]";
import { GetOrdersByUserIdResponse } from "@/pages/api/customers/[id]/orders";
import {
  CancelOrderResponse,
  GetOrderByIdResponse,
} from "@/pages/api/customers/[id]/orders/[order_id]";
import { CancellationRequestDTO } from "@/pages/api/customers/[id]/orders/cancellation-request";
import {
  AddShippingAddress,
  AddShippingAddressResponse,
  GetShippingAddressResponse,
} from "@/pages/api/customers/[id]/shipping-address";
import {
  DeleteShippingAddressResponse,
  UpdateShippingAddress,
  UpdateShippingAddressResponse,
} from "@/pages/api/customers/[id]/shipping-address/[shipping_id]";
import { GetDashboardDataResponse } from "@/pages/api/customers/dashboard";
import { AddNewOrder, AddNewOrderResponse } from "@/pages/api/orders";
import useAuthStore from "@/stores/auth-store";
import { supabaseClient } from "@/supabase";
import {
  BillingAddress,
  Discount,
  Order,
  OrderItem,
  OrderStatus,
  Product,
  ShippingAddress,
} from "@/supabase/types";
import {
  useMutation,
  useQueries,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";

async function getCustomerById(
  user_id: string,
  token: string
): Promise<GetCustomerResponse> {
  const response = await fetch(`/api/customers/${user_id}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });
  const data = (await response.json()) as GetCustomerResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useGetCustomerQuery(user_id: string) {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)();
  const token = useAuthStore((state) => state.token);

  return useQuery({
    enabled: isAuthenticated && !!user_id,
    queryKey: ["customers", user_id],
    queryFn: async () => await getCustomerById(user_id, token),
  });
}

async function getBillingAddresses(
  customerId: string,
  token: string
): Promise<BillingAddress[]> {
  const response = await fetch(`/api/customers/${customerId}/billing-address`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  const data = (await response.json()) as GetBillingAddressResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data.billing_address ?? [];
}

export function useGetBillingAddressesQuery(userId: string) {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)();
  const token = useAuthStore((state) => state.token);

  return useQuery({
    enabled: isAuthenticated && !!userId,
    queryKey: ["billing-addresses", userId],
    queryFn: async () => await getBillingAddresses(userId, token),
  });
}

async function addBillingAddress(
  dto: AddBillingAddress,
  customer_id: string,
  token: string
): Promise<BillingAddress> {
  const response = await fetch(
    `/api/customers/${customer_id}/billing-address`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(dto),
    }
  );

  const data = (await response.json()) as AddBillingAddressResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data.billing_address ?? ({} as BillingAddress);
}

export function useAddBillingAddressMutation(userId: string) {
  const token = useAuthStore((state) => state.token);
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["add-billing-addresses", userId],
    mutationFn: async (dto: AddBillingAddress) =>
      await addBillingAddress(dto, userId, token),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["billing-addresses", userId],
      });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function updateBillingAddress(
  userId: string,
  billingId: string,
  token: string,
  dto: UpdateBillingAddress
) {
  const response = await fetch(
    `/api/customers/${userId}/billing-address/${billingId}`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(dto),
    }
  );

  const data = (await response.json()) as UpdateBillingAddressResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data.billing_address ?? ({} as BillingAddress);
}

export function useUpdateBillingAddressMutation(
  userId: string,
  billingId: string
) {
  const token = useAuthStore((state) => state.token);
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["update-billing-addresses", userId, billingId],
    mutationFn: async (dto: UpdateBillingAddress) =>
      await updateBillingAddress(userId, billingId, token, dto),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["billing-addresses", userId],
      });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function removeBillingAddress(userId: string, id: string, token: string) {
  const response = await fetch(
    `/api/customers/${userId}/billing-address/${id}`,
    {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    }
  );

  const data = (await response.json()) as DeleteBillingAddressResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data.billing_address ?? ({} as BillingAddress);
}

export function useDeleteBillingAddressMutation(
  userId: string,
  billingId: string
) {
  const token = useAuthStore((state) => state.token);
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["remove-billing-addresses", userId, billingId],
    mutationFn: async () =>
      await removeBillingAddress(userId, billingId, token),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["billing-addresses", userId],
      });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function getShippingAddresses(
  customerId: string,
  token: string
): Promise<ShippingAddress[]> {
  const response = await fetch(
    `/api/customers/${customerId}/shipping-address`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    }
  );

  const data = (await response.json()) as GetShippingAddressResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data.shipping_address ?? [];
}

export function useGetShippingAddressesQuery(customerId: string) {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)();
  const token = useAuthStore((state) => state.token);

  return useQuery({
    enabled: isAuthenticated && !!customerId,
    queryKey: ["shipping-addresses", customerId],
    queryFn: async () => await getShippingAddresses(customerId, token),
  });
}

async function addShippingAddress(
  dto: AddShippingAddress,
  user_id: string,
  token: string
): Promise<ShippingAddress> {
  const response = await fetch(`/api/customers/${user_id}/shipping-address`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(dto),
  });

  const data = (await response.json()) as AddShippingAddressResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data.shipping_address ?? ({} as ShippingAddress);
}

export function useAddShippingAddressMutation(userId: string) {
  const token = useAuthStore((state) => state.token);
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["add-shipping-addresses", userId],
    mutationFn: async (dto: AddShippingAddress) =>
      await addShippingAddress(dto, userId, token),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["shipping-addresses", userId],
      });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function updateShippingAddress(
  userId: string,
  id: string,
  dto: UpdateShippingAddress,
  token: string
): Promise<ShippingAddress> {
  const response = await fetch(
    `/api/customers/${userId}/shipping-address/${id}`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(dto),
    }
  );

  const data = (await response.json()) as UpdateShippingAddressResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data.shipping_address ?? ({} as ShippingAddress);
}

export function useUpdateShippingAddressMutation(userId: string, id: string) {
  const token = useAuthStore((state) => state.token);
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["update-shipping-addresses", id],
    mutationFn: async (dto: UpdateShippingAddress) =>
      await updateShippingAddress(userId, id, dto, token),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["shipping-addresses", userId],
      });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function removeShippingAddress(
  userId: string,
  id: string,
  token: string
) {
  const response = await fetch(
    `/api/customers/${userId}/shipping-address/${id}`,
    {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    }
  );

  const data = (await response.json()) as DeleteShippingAddressResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data.shipping_address ?? ({} as ShippingAddress);
}

export function useDeleteShippingAddressMutation(userId: string, id: string) {
  const token = useAuthStore((state) => state.token);
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["remove-shipping-addresses", userId, id],
    mutationFn: async () => await removeShippingAddress(userId, id, token),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["shipping-addresses", userId],
      });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function addNewOrder(dto: AddNewOrder, token: string) {
  const response = await fetch(`/api/orders`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(dto),
  });

  const data = (await response.json()) as AddNewOrderResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data.order_id;
}

export function useAddNewOrderMutation(userId: string) {
  const token = useAuthStore((state) => state.token);
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["add-new-order", userId],
    mutationFn: async (dto: AddNewOrder) => await addNewOrder(dto, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["orders", userId] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

export function useUploadFile(userId: string, path: string) {
  const token = useAuthStore((state) => state.token);
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["upload-file", userId],
    mutationFn: async (file: File) => {
      const filePath = `${path}/${userId}/${Date.now()}-${file.name}`;
      const { data: urlData, error: uploadError } = await supabaseClient.storage
        .from("maxton-bucket")
        .upload(filePath, file, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

      if (uploadError) {
        throw new Error(uploadError.message);
      }

      const formData = new FormData();
      formData.append("file", file);

      return urlData;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["upload-file", userId] });
      return data;
    },
    onError: (error) => {
      return { error: error.message };
    },
  });
}

export function useUploadImage(path: string) {
  const token = useAuthStore((state) => state.token);
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["upload-image", path],
    mutationFn: async (file: File) => {
      const filePath = `${path}/${Date.now()}-${file.name}`;
      const { data: urlData, error: uploadError } = await supabaseClient.storage
        .from("maxton-bucket")
        .upload(filePath, file, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

      if (uploadError) {
        throw new Error(uploadError.message);
      }

      const formData = new FormData();
      formData.append("file", file);

      return urlData;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["upload-image", path] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

export function useGetAllImages(path: string) {
  return useQuery<string[]>({
    queryKey: ["get-all-images", path],
    queryFn: async () => {
      const { data, error } = await supabaseClient.storage
        .from("maxton-bucket")
        .list(path);
      return data?.map((item) => item.name) ?? [];
    },
  });
}

export function useGetImage(path: string) {
  return useQuery<string>({
    queryKey: ["get-image", path],
    queryFn: async () => {
      const { data, error } = await supabaseClient.storage
        .from("maxton-bucket")
        .download(path);
      if (error) return "";
      const blob = new Blob([data], { type: "image/jpeg" });
      return URL.createObjectURL(blob);
    },
    enabled: !!path && !path.startsWith("http"),
  });
}

export function useGetImages(paths: string[]) {
  return useQueries<string[]>({
    queries: paths.map((path) => ({
      queryKey: ["get-image", path],
      queryFn: async () => {
        const { data, error } = await supabaseClient.storage
          .from("maxton-bucket")
          .download(path);
        if (error) return "";
        const blob = new Blob([data], { type: "image/jpeg" });
        return URL.createObjectURL(blob);
      },
      enabled: !!path && !path.startsWith("http"),
    })),
  });
}

export interface OrdersResponse {
  orders: Partial<OrderDataWithStatusAndItems>[];
  total: number;
}

async function getOrdersByUserId(
  userId: string,
  page: number,
  limit: number
): Promise<OrdersResponse> {
  const token = useAuthStore.getState().token;
  const response = await fetch(
    `/api/customers/${userId}/orders?page=${page}&limit=${limit}`,
    {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    }
  );

  const data = (await response.json()) as GetOrdersByUserIdResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return {
    orders: data.orders || [],
    total: data.total || 0,
  };
}

export function useGetOrdersByUserIdQuery(
  userId: string,
  page: number,
  limit: number
) {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)();

  return useQuery<OrdersResponse>({
    enabled: isAuthenticated && !!userId,
    queryKey: ["orders", userId, page, limit],
    queryFn: () => getOrdersByUserId(userId, page, limit),
  });
}

export interface OrderItemOption {
  name: string;
  value: string;
  price?: number;
}

export interface OrderItemsWithProduct
  extends Pick<OrderItem, "id" | "order_id" | "quantity"> {
  products: Product;
  options: OrderItemOption[];
  item_price?: number;
}

export interface OrderWithItems extends Order {
  order_items: OrderItemsWithProduct[];
  order_statuses: OrderStatus[];
  shipping_addresses: ShippingAddress;
  discounts: Discount;
  tax_rate?: number | null;
  tax_amount?: number | null;
}

async function getOrderById(
  userId: string,
  orderId: string,
  token: string
): Promise<OrderWithItems> {
  const response = await fetch(`/api/customers/${userId}/orders/${orderId}`, {
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  const data = (await response.json()) as GetOrderByIdResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  const order = data.order as OrderWithItems;

  return order ?? ({} as OrderWithItems);
}

export function useGetOrderByIdQuery(userId: string, orderId: string) {
  const token = useAuthStore((state) => state.token);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)();

  return useQuery({
    enabled: isAuthenticated && !!userId && !!orderId,
    queryKey: ["order", userId, orderId],
    queryFn: () => getOrderById(userId, orderId, token),
  });
}

async function cancelOrder(userId: string, orderId: string, token: string) {
  const response = await fetch(`/api/customers/${userId}/orders/${orderId}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  const data = (await response.json()) as CancelOrderResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data.order;
}

export function useCancelOrderMutation(userId: string) {
  const token = useAuthStore((state) => state.token);
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["cancel-order", userId],
    mutationFn: async (orderId: string) => cancelOrder(userId, orderId, token),
    onSuccess: (_, orderId) => {
      queryClient.invalidateQueries({ queryKey: ["orders", userId] });
      queryClient.invalidateQueries({ queryKey: ["order", userId, orderId] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

interface CancellationRequestResponse {
  success?: boolean;
  data?: {
    id: string;
  };
  error?: string;
}

async function submitCancellationRequest(
  userId: string,
  data: CancellationRequestDTO,
  token: string
) {
  const response = await fetch(
    `/api/customers/${userId}/orders/cancellation-request`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    }
  );

  const responseData = (await response.json()) as CancellationRequestResponse;

  if (responseData.error) {
    throw new Error(responseData.error);
  }

  return responseData;
}

export function useSubmitCancellationRequestMutation(userId: string) {
  const token = useAuthStore((state) => state.token);
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["cancellation-request", userId],
    mutationFn: async (data: CancellationRequestDTO) =>
      submitCancellationRequest(userId, data, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["orders", userId] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function getCustomerDashboardData(userId: string, token: string) {
  const response = await fetch(`/api/customers/dashboard?id=${userId}`, {
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  const data = (await response.json()) as GetDashboardDataResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useGetCustomerDashboardDataQuery(userId: string) {
  const token = useAuthStore((state) => state.token);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)();

  return useQuery({
    enabled: isAuthenticated && !!userId,
    queryKey: ["customer-dashboard", userId],
    queryFn: () => getCustomerDashboardData(userId, token),
  });
}
