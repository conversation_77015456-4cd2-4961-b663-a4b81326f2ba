import { GetProductResponse } from "@/pages/api/products/[slug]";
import { Product } from "@/supabase/types";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import useAuthStore from "stores/auth-store";
import { supabaseClient } from "../supabase";

// Fetch paginated products from API endpoint
async function getPaginatedProducts(
  page: number = 1,
  limit: number = 10,
  token: string,
  category?: string,
  search?: string
) {
  const userId = useAuthStore.getState().data?.id;
  if (!userId) throw new Error("User ID not found");

  const url = new URL(
    `/api/customers/${userId}/products`,
    window.location.origin
  );
  url.searchParams.append("page", page.toString());
  url.searchParams.append("limit", limit.toString());
  if (category) url.searchParams.append("category", category);
  if (search) url.searchParams.append("search", search);

  const response = await fetch(url.toString(), {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  const data = await response.json();

  if (data.error) {
    throw new Error(data.error);
  }

  return {
    products: data.products || [],
    categories: data.categories || [],
    total: data.total || 0,
    totalPages: data.totalPages || Math.ceil((data.total || 0) / limit),
  };
}

// Fetch public products for non-authenticated users
async function getPublicProducts(
  page: number = 1,
  limit: number = 10,
  category?: string,
  search?: string
) {
  const url = new URL(`/api/products`, window.location.origin);
  url.searchParams.append("page", page.toString());
  url.searchParams.append("limit", limit.toString());
  if (category) url.searchParams.append("category", category);
  if (search) url.searchParams.append("search", search);

  const response = await fetch(url.toString());
  const data = await response.json();

  if (data.error) {
    throw new Error(data.error);
  }

  return {
    products: data.products || [],
    categories: data.categories || [],
    total: data.total || 0,
    totalPages: data.totalPages || 1,
  };
}

export function useGetPaginatedProductsQuery(
  page: number = 1,
  limit: number = 10,
  token: string,
  category?: string,
  search?: string
) {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const userId = useAuthStore((state) => state.data?.id);

  return useQuery({
    enabled: isAuthenticated() && !!userId,
    queryKey: ["paginated-products", page, limit, category, search],
    queryFn: async () =>
      await getPaginatedProducts(page, limit, token, category, search),
  });
}

export function useGetPublicProductsQuery(
  page: number = 1,
  limit: number = 10,
  category?: string,
  search?: string
) {
  return useQuery({
    queryKey: ["public-products", page, limit, category, search],
    queryFn: async () => await getPublicProducts(page, limit, category, search),
  });
}

// Fetch all products
async function getAllProducts(page: number = 1, category_ids: string[]) {
  const limit = 100;
  const offset = (page - 1) * limit;
  const productCategoryFilter = await supabaseClient
    .from("products")
    .select(
      "*, product_categories(category_id(id,name,value,parent_category_id(id))), options"
    )
    .limit(limit);

  if (productCategoryFilter.error) {
    throw new Error(productCategoryFilter.error.message);
  }

  const products = productCategoryFilter.data?.filter((product) =>
    product.product_categories.some(
      (category) =>
        category_ids.includes(category.category_id?.id ?? "") ||
        (category.category_id?.parent_category_id?.id &&
          category_ids.includes(
            category.category_id?.parent_category_id?.id ?? ""
          ))
    )
  );

  return products;
}

export function useGetAllProductsQuery(
  page: number = 1,
  category_ids: string[]
) {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

  return useQuery({
    enabled: isAuthenticated() && category_ids.length > 0,
    queryKey: ["products", page, category_ids],
    queryFn: async () => await getAllProducts(page, category_ids),
  });
}

// Function to get a product by ID
async function getProductById(id: string) {
  if (!id) {
    throw new Error("Product ID is required");
  }

  const response = await fetch(`/api/products/${id}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  const data = (await response.json()) as GetProductResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

// Query hook to get a product by ID
export function useGetProductByIdQuery(id: string) {
  return useQuery({
    queryKey: ["product", id],
    queryFn: () => getProductById(id),
    enabled: !!id,
  });
}

async function getProductByName(name: string): Promise<Product> {
  const { data, error } = await supabaseClient
    .from("products")
    .select("*")
    .eq("name", name)
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
}

export function useGetProductByNameQuery(name: string) {
  return useQuery({
    queryKey: ["product", name],
    queryFn: () => getProductByName(name),
    enabled: !!name,
  });
}

// Insert a new product
async function insertProduct(product: Product) {
  const { data, error } = await supabaseClient
    .from("products")
    .insert([product])
    .select();

  if (error) {
    throw new Error(error.message);
  }

  return data;
}

export function useInsertProductMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["products"],
    mutationFn: insertProduct,
    onError: (error) => {
      return error;
    },
    onSuccess: async (data) => {
      await queryClient.invalidateQueries({ queryKey: ["products"] });
      return data;
    },
  });
}

interface UpdateProductDTO {
  id: string;
  updates: Partial<Product>;
}

// Update an existing product
async function updateProduct(dto: UpdateProductDTO): Promise<Product> {
  const update = await supabaseClient
    .from("products")
    .update(dto.updates)
    .eq("id", dto.id)
    .select()
    .single();

  if (update.error) {
    throw new Error(update.error.message);
  }

  return update.data;
}

export function useUpdateProductMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["products"],
    mutationFn: updateProduct,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["products", data.id] });
      return data;
    },
    onError: (error) => {
      return error;
    },
  });
}

// Delete a product by ID
async function deleteProduct(id: string) {
  const { data, error } = await supabaseClient
    .from("products")
    .delete()
    .eq("id", id)
    .select("id")
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
}

export function useDeleteProductMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["products"],
    mutationFn: deleteProduct,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["products", data.id] });
      return data;
    },
    onError: (error) => {
      return error;
    },
  });
}
