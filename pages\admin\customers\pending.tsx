import { SvgSpinners90Ring } from "@/components/common/icons";
import AdminLayout from "@/components/features/admin/layout";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { CheckboxGroup, CheckboxItem } from "@/components/ui/checkbox-group";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  DataTable,
  DataTableFilter,
  DataTableSkeleton,
  DataTableToolbar,
} from "@/components/ui/data-table";
import { DataTableColumnHeader } from "@/components/ui/data-table-column-header";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog-shadcn";
import { Button } from "@/components/ui/shadcn-button";
import { toast } from "@/hooks/use-toast";
import { PublicUserWithCustomer } from "@/pages/api/users/index";
import { UpdateUserStatusRequest } from "@/pages/api/users/[slug]/status";
import {
  useCreateCustomerGroupMutation,
  useGetGroupsQuery,
  useGetPendingUsersQuery,
  useUpdateUserStatusMutation,
  useGetAllCategoriesQuery,
  useAddCustomerCategoryMutation,
} from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { GroupWithMembers } from "@/pages/api/groups";
import { UserStatus } from "@/supabase/types";
import {
  ColumnDef,
  ColumnFiltersState,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  Row,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import { Check, CheckCircle, ChevronDown, X, XCircle } from "lucide-react";
import Link from "next/link";
import Head from "next/head";
import { useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

// Add type for the combined promise results
type ApprovalPromiseResult =
  | { type: "status"; result: any }
  | { type: "group"; result: any }
  | { type: "categories"; result: any[] | null };

export default function PendingCustomers() {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "created_at",
      desc: true, // Newest first
    },
  ]);
  const [rowSelection, setRowSelection] = useState({});

  const token = useAuthStore((state) => state.token);
  const { data, isLoading, isError, refetch } = useGetPendingUsersQuery(
    page,
    pageSize,
    token
  );

  const pendingCustomers = data?.data ?? [];
  const totalItems = data?.total ?? 0;
  const totalPages = Math.max(1, Math.ceil(totalItems / pageSize));

  const columns: ColumnDef<PublicUserWithCustomer>[] = [
    {
      header: "Company Name",
      accessorKey: "companyName",
      cell: ({ row }) => {
        const customer = row.original;
        return (
          <div className="flex items-center gap-2">
            {customer.customer_data?.[0]?.company_name.trim()
              ? customer.customer_data?.[0]?.company_name
              : "N/A"}
          </div>
        );
      },
    },
    {
      id: "name",
      header: "Name",
      accessorFn: (row) => `${row.first_name} ${row.last_name}`,
      cell: ({ row }) => {
        const customer = row.original;

        return (
          <div className="flex items-center gap-2">
            <Avatar>
              <AvatarFallback>{customer.first_name?.charAt(0)}</AvatarFallback>
            </Avatar>
            {customer.first_name} {customer.last_name}
          </div>
        );
      },
    },
    {
      header: "Email",
      accessorKey: "email",
      cell: ({ row }) => {
        const customer = row.original;
        return <div className="flex items-center gap-2">{customer.email}</div>;
      },
    },
    {
      accessorKey: "created_at",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Requested At" />
      ),
      cell: ({ row }) => {
        const createdAt = row.getValue("created_at") as string;
        const date = new Date(createdAt);
        return (
          <div>
            {date.toLocaleDateString("en-US", {
              year: "numeric",
              month: "short",
              day: "numeric",
            })}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      header: "Actions",
      accessorKey: "actions",
      cell: CustomerActionButtons,
    },
  ];

  const table = useReactTable({
    data: pendingCustomers,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onGlobalFilterChange: setGlobalFilter,
    onColumnFiltersChange: setColumnFilters,
    onSortingChange: setSorting,
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const newPagination = updater({
          pageIndex: page - 1,
          pageSize,
        });
        setPage(newPagination.pageIndex + 1);
        setPageSize(newPagination.pageSize);
      } else {
        setPage(updater.pageIndex + 1);
        setPageSize(updater.pageSize);
      }
    },
    state: {
      sorting,
      rowSelection,
      columnFilters,
      pagination: {
        pageIndex: page - 1,
        pageSize,
      },
    },
    manualPagination: true,
    pageCount: totalPages,
  });

  return (
    <AdminLayout>
      <Head>
        <title>Customers | Pending Approvals</title>
      </Head>
      <div className="space-y-4">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h2 className="text-4xl font-bold tracking-tight">
              Manage Pending Approvals
            </h2>
            <p className="text-zinc-500 text-sm">
              Manage pending customer accounts and approve or reject them.
            </p>
          </div>
          <Button variant="outline" className="w-full md:w-auto" asChild>
            <Link href="/admin/customers">Back to All Customers</Link>
          </Button>
        </div>
        <div className="relative w-full h-full flex flex-col gap-12 pt-5">
          {isError ? (
            <div className="flex flex-col items-center justify-center py-10">
              <XCircle className="h-14 w-14 text-red-500 my-4" />
              <h3 className="text-xl font-semibold">
                Error loading pending customers
              </h3>
              <p className="text-gray-500 mt-2">Please try again later</p>
            </div>
          ) : null}
          {isLoading ? (
            <DataTableSkeleton />
          ) : (
            <>
              <DataTable
                data={pendingCustomers}
                columns={columns}
                table={table}
                renderToolbar={(table) => (
                  <DataTableToolbar table={table}>
                    <DataTableFilter
                      table={table}
                      column="name"
                      placeholder="Search pending customers..."
                      className="max-w-md rounded-full"
                    />
                  </DataTableToolbar>
                )}
              />
            </>
          )}
          {pendingCustomers.length > 0 && (
            <div className="flex items-center justify-end py-4">
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage((prev) => Math.max(1, prev - 1))}
                  disabled={page === 1}
                >
                  Previous
                </Button>
                <div className="text-sm">
                  Page {page} of {totalPages}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setPage((prev) => Math.min(totalPages, prev + 1))
                  }
                  disabled={page === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}

function CustomerStatusBadge({ status }: { status: UserStatus }) {
  const variant =
    status === "approved"
      ? "outline"
      : status === "pending"
        ? "pending"
        : "destructive";

  return (
    <Badge variant={variant} className="uppercase">
      {status}
    </Badge>
  );
}

function CustomerActionButtons({ row }: { row: Row<PublicUserWithCustomer> }) {
  const user = row.original;
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [isRejected, setIsRejected] = useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState<string | null>(null);
  const [selectedCategoryType, setSelectedCategoryType] = useState<"all_products" | "support_products" | null>(null);
  const [categoryDropdownOpen, setCategoryDropdownOpen] = useState(false);
  const [rejectionNotes, setRejectionNotes] = useState("");

  const token = useAuthStore((state) => state.token);
  const updateUserStatusMutation = useUpdateUserStatusMutation(user.id, token);
  const createCustomerGroupMutation = useCreateCustomerGroupMutation(token);
  const addCustomerCategoryMutation = useAddCustomerCategoryMutation(token);
  const { refetch } = useGetPendingUsersQuery(1, 10, token);
  const { data: groupData } = useGetGroupsQuery(1, 999, token);
  const { data: categoriesData, isLoading: isCategoriesLoading } =
    useGetAllCategoriesQuery(1, 100, token);

  const handleApproveUser = async () => {
    try {
      const customerId = user?.customer_data?.[0]?.id;

      if (!customerId || !selectedGroupId) {
        toast({
          title: "Failed",
          description:
            "Customer ID and Group ID are required to approve the user.",
          variant: "destructive",
        });
        return;
      }

      // Create an array of promises to execute
      const promises: Promise<ApprovalPromiseResult>[] = [
        updateUserStatusMutation
          .mutateAsync({ status: "approved" } as UpdateUserStatusRequest)
          .then((result) => ({ type: "status", result })),
        createCustomerGroupMutation
          .mutateAsync({
            customer_ids: [customerId],
            group_id: selectedGroupId,
          })
          .then((result) => ({ type: "group", result })),
      ];

      // Add category assignment if category type is selected
      if (selectedCategoryType) {
        promises.push(
          addCustomerCategoryMutation
            .mutateAsync({
              customerId,
              categoryType: selectedCategoryType,
            })
            .then((result) => ({ type: "categories", result: result || null }))
        );
      }

      await Promise.all(promises);
      setIsRejected(false);
    } catch (error) {
      console.error("Error approving user:", error);
    }
  };

  const handleRejectUser = () => {
    updateUserStatusMutation.mutate({ 
      status: "rejected",
      notes: rejectionNotes.trim() || undefined
    } as UpdateUserStatusRequest);
    setIsRejected(true);
  };

  useEffect(
    function showToast() {
      const message = isRejected
        ? "Customer request rejected"
        : "Customer approved successfully";
      const errorMessage = isRejected
        ? "Failed to reject customer"
        : "Failed to approve customer";

      if (updateUserStatusMutation.isSuccess) {
        toast({
          title: "Success",
          description: message,
          variant: "default",
        });
        refetch();
        setShowRejectDialog(false);
        setShowApproveDialog(false);
        if (isRejected) {
          setRejectionNotes("");
        }
      }

      if (updateUserStatusMutation.isError) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        setShowRejectDialog(false);
        setShowApproveDialog(false);
      }
    },
    [
      updateUserStatusMutation.isSuccess,
      updateUserStatusMutation.isError,
      refetch,
      isRejected,
    ]
  );



  return (
    <div className="flex items-center gap-2">
      <Select value={selectedGroupId || ""} onValueChange={setSelectedGroupId}>
        <SelectTrigger className="min-w-[160px] mr-2">
          <SelectValue placeholder="Select group" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectLabel>Assign Group</SelectLabel>
            {groupData?.groups && groupData.groups.length > 0 ? (
              groupData.groups.map((group) => (
                <SelectItem key={group.id} value={group.id}>
                  {group.name}
                </SelectItem>
              ))
            ) : (
              <SelectItem value="no-groups" disabled>
                No groups available
              </SelectItem>
            )}
          </SelectGroup>
        </SelectContent>
      </Select>

      <Select
        open={categoryDropdownOpen}
        onOpenChange={setCategoryDropdownOpen}
        value={selectedCategoryType || ""}
      >
        <SelectTrigger className="min-w-[200px] mr-2">
          <SelectValue placeholder="Select categories">
            {selectedCategoryType === "all_products" && "All Products"}
            {selectedCategoryType === "support_products" && "Support Products"}
            {!selectedCategoryType && "Select"}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectLabel>Select Categories</SelectLabel>
            <div className="p-4 space-y-4">
              <RadioGroup
                value={selectedCategoryType || ""}
                onValueChange={(value) => {
                  setSelectedCategoryType(value as "all_products" | "support_products");
                  setCategoryDropdownOpen(false);
                }}
                className="space-y-3"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="all_products" id="all_products" />
                  <Label htmlFor="all_products" className="font-medium">
                    All Products
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="support_products" id="support_products" />
                  <Label htmlFor="support_products" className="font-medium">
                    Support Products
                  </Label>
                </div>
              </RadioGroup>
            </div>
          </SelectGroup>
        </SelectContent>
      </Select>

      <Button
        variant="outline"
        size="sm"
        className="h-8 px-3 text-green-500"
        onClick={() => setShowApproveDialog(true)}
      >
        <Check className="h-4 w-4 mr-2" />
        Approve
      </Button>

      <Dialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Customer</DialogTitle>
            <DialogDescription>
              Are you sure you want to approve {user.first_name}{" "}
              {user.last_name}? This will grant them access to the platform.
              {selectedGroupId ? (
                <p className="mt-2">
                  This customer will be assigned to the selected group
                  {selectedCategoryType
                    ? ` and the selected category type (${selectedCategoryType === "all_products" ? "All Products" : "Support Products"})`
                    : ""}
                  .
                </p>
              ) : (
                <p className="mt-2 text-amber-500">
                  No group selected. Please select a group to enable approval.
                  Customers cannot be approved without a group assignment.
                </p>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowApproveDialog(false)}
            >
              Cancel
            </Button>
            <Button
              variant="default"
              type="button"
              onClick={handleApproveUser}
              disabled={
                updateUserStatusMutation.isPending ||
                createCustomerGroupMutation.isPending ||
                addCustomerCategoryMutation.isPending ||
                !selectedGroupId
              }
            >
              {updateUserStatusMutation.isPending ||
                createCustomerGroupMutation.isPending ||
                addCustomerCategoryMutation.isPending
                ? "Approving..."
                : "Confirm Approval"}
              {updateUserStatusMutation.isPending ||
                createCustomerGroupMutation.isPending ||
                addCustomerCategoryMutation.isPending ? (
                <SvgSpinners90Ring className="h-4 w-4 ml-2" />
              ) : null}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Button
        variant="outline"
        size="sm"
        className="h-8 px-3 text-red-500"
        onClick={() => setShowRejectDialog(true)}
        type="button"
      >
        <X className="h-4 w-4 mr-2" />
        Reject
      </Button>

      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Customer</DialogTitle>
            <DialogDescription>
              Are you sure you want to reject {user.first_name} {user.last_name}
              ? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="rejection-notes">Rejection Notes (Optional)</Label>
              <Textarea
                id="rejection-notes"
                placeholder="Enter reason for rejection..."
                value={rejectionNotes}
                onChange={(e) => setRejectionNotes(e.target.value)}
                className="min-h-[100px]"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowRejectDialog(false);
                setRejectionNotes("");
              }}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleRejectUser}
              disabled={updateUserStatusMutation.isPending}
              type="button"
            >
              {updateUserStatusMutation.isPending
                ? "Rejecting..."
                : "Confirm Rejection"}
              {updateUserStatusMutation.isPending ? (
                <SvgSpinners90Ring className="h-4 w-4 ml-2" />
              ) : null}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
