import { useEffect, useState } from "react";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    Di<PERSON>Footer,
    Di<PERSON>Header,
    <PERSON><PERSON>Title,
    DialogTrigger
} from "@/components/ui/dialog-shadcn";
import { Button } from "@/components/ui/shadcn-button";
import { useGetImage, useGetImages } from "@/queries/customer-queries";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { ImageIcon } from "@/components/common/icons";
import { Input } from "@/components/ui/input";
import { ACCEPTED_IMAGE_TYPES } from "../add-new-product";

interface ImageSelectorProps {
    allImages: string[];
    onMainImageSelect: (imagePath: string | null) => void;
    onAdditionalImagesSelect: (imagePaths: string[]) => void;
    file: File | null;
    additionalFiles: File[];
    handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleAdditionalFilesChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    removeAdditionalFile: (index: number) => void;
    selectedMainImage: string | null;
    selectedAdditionalImages: string[];
}

export function ImageSelector({
    allImages,
    onMainImageSelect,
    onAdditionalImagesSelect,
    file,
    additionalFiles,
    handleFileChange,
    handleAdditionalFilesChange,
    removeAdditionalFile,
    selectedMainImage,
    selectedAdditionalImages
}: ImageSelectorProps) {
    const [open, setOpen] = useState(false);
    const [mainImage, setMainImage] = useState<string | null>(selectedMainImage);
    const [additionalImages, setAdditionalImages] = useState<string[]>(selectedAdditionalImages);
    const [searchTerm, setSearchTerm] = useState("");
    const [mainImageChanged, setMainImageChanged] = useState(false);

    // Update local state when props change
    useEffect(() => {
        setMainImage(selectedMainImage);
        setMainImageChanged(false);
    }, [selectedMainImage]);

    useEffect(() => {
        setAdditionalImages(selectedAdditionalImages);
    }, [selectedAdditionalImages]);

    // Fetch image URLs for the preview
    const imageRequests = useGetImages(allImages);
    const imageMap = new Map<string, string>();

    allImages.forEach((path, index) => {
        if (imageRequests[index].data) {
            imageMap.set(path, imageRequests[index].data as string);
        }
    });

    // Filter images based on search term
    const filteredImages = allImages.filter(imagePath =>
        imagePath.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const handleMainImageChange = (imagePath: string) => {
        setMainImageChanged(true);
        setMainImage(imagePath === mainImage ? null : imagePath);
    };

    const handleAdditionalImageChange = (imagePath: string) => {
        setAdditionalImages(prev => {
            if (prev.includes(imagePath)) {
                return prev.filter(path => path !== imagePath);
            } else {
                return [...prev, imagePath];
            }
        });
    };

    const handleApply = () => {
        // Only call onMainImageSelect if the main image was explicitly changed
        if (mainImageChanged) {
            onMainImageSelect(mainImage);
        }

        // Always update additional images
        onAdditionalImagesSelect(additionalImages);
        setOpen(false);
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button
                    type="button"
                    variant="outline"
                    className="flex items-center gap-2 mb-4"
                >
                    <ImageIcon className="h-4 w-4" />
                    Upload Images
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-auto">
                <DialogHeader>
                    <DialogTitle>Select Images</DialogTitle>
                </DialogHeader>
                <DialogDescription>
                    Select images for your product. You can either upload new images or select existing images from your library.
                </DialogDescription>

                <Tabs defaultValue="existing" className="mt-4">
                    <TabsList className="mb-4">
                        <TabsTrigger value="existing">Existing Images</TabsTrigger>
                        <TabsTrigger value="upload">Upload New</TabsTrigger>
                    </TabsList>

                    <TabsContent value="existing" className="space-y-4">
                        <div className="flex flex-col space-y-4">
                            <Input
                                type="text"
                                placeholder="Search images..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="mb-4"
                            />

                            <div className="grid grid-cols-2 gap-4 mb-4">
                                <div>
                                    <Label className="mb-2 block font-semibold">Main Image</Label>
                                    <p className="text-sm text-gray-500 mb-4">
                                        Select one image as the main product image
                                    </p>
                                    <div className="grid grid-cols-3 gap-2 max-h-[400px] overflow-y-auto p-2 border rounded-md">
                                        {filteredImages.map((imagePath) => (
                                            <div
                                                key={`main-${imagePath}`}
                                                className={`
                          relative cursor-pointer border rounded-md p-1
                          ${mainImage === imagePath ? 'ring-2 ring-primary' : ''}
                        `}
                                                onClick={() => handleMainImageChange(imagePath)}
                                            >
                                                {imageMap.get(imagePath) ? (
                                                    <img
                                                        src={imageMap.get(imagePath) || ''}
                                                        alt={imagePath}
                                                        className="w-full h-24 object-cover rounded-md"
                                                    />
                                                ) : (
                                                    <div className="w-full h-24 bg-gray-100 flex items-center justify-center rounded-md">
                                                        <ImageIcon className="h-8 w-8 text-gray-400" />
                                                    </div>
                                                )}
                                                <div className="absolute top-2 left-2">
                                                    <input
                                                        type="radio"
                                                        checked={mainImage === imagePath}
                                                        onChange={() => handleMainImageChange(imagePath)}
                                                        className="h-4 w-4"
                                                    />
                                                </div>
                                                <div className="text-xs truncate mt-1 text-center">
                                                    {imagePath.split('/').pop()?.substring(0, 15) || ''}
                                                    {(imagePath.split('/').pop()?.length || 0) > 15 ? '...' : ''}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                <div>
                                    <Label className="mb-2 block font-semibold">Additional Images</Label>
                                    <p className="text-sm text-gray-500 mb-4">
                                        Select multiple images as additional product images
                                    </p>
                                    <div className="grid grid-cols-3 gap-2 max-h-[400px] overflow-y-auto p-2 border rounded-md">
                                        {filteredImages.map((imagePath) => (
                                            <div
                                                key={`additional-${imagePath}`}
                                                className={`
                          relative cursor-pointer border rounded-md p-1
                          ${additionalImages.includes(imagePath) ? 'ring-2 ring-primary' : ''}
                        `}
                                                onClick={() => handleAdditionalImageChange(imagePath)}
                                            >
                                                {imageMap.get(imagePath) ? (
                                                    <img
                                                        src={imageMap.get(imagePath) || ''}
                                                        alt={imagePath}
                                                        className="w-full h-24 object-cover rounded-md"
                                                    />
                                                ) : (
                                                    <div className="w-full h-24 bg-gray-100 flex items-center justify-center rounded-md">
                                                        <ImageIcon className="h-8 w-8 text-gray-400" />
                                                    </div>
                                                )}
                                                <div className="absolute top-2 left-2">
                                                    <input
                                                        type="checkbox"
                                                        checked={additionalImages.includes(imagePath)}
                                                        onChange={() => handleAdditionalImageChange(imagePath)}
                                                        className="h-4 w-4"
                                                    />
                                                </div>
                                                <div className="text-xs truncate mt-1 text-center">
                                                    {imagePath.split('/').pop()?.substring(0, 15) || ''}
                                                    {(imagePath.split('/').pop()?.length || 0) > 15 ? '...' : ''}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </TabsContent>

                    <TabsContent value="upload" className="space-y-4">
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="mainImageUpload">Main Image</Label>
                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 mt-2">
                                    <div>
                                        <Input
                                            type="file"
                                            id="mainImageUpload"
                                            accept={ACCEPTED_IMAGE_TYPES.join(",")}
                                            onChange={handleFileChange}
                                            className="cursor-pointer"
                                        />
                                        <p className="mt-2 text-sm text-gray-500">
                                            Upload a product image (PNG, JPG, GIF up to 10MB)
                                        </p>
                                    </div>
                                    <div className="flex items-center justify-center">
                                        {file && (
                                            <div className="relative h-32 w-32 overflow-hidden rounded-md border">
                                                <img
                                                    src={URL.createObjectURL(file)}
                                                    alt="Product preview"
                                                    className="h-full w-full object-cover"
                                                />
                                            </div>
                                        )}
                                        {!file && (
                                            <div className="flex h-32 w-32 items-center justify-center rounded-md border bg-neutral-50">
                                                <ImageIcon className="h-12 w-12 text-gray-300" />
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="additionalImagesUpload">Additional Images</Label>
                                <div className="space-y-4">
                                    <Input
                                        type="file"
                                        id="additionalImagesUpload"
                                        accept={ACCEPTED_IMAGE_TYPES.join(",")}
                                        onChange={handleAdditionalFilesChange}
                                        className="cursor-pointer"
                                        multiple
                                    />
                                    <p className="text-sm text-gray-500">
                                        Upload additional product images (PNG, JPG, GIF up to 10MB each)
                                    </p>

                                    {additionalFiles.length > 0 && (
                                        <div className="grid grid-cols-2 sm:grid-cols-3 gap-4 mt-4">
                                            {additionalFiles.map((file, index) => (
                                                <div key={index} className="relative">
                                                    <div className="relative h-24 w-full overflow-hidden rounded-md border">
                                                        <img
                                                            src={URL.createObjectURL(file)}
                                                            alt={`Additional image ${index + 1}`}
                                                            className="h-full w-full object-cover"
                                                        />
                                                    </div>
                                                    <Button
                                                        type="button"
                                                        variant="destructive"
                                                        size="icon"
                                                        className="absolute -top-2 -right-2 h-6 w-6 rounded-full"
                                                        onClick={() => removeAdditionalFile(index)}
                                                    >
                                                        <span className="sr-only">Remove image</span>
                                                        ✕
                                                    </Button>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </TabsContent>
                </Tabs>

                <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                        Cancel
                    </Button>
                    <Button type="button" onClick={handleApply}>
                        Apply
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}

export function SelectedImagesPreview({
    mainImageUrl,
    filePreview,
    additionalImageUrls,
    file,
    additionalFiles,
}: {
    mainImageUrl: string | null;
    filePreview?: string | null;
    additionalImageUrls: string[];
    file: File | null;
    additionalFiles: File[];
}) {
    return (
        <div className="space-y-4">
            <div>
                <Label className="mb-2 block">Main Image</Label>
                <div className="flex items-center justify-center">
                    {file ? (
                        <div className="relative h-40 w-40 overflow-hidden rounded-md border">
                            <img
                                src={URL.createObjectURL(file)}
                                alt="Main product image"
                                className="h-full w-full object-cover"
                            />
                        </div>
                    ) : filePreview ? (
                        <div className="relative h-40 w-40 overflow-hidden rounded-md border">
                            <img
                                src={filePreview}
                                alt="Main product image"
                                className="h-full w-full object-cover"
                            />
                        </div>
                    ) : mainImageUrl ? (
                        <div className="relative h-40 w-40 overflow-hidden rounded-md border">
                            <img
                                src={mainImageUrl}
                                alt="Main product image"
                                className="h-full w-full object-cover"
                            />
                        </div>
                    ) : (
                        <div className="flex h-40 w-40 items-center justify-center rounded-md border bg-neutral-50">
                            <ImageIcon className="h-16 w-16 text-gray-300" />
                        </div>
                    )}
                </div>
            </div>

            {(additionalFiles.length > 0 || additionalImageUrls.length > 0) && (
                <div>
                    <Label className="mb-2 block">Additional Images</Label>
                    <div className="grid grid-cols-3 sm:grid-cols-4 gap-4 mt-2">
                        {additionalFiles.map((file, index) => (
                            <div key={`file-${index}`} className="relative h-24 overflow-hidden rounded-md border">
                                <img
                                    src={URL.createObjectURL(file)}
                                    alt={`Additional image ${index + 1}`}
                                    className="h-full w-full object-cover"
                                />
                            </div>
                        ))}
                        {additionalImageUrls.map((url, index) => (
                            <div key={`url-${index}`} className="relative h-24 overflow-hidden rounded-md border">
                                <img
                                    src={url}
                                    alt={`Additional image ${index + 1}`}
                                    className="h-full w-full object-cover"
                                />
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
} 