import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiResponse } from "next";
import { GetOrderByIdResponse } from "../../customers/[id]/orders/[order_id]";

export default checkAdmin(
    matchRoute({
        GET: handler
    })
);

async function handler(req: NextApiRequestWithUserContext, res: NextApiResponse<GetOrderByIdResponse>) {
    const { id } = req.query;

    if (!id || typeof id !== "string") {
        return res.status(400).json({ error: "Invalid order ID" });
    }

    const supabaseAdminClient = createSupabaseAdminClient();

    const { data, error } = await supabaseAdminClient
        .from("orders")
        .select(`
            *,
            order_items(
                id, 
                quantity, 
                options, 
                products(
                    id, 
                    name, 
                    price, 
                    image
                )
            ),
            shipping_addresses(
                id,
                contact_name,
                address,
                city,
                state,
                zip_code
            ),
            order_statuses(id, status, created_at)
        `)
        .eq("id", id)
        .single();

    if (error) {
        console.error("Error fetching order:", error);
        return res.status(400).json({ error: error.message });
    }

    if (!data) {
        return res.status(404).json({ error: "Order not found" });
    }

    // Sort order_statuses by created_at
    if (data.order_statuses && Array.isArray(data.order_statuses)) {
        data.order_statuses.sort((a, b) => 
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
    }

    return res.status(200).json({ order: data });
}
