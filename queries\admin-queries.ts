import { GetDashboardDataResponse } from "@/pages/api/admin/dashboard";
import { SalesReportResponse } from "@/pages/api/admin/reports/sales";
import {
  ApproveBillingAddressResponse,
  GetBillingAddressesResponse,
} from "@/pages/api/billing-addresses";
import {
  ApproveBillingAddress,
  GetPendingBillingAddressesResponse,
} from "@/pages/api/billing-addresses/pending";
import {
  CreateCategoryRequest,
  CreateCategoryResponse,
  GetAllCategoriesResponse,
} from "@/pages/api/categories";
import {
  DeleteCategoryResponse,
  UpdateCategoryRequest,
  UpdateCategoryResponse,
} from "@/pages/api/categories/[id]";
import {
  AssignGroupToCustomerRequest,
  CreateCustomerGroupResponse,
  DeleteCustomerGroupRequest,
  DeleteCustomerGroupResponse,
  GetCustomerGroupsResponse,
} from "@/pages/api/customer-groups";
import { GetCustomersResponse } from "@/pages/api/customers";
import { GetOrderByIdResponse } from "@/pages/api/customers/[id]/orders/[order_id]";
import {
  CreateDiscountRequest,
  CreateDiscountResponse,
  GetDiscountsResponse,
} from "@/pages/api/discounts";
import {
  CreateGroupRequest,
  CreateGroupResponse,
  GetGroupsResponse,
} from "@/pages/api/groups";
import { GetOrdersResponse } from "@/pages/api/orders";
import {
  UpdateOrderStatusRequest,
  UpdateOrderStatusResponse,
} from "@/pages/api/orders/[id]/status";
import {
  GetCancellationRequestsResponse,
  UpdateCancellationRequest,
  UpdateCancellationRequestResponse,
} from "@/pages/api/orders/cancellation-requests";
import {
  CreateProductResponse,
  GetAllProductsResponse,
} from "@/pages/api/products";
import { CreateProductCategoryResponse } from "@/pages/api/products/[slug]/categories";
import { DeleteProductCategoryResponse } from "@/pages/api/products/[slug]/categories/[category_id]";
import { GetAllUsersResponse } from "@/pages/api/users";
import {
  UpdateUserStatusRequest,
  UpdateUserStatusResponse,
} from "@/pages/api/users/[slug]/status";
import { GetPendingUsersResponse } from "@/pages/api/users/pending";
import useAuthStore from "@/stores/auth-store";
import { Product } from "@/supabase/types";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

async function getCustomers(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const response = await fetch(`/api/customers?page=${page}&limit=${limit}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  const data = (await response.json()) as GetCustomersResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useGetCustomersQuery(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated);
  return useQuery({
    queryKey: ["customers", page, limit],
    queryFn: () => getCustomers(page, limit, token),
    enabled: !!token && isAuthenticated,
  });
}

async function getAllProducts(
  page: number = 1,
  limit: number = 10,
  token: string,
  search?: string,
) {
  const url = new URL("/api/products", window.location.origin);
  url.searchParams.append("page", page.toString());
  url.searchParams.append("limit", limit.toString());
  if (search) url.searchParams.append("search", search);

  const response = await fetch(url.toString(), {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  const data = (await response.json()) as GetAllProductsResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useGetAllProductsQuery(
  page: number = 1,
  limit: number = 10,
  token: string,
  search?: string,
) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated);
  return useQuery({
    queryKey: ["products", page, limit, search],
    queryFn: () => getAllProducts(page, limit, token, search),
    enabled: !!token && isAuthenticated,
  });
}

async function createProduct(product: Partial<Product>, token: string) {
  const response = await fetch("/api/products", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(product),
  });

  const data = (await response.json()) as CreateProductResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function createProductMutation(token: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (product: Partial<Product>) => createProduct(product, token),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["products", "paginated-products"],
      });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function getDiscounts(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const response = await fetch(`/api/discounts?page=${page}&limit=${limit}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  const data = (await response.json()) as GetDiscountsResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useGetDiscountsQuery(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated);
  return useQuery({
    queryKey: ["discounts", page, limit],
    queryFn: () => getDiscounts(page, limit, token),
    enabled: !!token && isAuthenticated,
  });
}

async function createDiscount(discount: CreateDiscountRequest, token: string) {
  const response = await fetch("/api/discounts", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(discount),
  });

  const data = (await response.json()) as CreateDiscountResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function createDiscountMutation(
  discount: CreateDiscountRequest,
  token: string,
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => createDiscount(discount, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["discounts"] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function getGroups(page: number = 1, limit: number = 10, token: string) {
  const response = await fetch(`/api/groups?page=${page}&limit=${limit}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  const data = (await response.json()) as GetGroupsResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useGetGroupsQuery(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated);
  return useQuery({
    queryKey: ["groups", page, limit],
    queryFn: () => getGroups(page, limit, token),
    enabled: !!token && isAuthenticated,
  });
}

async function createGroup(group: CreateGroupRequest, token: string) {
  const response = await fetch("/api/groups", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(group),
  });

  const data = (await response.json()) as CreateGroupResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useCreateGroupMutation(
  initialValues: CreateGroupRequest,
  token: string,
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (values: CreateGroupRequest = initialValues) =>
      createGroup(values, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["groups"] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function getCustomerGroups(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const response = await fetch(
    `/api/customer-groups?page=${page}&limit=${limit}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    },
  );

  const data = (await response.json()) as GetCustomerGroupsResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useGetCustomerGroupsQuery(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated);

  return useQuery({
    queryKey: ["customer-groups", page, limit],
    queryFn: () => getCustomerGroups(page, limit, token),
    enabled: !!token && isAuthenticated,
  });
}

async function createCustomerGroup(
  customerGroup: AssignGroupToCustomerRequest,
  token: string,
) {
  const response = await fetch("/api/customer-groups", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(customerGroup),
  });

  const data = (await response.json()) as CreateCustomerGroupResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useCreateCustomerGroupMutation(token: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (customerGroup: AssignGroupToCustomerRequest) =>
      createCustomerGroup(customerGroup, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["customer-groups"] });
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.invalidateQueries({ queryKey: ["customers"] });
      queryClient.invalidateQueries({ queryKey: ["groups"] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function getBillingAddresses(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const response = await fetch(
    `/api/billing-addresses?page=${page}&limit=${limit}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  const data = (await response.json()) as GetBillingAddressesResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useGetBillingAddressesQuery(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated);

  return useQuery({
    queryKey: ["billing-addresses", page, limit],
    queryFn: () => getBillingAddresses(page, limit, token),
    enabled: !!token && isAuthenticated,
  });
}

async function approveBillingAddress(
  dto: ApproveBillingAddress,
  token: string,
) {
  const response = await fetch(`/api/billing-addresses/pending`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(dto),
  });

  const data = (await response.json()) as ApproveBillingAddressResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useApproveBillingAddressMutation(token: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (dto: ApproveBillingAddress) =>
      approveBillingAddress(dto, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["billing-addresses"] });
      queryClient.invalidateQueries({
        queryKey: ["pending-billing-addresses"],
      });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function getDashboardData(token: string) {
  const response = await fetch("/api/admin/dashboard", {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  const data = (await response.json()) as GetDashboardDataResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useGetDashboardDataQuery(token: string) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated);
  return useQuery({
    queryKey: ["dashboard-data"],
    queryFn: () => getDashboardData(token),
    enabled: !!token && isAuthenticated,
  });
}

async function getAllUsers(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const response = await fetch(`/api/users?page=${page}&limit=${limit}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  const data = (await response.json()) as GetAllUsersResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useGetAllUsersQuery(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated);
  return useQuery({
    queryKey: ["users", page, limit],
    queryFn: () => getAllUsers(page, limit, token),
    enabled: !!token && isAuthenticated,
  });
}

async function searchUsers(
  query: string,
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const response = await fetch(
    `/api/users/search?query=${encodeURIComponent(
      query,
    )}&page=${page}&limit=${limit}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  const data = (await response.json()) as GetAllUsersResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useSearchUsersQuery(
  query: string,
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated());

  return useQuery({
    queryKey: ["users", "search", query, page, limit],
    queryFn: () => searchUsers(query, page, limit, token),
    enabled: !!token && isAuthenticated && !!query,
  });
}

async function updateUserStatus(id: string, request: UpdateUserStatusRequest, token: string) {
  const response = await fetch(`/api/users/${id}/status`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(request),
  });

  const data = (await response.json()) as UpdateUserStatusResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useUpdateUserStatusMutation(id: string, token: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: UpdateUserStatusRequest) =>
      updateUserStatus(id, request, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function getOrders(
  page: number = 1,
  limit: number = 10,
  token: string,
  search?: string,
  status?: string,
) {
  let url = `/api/orders?page=${page}&limit=${limit}`;

  if (search) {
    url += `&search=${encodeURIComponent(search)}`;
  }

  if (status) {
    url += `&status=${encodeURIComponent(status)}`;
  }

  const response = await fetch(url, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  const data = (await response.json()) as GetOrdersResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useGetOrdersQuery(
  page: number = 1,
  limit: number = 10,
  token: string,
  search?: string,
  status?: string,
) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated);

  return useQuery({
    queryKey: ["orders", page, limit, search, status],
    queryFn: () => getOrders(page, limit, token, search, status),
    enabled: !!token && isAuthenticated,
  });
}

async function updateOrderStatus(
  orderId: string,
  updateData: UpdateOrderStatusRequest,
  token: string,
) {
  const response = await fetch(`/api/orders/${orderId}/status`, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(updateData),
  });

  const data = (await response.json()) as UpdateOrderStatusResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useUpdateOrderStatusMutation(orderId: string, token: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (updateData: UpdateOrderStatusRequest) =>
      updateOrderStatus(orderId, updateData, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["orders"] });
      queryClient.invalidateQueries({ queryKey: ["order", orderId] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function getAllCategories(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const response = await fetch(`/api/categories?page=${page}&limit=${limit}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  const data = (await response.json()) as GetAllCategoriesResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useGetAllCategoriesQuery(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated);

  return useQuery({
    queryKey: ["categories", page, limit],
    queryFn: () => getAllCategories(page, limit, token),
    enabled: !!token && isAuthenticated,
  });
}

async function getPendingBillingAddresses(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const response = await fetch(
    `/api/billing-addresses/pending?page=${page}&limit=${limit}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  const data = (await response.json()) as GetPendingBillingAddressesResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useGetPendingBillingAddressesQuery(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated);

  return useQuery({
    queryKey: ["pending-billing-addresses", page, limit],
    queryFn: () => getPendingBillingAddresses(page, limit, token),
    enabled: !!token && isAuthenticated,
  });
}

async function getCancellationRequests(
  page: number = 1,
  limit: number = 10,
  token: string,
  search?: string,
) {
  let url = `/api/orders/cancellation-requests?page=${page}&limit=${limit}`;

  if (search) {
    url += `&search=${encodeURIComponent(search)}`;
  }

  const response = await fetch(url, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  const data = (await response.json()) as GetCancellationRequestsResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useGetCancellationRequestsQuery(
  page: number = 1,
  limit: number = 10,
  token: string,
  search?: string,
) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated);

  return useQuery({
    queryKey: ["cancellation-requests", page, limit, search],
    queryFn: () => getCancellationRequests(page, limit, token, search),
    enabled: !!token && isAuthenticated,
  });
}

async function updateCancellationRequest(
  dto: UpdateCancellationRequest,
  token: string,
) {
  const response = await fetch(`/api/orders/cancellation-requests`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(dto),
  });

  const data = (await response.json()) as UpdateCancellationRequestResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useUpdateCancellationRequestMutation(token: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (dto: UpdateCancellationRequest) =>
      updateCancellationRequest(dto, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["cancellation-requests"] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function createCategory(dto: CreateCategoryRequest, token: string) {
  const response = await fetch("/api/categories", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(dto),
  });

  const data = (await response.json()) as CreateCategoryResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useCreateCategoryMutation(token: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (dto: CreateCategoryRequest) => createCategory(dto, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["categories"] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function deleteCategory(id: string, token: string) {
  const response = await fetch(`/api/categories/${id}`, {
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });

  const data = (await response.json()) as DeleteCategoryResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useDeleteCategoryMutation(token: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteCategory(id, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["categories"] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function updateCategory(
  id: string,
  dto: UpdateCategoryRequest,
  token: string,
) {
  const response = await fetch(`/api/categories/${id}`, {
    method: "PATCH",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(dto),
  });

  const data = (await response.json()) as UpdateCategoryResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useUpdateCategoryMutation(id: string, token: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (dto: UpdateCategoryRequest) => updateCategory(id, dto, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["categories"] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function deleteProductCategory(
  productId: string,
  categoryId: string,
  token: string,
) {
  const response = await fetch(
    `/api/products/${productId}/categories/${categoryId}`,
    {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    },
  );

  const data = (await response.json()) as DeleteProductCategoryResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useDeleteProductCategoryMutation(
  productId: string,
  token: string,
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (categoryId: string) =>
      deleteProductCategory(productId, categoryId, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function createProductCategory(
  productId: string,
  categoryIds: string[],
  token: string,
) {
  const response = await fetch(`/api/products/${productId}/categories`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ category_ids: categoryIds }),
  });

  const data = (await response.json()) as CreateProductCategoryResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useCreateProductCategoryMutation(
  productId: string,
  token: string,
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (categoryIds: string[]) =>
      createProductCategory(productId, categoryIds, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function deleteCustomerGroup(
  dto: DeleteCustomerGroupRequest,
  token: string,
) {
  const response = await fetch(`/api/customer-groups`, {
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(dto),
  });

  const data = (await response.json()) as DeleteCustomerGroupResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useDeleteCustomerGroupMutation(token: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (dto: DeleteCustomerGroupRequest) =>
      deleteCustomerGroup(dto, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["customers"] });
      queryClient.invalidateQueries({ queryKey: ["groups"] });
      queryClient.invalidateQueries({ queryKey: ["customer-groups"] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function getPendingUsers(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const response = await fetch(
    `/api/users/pending?page=${page}&limit=${limit}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  const data = (await response.json()) as GetPendingUsersResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useGetPendingUsersQuery(
  page: number = 1,
  limit: number = 10,
  token: string,
) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated);
  return useQuery({
    queryKey: ["pending-users", page, limit],
    queryFn: () => getPendingUsers(page, limit, token),
    enabled: !!token && isAuthenticated,
  });
}

// Customer category operations
export interface CustomerCategoriesResponse {
  error?: string;
  customer_categories?: any[];
  customer_category?: any;
  message?: string;
}

async function getCustomerCategories(customerId: string, token: string) {
  const response = await fetch(`/api/customers/${customerId}/categories`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  const data = (await response.json()) as CustomerCategoriesResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data.customer_categories;
}

export function useGetCustomerCategoriesQuery(
  customerId: string,
  token: string,
) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated());

  return useQuery({
    queryKey: ["customer-categories", customerId],
    queryFn: () => getCustomerCategories(customerId, token),
    enabled: !!token && isAuthenticated && !!customerId,
  });
}

async function addCustomerCategories(
  customerId: string,
  categoryType: "all_products" | "support_products",
  token: string,
) {
  const response = await fetch(`/api/customers/${customerId}/categories`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({ category_type: categoryType }),
  });

  const data = (await response.json()) as CustomerCategoriesResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data.customer_categories;
}

export function useAddCustomerCategoryMutation(token: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      customerId,
      categoryType,
    }: {
      customerId: string;
      categoryType: "all_products" | "support_products";
    }) => addCustomerCategories(customerId, categoryType, token),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["customer-categories", variables.customerId],
      });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function deleteCustomerCategory(
  customerId: string,
  categoryId: string,
  token: string,
) {
  const response = await fetch(`/api/customers/${customerId}/categories`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({ category_id: categoryId }),
  });

  const data = (await response.json()) as CustomerCategoriesResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data.message;
}

export function useDeleteCustomerCategoryMutation(token: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      customerId,
      categoryId,
    }: {
      customerId: string;
      categoryId: string;
    }) => deleteCustomerCategory(customerId, categoryId, token),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["customer-categories", variables.customerId],
      });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

// Update product function and mutation hook
async function updateProduct(
  productId: string,
  product: Partial<Product>,
  token: string,
) {
  const response = await fetch(`/api/products/${productId}`, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(product),
  });

  const data = await response.json();

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useUpdateProductMutation(productId: string, token: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (product: Partial<Product>) =>
      updateProduct(productId, product, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
      queryClient.invalidateQueries({ queryKey: ["product", productId] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

async function getOrderById(orderId: string, token: string) {
  const response = await fetch(`/api/orders/${orderId}`, {
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });

  const data = (await response.json()) as GetOrderByIdResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data.order;
}

export function useGetOrderQuery(orderId: string, token: string) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated)();

  return useQuery({
    queryKey: ["order", orderId],
    queryFn: () => getOrderById(orderId, token),
    enabled: !!token && isAuthenticated && !!orderId,
  });
}

// Sales Reports Query
async function getSalesReport(
  token: string,
  period: "day" | "week" | "month",
  range?: string,
  startDate?: string,
  endDate?: string,
) {
  const queryParams = new URLSearchParams();
  queryParams.append("period", period);
  if (range) queryParams.append("range", range);
  if (startDate) queryParams.append("startDate", startDate);
  if (endDate) queryParams.append("endDate", endDate);

  const response = await fetch(
    `/api/admin/reports/sales?${queryParams.toString()}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  const data = (await response.json()) as SalesReportResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data;
}

export function useGetSalesReportQuery(
  token: string,
  period: "day" | "week" | "month" = "day",
  range?: string,
  startDate?: string,
  endDate?: string,
) {
  const isAuthenticated = useAuthStore((data) => data.isAuthenticated);
  return useQuery({
    queryKey: ["sales-report", period, range, startDate, endDate],
    queryFn: () => getSalesReport(token, period, range, startDate, endDate),
    enabled: !!token && isAuthenticated,
  });
}

// Update user notes
export interface UpdateUserNotesRequest {
  notes: string;
}

export interface UpdateUserNotesResponse {
  error?: string;
  success?: boolean;
}

async function updateUserNotes(
  id: string,
  data: UpdateUserNotesRequest,
  token: string,
) {
  const response = await fetch(`/api/admin/users/${id}/notes`, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  const responseData = (await response.json()) as UpdateUserNotesResponse;

  if (responseData.error) {
    throw new Error(responseData.error);
  }

  return responseData;
}

export function useUpdateUserNotesMutation(id: string, token: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateUserNotesRequest) =>
      updateUserNotes(id, data, token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.invalidateQueries({ queryKey: ["user", id] });
    },
    onError: (error) => {
      return error.message;
    },
  });
}
