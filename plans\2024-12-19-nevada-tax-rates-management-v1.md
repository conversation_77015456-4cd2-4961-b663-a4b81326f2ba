# Nevada Tax Rates Management System

## Objective
Create an endpoint to manage Nevada tax rates and replace hardcoded NEVADA_TAX_RATES constant in the checkout process with dynamic database-driven tax rate calculation. Add an admin settings sub-page for managing tax rates with full CRUD operations.

## Implementation Plan

1. **Create Tax Rates API Endpoints**
   - Dependencies: None
   - Notes: Follow existing API patterns, implement GET and PATCH/PUT endpoints with proper authentication
   - Files: `/pages/api/tax-rates/index.ts`, `/pages/api/tax-rates/[id].ts`
   - Status: Not Started

2. **Export NevadaTaxRate Type**
   - Dependencies: None (can run parallel with Task 1)
   - Notes: Add type export based on nevada_tax_rates table schema from database.types.ts
   - Files: `supabase/types.ts`
   - Status: Not Started

3. **Create Tax Rate Query Hooks**
   - Dependencies: Task 1, Task 2
   - Notes: Follow queries/customer-queries.ts pattern, implement useQuery and useMutation hooks
   - Files: `queries/customer-queries.ts`
   - Status: Not Started

4. **Update Checkout Tax Calculation**
   - Dependencies: Task 3
   - Notes: Replace hardcoded getTaxRateForCity function calls with API-driven data, maintain fallback to prevent breaking changes
   - Files: `pages/store/checkout.tsx`, potentially update `lib/utils/nevada-tax-rates.ts`
   - Status: Not Started

5. **Create Tax Rate Management Page**
   - Dependencies: Task 3
   - Notes: Use shadcn components (Table, Dialog, Button), implement data table with edit functionality
   - Files: `pages/admin/settings/tax-rates.tsx`
   - Status: Not Started

6. **Add Tax Rates to Settings Navigation**
   - Dependencies: Task 5
   - Notes: Add navigation item to settings layout and overview page
   - Files: `components/features/admin/settings/layout.tsx`, `pages/admin/settings/index.tsx`
   - Status: Not Started

7. **Implement Data Validation and Error Handling**
   - Dependencies: Task 1, Task 4, Task 5
   - Notes: Tax rate validation (0-100%), error states, loading states
   - Files: API endpoints, form validation, UI components
   - Status: Not Started

8. **Database Data Migration**
   - Dependencies: All previous tasks
   - Notes: Seed nevada_tax_rates table with current hardcoded values if empty
   - Files: Database seeding script or manual data entry
   - Status: Not Started

9. **Middleware**
   - The PATCH/PUT `api/tax-rates/[id]` endpoint should have a middleware (checkAdmin) that checks if the user has the necessary permissions to update the tax rate.
   - Use the matchRoute to export the handler.

## Verification Criteria
- Tax rates API endpoints respond correctly with proper authentication
- NevadaTaxRate type is properly exported and used throughout the application
- Checkout page calculates tax using database values instead of hardcoded constants
- Admin settings page displays tax rates table with edit functionality
- Tax rate updates reflect immediately in checkout calculations (with proper cache invalidation)
- Form validation prevents invalid tax rate values
- Error handling gracefully manages API failures
- Navigation includes tax rates management option in admin settings

## Potential Risks and Mitigations

1. **Database Query Failure Breaking Checkout**
   Mitigation: Implement fallback to hardcoded values if API call fails, with proper error logging

2. **Cache Invalidation Issues with React Query**
   Mitigation: Use proper queryKey structure and invalidateQueries after mutations

3. **Invalid Tax Rate Data Entry**
   Mitigation: Implement client and server-side validation (tax rates 0-100%, proper decimal format)

4. **Unauthorized Access to Tax Rate Management**
   Mitigation: Follow existing admin middleware pattern for route protection

5. **Performance Impact from Additional API Calls**
   Mitigation: Use React Query caching, consider implementing localStorage fallback for repeated checkout sessions

## Alternative Approaches

3. **Bulk Edit Interface**: Instead of individual edit dialogs, implement inline editing for multiple tax rates at once