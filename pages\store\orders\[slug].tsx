import DownloadAttachmentButton from "@/components/features/store/download-attachment-button"
import StoreLayout from "@/components/features/store/layout"
import { OrderStatusBadge } from "@/components/features/store/orders-table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/shadcn-button"
import { Skeleton } from "@/components/ui/skeleton"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { toast } from "@/hooks/use-toast"
import { formatPrice } from "@/lib/utils"
import { OrderItemOption, OrderItemsWithProduct, useGetImage, useGetOrderByIdQuery } from "@/queries/customer-queries"
import useAuthStore from "@/stores/auth-store"
import useCartStore from "@/stores/cart-store"
import { ArrowLeft, CreditCard, ExternalLink, FileText, Package, ShoppingCart, Truck } from "lucide-react"
import Head from "next/head"
import Link from "next/link"
import { useRouter } from "next/router"
import { useEffect, useState } from "react"

export default function OrderDetails() {
  const router = useRouter();
  const { slug } = router.query;

  const [isLoading, setIsLoading] = useState(true);

  const userData = useAuthStore(state => state.data);
  const userId = userData.id;
  const order = useGetOrderByIdQuery(userId, slug as string);
  const orderData = order.data;
  const orderedDate = new Date(orderData?.created_at ?? Date.now());
  const orderShipping = orderData?.shipping_addresses;
  const orderItems = orderData?.order_items;

  const addItem = useCartStore((state) => state.addItem);
  const totalAmount = orderData?.total_amount ?? 0;

  const formattedDate = Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "numeric",
  }).format(orderedDate);

  const reorderHandler = () => {
    if (!orderItems) return;

    orderItems.forEach((item) => {
      const itemPrice = item.item_price ?? item.products.price;

      addItem({
        id: item.products.id,
        name: item.products.name,
        price: itemPrice,
        quantity: item.quantity,
        image: item.products.image ?? "",
        sku: item.products.sku ?? "",
        selected: false,
        discount: 0,
        selectedOptions: item.options?.map(option => ({
          name: option.name,
          value: option.value,
          price: option.price
        })) || []
      });
    });

    toast({
      title: 'Items added to cart',
      description: 'Items from this order have been added to your cart.',
      variant: "success",
      duration: 3000
    });
  }

  useEffect(function handleOrderLoading() {
    if (order.isSuccess) {
      setIsLoading(false);
    }
  }, [order.isSuccess]);

  return (
    <StoreLayout>
      <Head>
        <title>Order Details | #{orderData?.id.split('-').at(0)}...{orderData?.id.split('-').at(-1)}</title>
      </Head>
      <div className="min-h-screen bg-white">
        <div className="flex-1 space-y-8 p-8 pt-6">
          {/* Header Section */}
          <div className="flex flex-col space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button variant="ghost" size="icon" className="shrink-0" asChild>
                  <Link href="/store/orders">
                    <ArrowLeft className="h-5 w-5" />
                    <span className="sr-only">Back to orders</span>
                  </Link>
                </Button>
                <div>
                  <h1 className="text-3xl font-bold tracking-tight">Order Details</h1>
                  <p className="text-muted-foreground">
                    View and manage your order information
                  </p>
                </div>
              </div>
              {!isLoading && (
                <OrderStatusBadge status={orderData?.order_statuses?.[0]?.status ?? "pending"} />
              )}
            </div>
          </div>

          {isLoading ? (
            <OrderDetailsSkeleton />
          ) : (
            <div className="grid gap-6">
              {/* Quick Stats */}
              <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                <Card className="hover:shadow-md transition-all">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Order ID</CardTitle>
                    <Package className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">#{orderData?.id.split('-').at(0)}...{orderData?.id.split('-').at(-1)}</div>
                    <p className="text-xs text-muted-foreground">Placed on {formattedDate}</p>
                  </CardContent>
                </Card>
                <Card className="hover:shadow-md transition-all">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatPrice(totalAmount)}</div>
                    <p className="text-xs text-muted-foreground">
                      {orderData?.payment_type === "credit_card" ? "Paid with Credit Card" :
                        orderData?.payment_type === "purchase_order" ? "Purchase Order" : "Payment Method"}
                    </p>
                  </CardContent>
                </Card>
                <Card className="hover:shadow-md transition-all">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Delivery Method</CardTitle>
                    <Truck className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{orderData?.delivery_method || "Standard"}</div>
                    <p className="text-xs text-muted-foreground">
                      {orderData?.ship_collect ? "Ship Collect UPS" : "Standard Shipping"}
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Order Details Card */}
              <Card className="hover:shadow-md transition-all">
                <CardHeader>
                  <CardTitle>Order Items</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Order Items Table */}
                  <Table>
                    <TableHeader className="bg-muted/50">
                      <TableRow className="hover:bg-muted/50">
                        <TableHead className="w-[80px]">Image</TableHead>
                        <TableHead>Product</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead className="text-right">Price</TableHead>
                        <TableHead className="text-right">Options</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {orderItems && orderItems.length > 0 ? orderItems.map((item) => (
                        <OrderDetailItem key={`order-detail-item-${item.id}`} item={item} />
                      )) : (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center">
                            No items found.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>

                  <Separator />

                  {/* Shipping Details */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Shipping Details</h3>
                    <Card className="bg-muted/50">
                      <CardContent className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium">{orderShipping?.contact_name}</div>
                          <div className="text-sm text-muted-foreground">{orderShipping?.address}</div>
                          <div className="text-sm text-muted-foreground">
                            {orderShipping?.city}, {orderShipping?.state}, {orderShipping?.zip_code}
                          </div>
                          {orderData?.tracking_link && (
                            <div className="pt-3">
                              <Button variant="outline" size="sm" asChild className="gap-2">
                                <Link href={orderData.tracking_link} target="_blank" rel="noopener noreferrer">
                                  <ExternalLink className="h-4 w-4" />
                                  Track Order
                                </Link>
                              </Button>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <Separator />

                  {/* Payment Information */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Payment Information</h3>
                    <Card className="bg-muted/50">
                      <CardContent className="p-4 space-y-4">
                        {orderData?.payment_type === "credit_card" && (
                          <div className="flex items-center gap-2">
                            <CreditCard className="h-5 w-5 text-muted-foreground" />
                            <span>Paid with Credit Card</span>
                          </div>
                        )}

                        {orderData?.payment_type === "purchase_order" && (
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Package className="h-5 w-5 text-muted-foreground" />
                              <span>Purchase Order</span>
                            </div>
                            {orderData?.purchase_order && (
                              <div className="text-sm text-muted-foreground">
                                PO Number: {orderData.purchase_order}
                              </div>
                            )}
                            {orderData?.po_attachment && (
                              <div className="pt-2">
                                <DownloadAttachmentButton filePath={orderData.po_attachment} />
                              </div>
                            )}
                          </div>
                        )}

                        <div className="flex items-center gap-2">
                          <FileText className="h-5 w-5 text-muted-foreground" />
                          <div className="flex flex-col">
                            <span>Tax Status: {orderData?.tax_exempt ? "Tax Exempt" : "Taxable"}</span>
                            {(orderData as any)?.tax_rate && (orderData as any).tax_rate > 0 && (
                              <div className="text-sm text-muted-foreground mt-1">
                                Tax Rate: {(orderData as any).tax_rate.toFixed(3)}% • Tax Amount: {(orderData as any).tax_amount ? formatPrice((orderData as any).tax_amount) : "$0.00"}
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <Separator />

                  {/* Order Summary */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Order Summary</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Total Items</span>
                        <span>{orderData?.order_items.reduce((acc, item) => acc + item.quantity, 0)}</span>
                      </div>
                      {(orderData as any)?.tax_rate && (orderData as any).tax_rate > 0 && (
                        <>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">Subtotal</span>
                            <span>{formatPrice((totalAmount || 0) - ((orderData as any).tax_amount || 0))}</span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">
                              Tax ({(orderData as any).tax_rate.toFixed(3)}%)
                              {orderData?.tax_exempt && (
                                <span className="text-orange-600 ml-1">(Exempt)</span>
                              )}
                            </span>
                            <span className={orderData?.tax_exempt ? "line-through text-muted-foreground" : ""}>
                              {orderData?.tax_exempt ? "$0.00" : formatPrice((orderData as any).tax_amount || 0)}
                            </span>
                          </div>
                        </>
                      )}
                      <Separator />
                      <div className="flex items-center justify-between font-medium">
                        <span>Total Amount</span>
                        <span className="text-lg">{formatPrice(totalAmount)}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>

                {/* Actions */}
                <div className="p-6 bg-muted/50 rounded-b-lg">
                  <Button onClick={reorderHandler} className="w-full" size="lg">
                    <ShoppingCart className="mr-2 h-5 w-5" />
                    Reorder Items
                  </Button>
                </div>
              </Card>
            </div>
          )}
        </div>
      </div>
    </StoreLayout>
  );
}

function OrderDetailItem({ item }: { item: OrderItemsWithProduct }) {
  const optionPriceTotal = item.options?.reduce((total, option) =>
    total + (option.price || 0), 0) || 0;

  const itemTotal = (item.item_price ?? 0) * item.quantity;
  const productImage = useGetImage(item.products.image ?? "").data || item.products.image;

  return (
    <TableRow className="hover:bg-muted/50">
      <TableCell>
        <div className="w-16 h-16 rounded-lg border overflow-hidden">
          <img
            src={productImage ?? ""}
            alt={item.products.name}
            className="w-full h-full object-cover object-center"
          />
        </div>
      </TableCell>
      <TableCell>
        <div className="font-medium">{item.products.name}</div>
        <div className="text-sm text-muted-foreground">{item.products.variant}</div>
      </TableCell>
      <TableCell>{item.quantity}</TableCell>
      <TableCell className="text-right">
        <div>{formatPrice(item.item_price ?? 0)}</div>
        {optionPriceTotal > 0 && (
          <div className="text-green-500 text-sm">
            +{formatPrice(optionPriceTotal)}
          </div>
        )}
      </TableCell>
      <TableCell className="text-right">
        {item.options && item.options.length > 0 ? (
          <div className="space-y-1 text-right">
            {item.options.map((option: OrderItemOption) => (
              <div key={`order-item-option-${option.name}`} className="text-sm">
                <span className="font-medium">{option.name}:</span>{" "}
                <span className="text-muted-foreground">{option.value}</span>
                {option.price && option.price > 0 && (
                  <span className="text-green-500 ml-1">
                    (+{formatPrice(option.price)})
                  </span>
                )}
              </div>
            ))}
          </div>
        ) : (
          <span className="text-sm text-muted-foreground">No options</span>
        )}
      </TableCell>
    </TableRow>
  );
}

function OrderDetailsSkeleton() {
  return (
    <div className="grid gap-6">
      {/* Quick Stats Skeleton */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="hover:shadow-md transition-all">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-[100px]" />
              <Skeleton className="h-4 w-4 rounded-full" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-[120px] mb-1" />
              <Skeleton className="h-4 w-[140px]" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Order Details Skeleton */}
      <Card className="hover:shadow-md transition-all">
        <CardHeader>
          <Skeleton className="h-6 w-[150px]" />
        </CardHeader>
        <CardContent className="space-y-6">
          <Table>
            <TableHeader className="bg-muted/50">
              <TableRow>
                <TableHead className="w-[80px]">
                  <Skeleton className="h-4 w-full" />
                </TableHead>
                <TableHead>
                  <Skeleton className="h-4 w-full" />
                </TableHead>
                <TableHead>
                  <Skeleton className="h-4 w-full" />
                </TableHead>
                <TableHead>
                  <Skeleton className="h-4 w-full" />
                </TableHead>
                <TableHead>
                  <Skeleton className="h-4 w-full" />
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[1, 2].map((i) => (
                <TableRow key={i}>
                  <TableCell>
                    <Skeleton className="w-16 h-16 rounded-lg" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-[200px] mb-2" />
                    <Skeleton className="h-4 w-[150px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-[50px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-[100px] ml-auto" />
                  </TableCell>
                  <TableCell>
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[150px] ml-auto" />
                      <Skeleton className="h-4 w-[100px] ml-auto" />
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Other sections skeleton */}
          {["Shipping Details", "Payment Information", "Order Summary"].map((section, i) => (
            <div key={section}>
              <Separator />
              <div className="py-4">
                <Skeleton className="h-6 w-[200px] mb-4" />
                <Card className="bg-muted/50">
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <Skeleton className="h-5 w-[250px]" />
                      <Skeleton className="h-4 w-[200px]" />
                      <Skeleton className="h-4 w-[150px]" />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          ))}
        </CardContent>

        <div className="p-6 bg-muted/50 rounded-b-lg">
          <Skeleton className="h-11 w-full" />
        </div>
      </Card>
    </div>
  );
}