import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NevadaTaxRate } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default matchRoute({
  PATCH: checkAdmin(updateTaxRateHandler),
});

export interface UpdateTaxRateResponse {
  error?: string;
  taxRate?: NevadaTaxRate;
}

const updateTaxRateSchema = z.object({
  tax_rate: z.number().min(0).max(100, "Tax rate cannot exceed 100%"),
  city: z.string().min(1, "City is required"),
});

async function updateTaxRateHandler(
  req: NextApiRequest,
  res: NextApiResponse<UpdateTaxRateResponse>,
) {
  const { id } = req.query;

  if (!id || typeof id !== "string") {
    return res.status(400).json({ error: "Tax rate ID is required" });
  }

  try {
    const validatedData = updateTaxRateSchema.parse(req.body);

    const supabaseAdminClient = createSupabaseAdminClient();

    const { data, error } = await supabaseAdminClient
      .schema("public")
      .from("nevada_tax_rates")
      .update({
        tax_rate: validatedData.tax_rate,
        city: validatedData.city,
      })
      .eq("id", id)
      .select()
      .single();

    if (error) {
      return res.status(500).json({ error: error.message });
    }

    if (!data) {
      return res.status(404).json({ error: "Tax rate not found" });
    }

    return res.status(200).json({
      taxRate: data as NevadaTaxRate,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: error.errors.map((e) => e.message).join(", "),
      });
    }

    return res.status(500).json({
      error: "An unexpected error occurred",
    });
  }
}