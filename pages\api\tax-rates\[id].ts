import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NevadaTaxRate } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default matchRoute({
  GET: getTaxRateByCityHandler,
  PATCH: checkAdmin(updateTaxRateByCityHandler),
});

export interface GetTaxRateByCityResponse {
  error?: string;
  taxRate?: NevadaTaxRate;
}

export interface UpdateTaxRateResponse {
  error?: string;
  taxRate?: NevadaTaxRate;
}

const updateTaxRateSchema = z.object({
  tax_rate: z.number().min(0).max(100, "Tax rate cannot exceed 100%"),
});

async function getTaxRateByCityHandler(
  req: NextApiRequest,
  res: NextApiResponse<GetTaxRateByCityResponse>,
) {
  const { id: city } = req.query;

  if (!city || typeof city !== "string") {
    return res.status(400).json({ error: "City name is required" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const { data, error } = await supabaseAdminClient
    .from("nevada_tax_rates")
    .select("*")
    .eq("city", city)
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      return res.status(404).json({ error: "Tax rate not found for this city" });
    }
    return res.status(500).json({ error: error.message });
  }

  return res.status(200).json({
    taxRate: data as NevadaTaxRate,
  });
}

async function updateTaxRateByCityHandler(
  req: NextApiRequest,
  res: NextApiResponse<UpdateTaxRateResponse>,
) {
  const { id: city } = req.query;

  if (!city || typeof city !== "string") {
    return res.status(400).json({ error: "City name is required" });
  }

  try {
    const validatedData = updateTaxRateSchema.parse(req.body);

    const supabaseAdminClient = createSupabaseAdminClient();

    const { data, error } = await supabaseAdminClient
      .schema("public")
      .from("nevada_tax_rates")
      .update({
        tax_rate: validatedData.tax_rate,
      })
      .ilike("city", city.trim())
      .select()
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        return res.status(404).json({ error: "Tax rate not found for this city" });
      }
      return res.status(500).json({ error: error.message });
    }

    if (!data) {
      return res.status(404).json({ error: "Tax rate not found" });
    }

    return res.status(200).json({
      taxRate: data as NevadaTaxRate,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: error.errors.map((e) => e.message).join(", "),
      });
    }

    return res.status(500).json({
      error: "An unexpected error occurred",
    });
  }
}