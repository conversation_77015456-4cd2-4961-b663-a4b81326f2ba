import { createMailer, createMailerOptions } from "@/lib/mailer";
import { sendEmail } from "@/lib/mailer/sender";
import { createOrderUpdateTemplate } from "@/lib/mailer/templates";
import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAuth } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { Order } from "@/supabase/types";
import { NextApiResponse } from "next";

export default checkAuth(
  matchRoute({
    GET: handler,
    PATCH: cancelOrderHandler,
  }),
);

export interface GetOrderByIdResponse {
  order?: Order;
  error?: string;
}

async function handler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<GetOrderByIdResponse>,
) {
  const { id: user_id, order_id } = req.query;

  const userId = req.user?.id.toString();

  if (!user_id || !order_id) {
    return res.status(400).json({ error: "Missing required fields" });
  }

  if (!userId || userId !== user_id) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const customer = await supabaseAdminClient
    .from("customers")
    .select("id")
    .eq("user_id", user_id)
    .single();

  if (customer.error) {
    return res.status(400).json({ error: customer.error.message });
  }

  const customerId = customer.data.id;

  const order = await supabaseAdminClient
    .from("orders")
    .select(
      "*, order_items(id, order_id, quantity, item_price, products(id, name, sku, image, options, product_group_prices(*)), options), order_statuses(id, status, created_at), shipping_addresses(*), payment_type",
    )
    .eq("id", order_id.toString())
    .eq("user_id", user_id)
    .eq("customer_id", customerId)
    .single();

  if (order.error) {
    return res.status(400).json({ error: order.error.message });
  }

  // Sort order_statuses by created_at
  if (order.data.order_statuses && Array.isArray(order.data.order_statuses)) {
    order.data.order_statuses.sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
    );
  }

  return res.status(200).json({ order: order.data });
}

export interface CancelOrderResponse {
  order?: Pick<Order, "id">;
  error?: string;
}

async function cancelOrderHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<CancelOrderResponse>,
) {
  const { id: user_id, order_id } = req.query;

  const userId = req.user?.id.toString();

  if (!user_id || !order_id) {
    return res.status(400).json({ error: "Missing required fields" });
  }

  if (!userId || userId !== user_id) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const customer = await supabaseAdminClient
    .from("customers")
    .select("id")
    .eq("user_id", user_id)
    .single();

  if (customer.error) {
    return res.status(400).json({ error: customer.error.message });
  }

  const customerId = customer.data.id;

  const order = await supabaseAdminClient
    .from("orders")
    .select(
      "id, invoice, order_statuses(status), order_items(id, quantity, products(name, options))",
    )
    .eq("id", order_id.toString())
    .eq("user_id", user_id)
    .eq("customer_id", customerId)
    .single();

  if (order.error) {
    return res.status(400).json({ error: order.error.message });
  }

  const orderStatus = order.data.order_statuses?.[0]?.status;

  if (orderStatus !== "pending") {
    return res.status(400).json({
      error: `This order has a status of "${orderStatus}" and cannot be canceled. Please contact the admin team for assistance with canceling this order.`,
    });
  }

  const orderId = order.data.id;

  const new_order_status = await supabaseAdminClient
    .from("order_statuses")
    .update({ status: "canceled" })
    .eq("order_id", orderId)
    .select("*")
    .single();

  if (new_order_status.error) {
    return res.status(400).json({ error: new_order_status.error.message });
  }

  const now = new Date();
  const updatedAt = now.toISOString();

  await supabaseAdminClient
    .from("orders")
    .update({ updated_at: updatedAt })
    .eq("id", orderId);

  // Get user email for notification
  const userDetails = await supabaseAdminClient
    .from("users")
    .select("email, first_name, last_name")
    .eq("id", user_id)
    .single();

  if (userDetails.data?.email) {
    // Format order items for email template
    const items =
      order.data.order_items?.map((item) => {
        // Handle options conversion to expected format
        let formattedOptions:
          | { name: string; value: string; price?: number }[]
          | undefined = undefined;

        if (item.products?.options && Array.isArray(item.products.options)) {
          formattedOptions = item.products.options.map((opt: any) => ({
            name: opt.name || "Option",
            value: opt.value || "",
            price: typeof opt.price === "number" ? opt.price : undefined,
          }));
        }

        return {
          name: item.products?.name || "Product",
          quantity: item.quantity,
          options: formattedOptions,
        };
      }) || [];

    // Send cancellation email
    const emailTemplate = createOrderUpdateTemplate({
      to: userDetails.data.email,
      orderId: order.data.id,
      invoice: order.data.invoice || `#${order.data.id}`,
      oldStatus: "pending",
      newStatus: "canceled",
      statusDate: new Date().toISOString(),
      items: items,
      additionalInfo:
        "Your order has been canceled as requested. If you have any questions, please contact our customer service team.",
    });

    const mailerOptions = createMailerOptions();
    const mailer = createMailer(mailerOptions);
    await sendEmail(mailer, emailTemplate);
  }

  return res.status(200).json({ order: order.data });
}
