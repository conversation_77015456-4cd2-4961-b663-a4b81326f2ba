import { useGetAllNotificationsQuery } from "@/queries/notification-queries";
import useAuthStore from "@/stores/auth-store";
import { useNotificationsStore } from "@/stores/notifications-store";
import { useEffect } from "react";

export function useNotifications() {
    const userData = useAuthStore((state) => state.data);
    const userId = userData?.id;
    const notifications = useNotificationsStore((state) => state.notifications);
    const addNotification = useNotificationsStore((state) => state.addNotification);
    const subscribe = useNotificationsStore((state) => state.subscribe);
    const notificationsData = useGetAllNotificationsQuery(userData?.id ?? "");
    const existingNotifications = notificationsData.data ?? [];

    useEffect(function subscribeToNotifications() {
        if (!userId) return;

        const unsubscribe = subscribe(userId);

        return () => {
            unsubscribe();
        }
    }, [userId]);

    useEffect(() => {
        if (!existingNotifications) return;
        existingNotifications.forEach(addNotification);
    }, [existingNotifications]);

    return notifications;
}