import { SvgSpinners90Ring } from "@/components/common/icons";
import { Footer } from "@/components/sections/footer";
import { Navigation } from "@/components/sections/navigation";
import { Card } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PhoneInput, phoneSchema } from "@/components/ui/phone-input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/shadcn-button";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import {
  useFooterQuery,
  useNavigationQuery,
} from "@/queries/component-queries";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft, ArrowRight, Eye, EyeClosed } from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useRouter } from "next/router";
import type { CreateUserDTO } from "pages/api/users";
import { useSignupMutation } from "queries/user-queries";
import React, { useEffect, useState } from "react";
import { useForm, Controller } from "react-hook-form";
import useAuthStore from "stores/auth-store";
import { z } from "zod";
import { CountrySelect, StateSelect, CitySelect } from "react-country-state-city";
import "react-country-state-city/dist/react-country-state-city.css";
import { Country, State } from "react-country-state-city/dist/esm/types";
import { useLocationData } from "@/hooks/use-location-data";

// Step 1: Account Information Schema
const accountInfoSchema = z
  .object({
    first_name: z.string().min(1, { message: "First name is required" }),
    last_name: z.string().min(1, { message: "Last name is required" }),
    email: z.string().email({ message: "Invalid email" }),
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters" }),
    confirm_password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters" }),
  })
  .refine(({ password, confirm_password }) => password === confirm_password, {
    message: "Passwords do not match",
    path: ["confirm_password"],
  });

// Step 2: Billing Information Schema
const billingInfoSchema = z.object({
  address: z.string().min(1, { message: "Address is required" }),
  address_type: z.enum(["commercial", "residential"], {
    required_error: "Address type is required",
  }),
  city: z.string().min(1, { message: "City is required" }),
  state: z.string().min(1, { message: "State is required" }),
  zip_code: z.string().min(1, { message: "Zip code is required" }),
  country: z.string().min(1, { message: "Country is required" }),
});

// Step 3: Business Details Schema
const businessDetailsSchema = z.object({
  business_type: z.string().optional(),
  business_nature: z.string().optional(),
  website: z.string().optional(),
  maxton_account: z.string().optional(),
  has_mechanic: z.enum(["yes", "no", "use service company"], {
    required_error: "Mechanic status is required",
  }),
  number_of_elevators: z.number().optional(),
  authorized_contact_name: z.string().optional(),
  buyer_name: z.string().optional(),
  buyer_phone: z.string().optional(),
  technical_contact: z.string().optional(),
  technical_contact_phone: z.string().optional(),
  account_payable_contact: z.string().optional(),
  account_payable_phone: z.string().optional(),
  technician: z.string().optional(),
});

// Combined schema for the entire form
const signupFormSchema = z
  .object({
    // User Details
    first_name: z.string().min(1, { message: "First name is required" }),
    last_name: z.string().min(1, { message: "Last name is required" }),
    email: z.string().email({ message: "Invalid email" }),
    telephone: z.string().min(10, { message: "Telephone number must be at least 10 digits" }).regex(/^[0-9+\-\(\)\s]*$/, { message: "Phone number can only contain numbers, +, -, (, ), and spaces" }),
    company_name: z.string().min(1, { message: "Company name is required" }),
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters" }),
    confirm_password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters" }),
    subscribe_newsletter: z.boolean().default(false),
    address: z.string().min(1, { message: "Address is required" }),
    address_2: z.string().optional(),
    address_type: z.enum(["commercial", "residential"], {
      required_error: "Address type is required",
    }),
    city: z.string().min(1, { message: "City is required" }),
    state: z.string().min(1, { message: "State is required" }),
    zip_code: z.string().min(1, { message: "Zip code is required" }),
    country: z.string().min(1, { message: "Country is required" }),
    // Business Details
    business_type: z.string().optional(),
    business_nature: z.string().optional(),
    website: z.string().optional(),
    maxton_account: z.string().optional(),
    has_mechanic: z.enum(["yes", "no", "use service company"], {
      required_error: "Mechanic status is required",
    }),
    number_of_elevators: z.coerce.number().optional(),
    authorized_contact_name: z.string().optional(),
    buyer_name: z.string().optional(),
    buyer_phone: z.string().min(10, { message: "Telephone number must be at least 10 digits" }).regex(/^[0-9+\-\(\)\s]*$/, { message: "Phone number can only contain numbers, +, -, (, ), and spaces" }),
    technical_contact: z.string().optional(),
    technical_contact_phone: z.string().min(10, { message: "Telephone number must be at least 10 digits" }).regex(/^[0-9+\-\(\)\s]*$/, { message: "Phone number can only contain numbers, +, -, (, ), and spaces" }),
    account_payable_contact: z.string().optional(),
    account_payable_phone: z.string().min(10, { message: "Telephone number must be at least 10 digits" }).regex(/^[0-9+\-\(\)\s]*$/, { message: "Phone number can only contain numbers, +, -, (, ), and spaces" }),
    technician: z.string().optional(),
  })
  .refine(({ password, confirm_password }) => password === confirm_password, {
    message: "Passwords do not match",
    path: ["confirm_password"],
  });

type SignupForm = z.infer<typeof signupFormSchema>;

export default function SignUp() {
  const { toast } = useToast();
  const router = useRouter();
  const signupMutation = useSignupMutation();
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated());
  const [step, setStep] = useState(1);
  const [showPasswords, setShowPasswords] = useState(false);
  const [countryCode, setCountryCode] = useState<string>("");
  const [hasStates, setHasStates] = useState(true);
  const [hasCities, setHasCities] = useState(true);

  // Get location data from our custom hook with United States as default
  const {
    countryId, stateId,
    selectedCountry, selectedState, selectedCity,
    updateCountry, updateState, updateCity,
    isLoading
  } = useLocationData({});

  const togglePasswordVisibility = () => {
    setShowPasswords(!showPasswords);
  };
  const totalSteps = 3;

  const signupForm = useForm<SignupForm>({
    resolver: zodResolver(signupFormSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      telephone: "",
      company_name: "",
      password: "",
      confirm_password: "",
      subscribe_newsletter: false,
      address: "",
      address_2: "",
      address_type: "commercial",
      city: "",
      state: "",
      zip_code: "",
      country: "United States",
      // Business Details
      business_type: "",
      business_nature: "",
      website: "",
      maxton_account: "",
      has_mechanic: "no",
      number_of_elevators: 0,
      authorized_contact_name: "",
      buyer_name: "",
      buyer_phone: "",
      technical_contact: "",
      technical_contact_phone: "",
      account_payable_contact: "",
      account_payable_phone: "",
      technician: "",
    },
  });

  const nextStep = async () => {
    // Validate only the current step's fields
    if (step === 1) {
      const isValid = await signupForm.trigger([
        "first_name",
        "last_name",
        "email",
        "telephone",
        "password",
        "confirm_password",
      ]);
      if (isValid) setStep(2);
    } else if (step === 2) {
      const isValid = await signupForm.trigger([
        "company_name",
        "address",
        "address_type",
        "city",
        "state",
        "zip_code",
        "country",
      ]);
      if (isValid) setStep(3);
    }
  };

  const prevStep = () => {
    if (step > 1) setStep(step - 1);
  };

  const signUpHandler = async (data: SignupForm) => {
    if (isAuthenticated) return;
    try {
      // Create user with required fields only
      const dto: CreateUserDTO = {
        user_details: {
          email: data.email,
          telephone: data.telephone ?? "",
          company_name: data.company_name,
          password: data.password,
          first_name: data.first_name,
          last_name: data.last_name,
          subscribe_newsletter: data.subscribe_newsletter,
          address: data.address,
          address_2: data.address_2 ?? "",
          address_type: data.address_type,
          city: data.city,
          state: data.state,
          zip_code: data.zip_code,
          country: data.country,
        },
        business_details: {
          business_type: data.business_type || "",
          business_nature: data.business_nature || "",
          website: data.website || "",
          maxton_account: data.maxton_account || "",
          number_of_elevators: data.number_of_elevators,
          authorized_contact_name: data.authorized_contact_name || "",
          buyer_name: data.buyer_name || "",
          buyer_phone: data.buyer_phone || "",
          technical_contact: data.technical_contact || "",
          technical_contact_phone: data.technical_contact_phone || "",
          account_payable_contact: data.account_payable_contact || "",
          account_payable_phone: data.account_payable_phone || "",
          technician: data.technician || "",
          has_mechanic: data.has_mechanic || "no"
        }
      };

      const result = await signupMutation.mutateAsync(dto);
    } catch (e) { }
  };

  useEffect(
    function showToast() {
      if (signupMutation.isSuccess) {
        toast({
          title: "Sign up successful",
          description:
            "You have successfully signed up, redirecting to log in page...",
          variant: "success",
          duration: 3000,
        });

        const timeout = setTimeout(function redirectToHome() {
          router.push("/log-in");
        }, 3000);
        return () => clearTimeout(timeout);
      }

      if (signupMutation.isError) {
        toast({
          title: "Sign up failed",
          description: signupMutation?.error?.message ?? "Something went wrong",
          variant: "destructive",
          duration: 3000,
        });
      }
    },
    [signupMutation.isSuccess, signupMutation.isError]
  );

  const navigationData = useNavigationQuery();
  const footerData = useFooterQuery();

  return (
    <React.Fragment>
      {navigationData.data && (
        <div className="sticky top-0 left-0 w-full h-full z-50">
          <Navigation data={navigationData.data?.data} />
        </div>
      )}
      <Head>
        <title>Sign Up</title>
        <meta name="description" content="Sign up for an account" />
      </Head>
      <section className="relative w-full min-h-screen flex items-center justify-center py-20">
        <div className="w-full mx-auto max-w-2xl">
          <Card className="px-12 py-10 shadow-lg shadow-slate-300 rounded-sm">
            <div className="mb-6">
              <h1 className="text-3xl font-medium text-center py-3">Sign Up</h1>

              {/* Enhanced Stepper Component */}
              <div className="w-full mb-8 mt-6">
                <div className="flex justify-between mb-2">
                  <span className="font-medium">
                    Step {step} of {totalSteps}
                  </span>
                  <span className="text-primary font-semibold">{Math.round((step / totalSteps) * 100)}%</span>
                </div>

                <div className="relative pt-8">
                  {/* Step Indicators */}
                  <div className="absolute top-0 left-0 right-0 flex justify-between z-20">
                    <div className={`flex flex-col items-center ${step >= 1 ? 'text-primary' : 'text-gray-400'}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 border-2 ${step >= 1 ? 'bg-primary text-white border-primary shadow-md' : 'bg-gray-100 border-gray-300'} transition-all duration-300`}>1</div>
                      <span className="text-xs font-medium">Account</span>
                    </div>

                    <div className={`flex flex-col items-center ${step >= 2 ? 'text-primary' : 'text-gray-400'}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 border-2 ${step >= 2 ? 'bg-primary text-white border-primary shadow-md' : 'bg-gray-100 border-gray-300'} transition-all duration-300`}>2</div>
                      <span className="text-xs font-medium">Billing</span>
                    </div>

                    <div className={`flex flex-col items-center ${step >= 3 ? 'text-primary' : 'text-gray-400'}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 border-2 ${step >= 3 ? 'bg-primary text-white border-primary shadow-md' : 'bg-gray-100 border-gray-300'} transition-all duration-300`}>3</div>
                      <span className="text-xs font-medium">Business</span>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="relative h-3 bg-gray-100 rounded-full mt-8">
                    <div
                      className="absolute top-0 left-0 h-full bg-primary rounded-full transition-all duration-500 ease-in-out"
                      style={{ width: `${(step / totalSteps) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <Form {...signupForm}>
              <form
                onSubmit={signupForm.handleSubmit(signUpHandler)}
                className="w-full h-full space-y-6"
              >
                {/* Step 1: Account Information */}
                {step === 1 && (
                  <div className="space-y-4 min-h-[400px]">
                    <h2 className="text-xl font-semibold">
                      Account Information
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={signupForm.control}
                        name="first_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel aria-required>First Name</FormLabel>
                            <FormControl>
                              <Input placeholder="John" {...field} />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={signupForm.control}
                        name="last_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel aria-required>Last Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Doe" {...field} />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={signupForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel aria-required>Email</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="<EMAIL>"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={signupForm.control}
                      name="telephone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel aria-required>Telephone</FormLabel>
                          <FormControl>
                            <PhoneInput
                              required
                              placeholder="Enter phone number"
                              defaultCountry={countryCode || "US"}
                              onCountryChange={(country) => {
                                console.log("Country changed:", country?.name);
                              }}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={signupForm.control}
                        name="password"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel aria-required>Password</FormLabel>
                            <FormControl>
                              <div className="relative w-full h-fit">
                                <Input
                                  type={showPasswords ? "text" : "password"}
                                  placeholder="**********"
                                  className="w-full h-fit mr-4"
                                  {...field}
                                />
                                <Button type="button" className="w-fit h-fit absolute z-10 right-2 top-1/2 transform -translate-y-1/2 hover:bg-transparent bg-transparent text-primary" size="icon" onClick={togglePasswordVisibility}>
                                  {
                                    showPasswords ? (
                                      <EyeClosed className="w-7 h-7" />
                                    ) : (
                                      <Eye className="w-7 h-7" />
                                    )
                                  }
                                </Button>
                              </div>
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={signupForm.control}
                        name="confirm_password"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel aria-required>Confirm Password</FormLabel>
                            <FormControl>
                              <div className="relative w-full h-fit">
                                <Input
                                  type={showPasswords ? "text" : "password"}
                                  placeholder="**********"
                                  className="w-full h-fit mr-4"
                                  {...field}
                                />
                                <Button type="button" className="w-fit h-fit absolute z-10 right-2 top-1/2 transform -translate-y-1/2 hover:bg-transparent bg-transparent text-primary" size="icon" onClick={togglePasswordVisibility}>
                                  {
                                    showPasswords ? (
                                      <EyeClosed className="w-7 h-7" />
                                    ) : (
                                      <Eye className="w-7 h-7" />
                                    )
                                  }
                                </Button>
                              </div>
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={signupForm.control}
                      name="subscribe_newsletter"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 mt-2">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>Subscribe to Newsletter</FormLabel>
                            <FormDescription className="text-xs">
                              Receive updates about new products, promotions, and company news.
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />

                    <div className="pt-4 flex justify-end">
                      <Button
                        type="button"
                        onClick={nextStep}
                        className="bg-primary"
                      >
                        Continue to Billing Information
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}

                {/* Step 2: Billing Information */}
                {step === 2 && (
                  <div className="space-y-4 min-h-[400px]">
                    <h2 className="text-xl font-semibold">
                      Billing Information
                    </h2>
                    <FormField
                      control={signupForm.control}
                      name="company_name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel aria-required>Company Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Your Company" {...field} />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={signupForm.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel aria-required>Address line 1</FormLabel>
                          <FormControl>
                            <Input placeholder="123 Main St" {...field} />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={signupForm.control}
                      name="address_2"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel aria-required>Address line 2</FormLabel>
                          <FormControl>
                            <Input placeholder="Apartment, suite, unit, etc." {...field} />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={signupForm.control}
                      name="address_type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel aria-required>Address Type</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select address type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="commercial">Commercial</SelectItem>
                              <SelectItem value="residential">Residential</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={signupForm.control}
                        name="country"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel aria-required>Country</FormLabel>
                            <FormControl>
                              {isLoading ? (
                                <div className="flex items-center h-9 px-3 border rounded-md">
                                  <SvgSpinners90Ring className="w-4 h-4 mr-2" />
                                  <span className="text-sm text-muted-foreground">Loading countries...</span>
                                </div>
                              ) : (
                                <div>
                                  <CountrySelect
                                    onChange={(item: Country) => {
                                      field.onChange(item.name);
                                      setCountryCode(item.iso2);
                                      setHasStates(item.hasStates);
                                      if (!item.hasStates) {
                                        setHasCities(false);
                                      } else {
                                        setHasCities(true);
                                      }
                                      // Update country and reset dependent fields
                                      updateCountry(item);
                                      // Reset form values
                                      signupForm.setValue('state', '');
                                      signupForm.setValue('city', '');
                                    }}
                                    placeHolder="Select country"
                                    showFlag={true}
                                    className="w-full h-9 px-3 py-2 bg-transparent text-sm border-0"
                                    defaultValue={selectedCountry}
                                  />
                                </div>
                              )}
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={signupForm.control}
                        name="state"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel aria-required>State</FormLabel>
                            <FormControl>
                              {isLoading ? (
                                <div className="flex items-center h-9 px-3 border rounded-md">
                                  <SvgSpinners90Ring className="w-4 h-4 mr-2" />
                                  <span className="text-sm text-muted-foreground">Loading states...</span>
                                </div>
                              ) : (
                                <div className={`border border-input rounded-md h-9 ${countryId === 0 ? 'opacity-50' : ''}`}>
                                  {
                                    hasStates ? (
                                      <StateSelect
                                        countryid={countryId}
                                        onChange={(item: State) => {
                                          field.onChange(item.name);
                                          setHasCities(item.hasCities);
                                          // Update state and reset city
                                          updateState(item);
                                          // Reset city form value
                                          signupForm.setValue('city', '');
                                        }}
                                        placeHolder="Select state"
                                        disabled={countryId === 0}
                                        className="w-full h-9 px-3 py-2 bg-transparent text-sm"
                                        defaultValue={selectedState}
                                      />
                                    ) : (
                                      <Input type="text" placeholder="Enter State" {...field} />
                                    )
                                  }
                                </div>
                              )}
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={signupForm.control}
                        name="city"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel aria-required>City</FormLabel>
                            <FormControl>
                              {isLoading ? (
                                <div className="flex items-center h-9 px-3 border rounded-md">
                                  <SvgSpinners90Ring className="w-4 h-4 mr-2" />
                                  <span className="text-sm text-muted-foreground">Loading cities...</span>
                                </div>
                              ) : (
                                <div className={`border border-input rounded-md h-9 ${!stateId ? 'opacity-50' : ''}`}>
                                  {
                                    hasCities ? (
                                      <CitySelect
                                        countryid={countryId}
                                        stateid={stateId}
                                        onChange={(item) => {
                                          field.onChange(item.name);
                                          // Update city
                                          updateCity(item);
                                        }}
                                        placeHolder="Select city"
                                        disabled={!stateId}
                                        className="w-full h-9 px-3 py-2 bg-transparent text-sm"
                                        defaultValue={selectedCity}
                                      />
                                    ) : (
                                      <Input type="text" placeholder="Enter City" {...field} />
                                    )
                                  }
                                </div>
                              )}
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={signupForm.control}
                        name="zip_code"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel aria-required>Zip Code</FormLabel>
                            <FormControl>
                              <Input placeholder="10001" {...field} />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="pt-4 flex justify-between">
                      <Button
                        type="button"
                        onClick={prevStep}
                        variant="outline"
                      >
                        <ArrowLeft className="ml-2 h-4 w-4" />
                        Back to Account Information
                      </Button>

                      <Button
                        type="button"
                        onClick={nextStep}
                        className="bg-primary"
                      >
                        Continue to Business Details
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}

                {/* Step 3: Business Details */}
                {step === 3 && (
                  <div className="space-y-4 min-h-[400px]">
                    <h2 className="text-xl font-semibold">
                      Business Details <span className="text-sm text-gray-500 font-normal">(Optional)</span>
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={signupForm.control}
                        name="business_type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Business Type</FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select business type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Elevator Company">Elevator Company</SelectItem>
                                  <SelectItem value="Non-Elevator Company">Non-Elevator Company</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={signupForm.control}
                        name="business_nature"
                        render={({ field }) => {
                          // Check if the field value starts with "other:"
                          const isOtherValue = field.value?.startsWith("other:") || field.value === "other";
                          const [showOtherInput, setShowOtherInput] = useState(isOtherValue);

                          // Extract the other value if it exists
                          const initialOtherValue = field.value?.startsWith("other:")
                            ? field.value.replace("other:", "").trim()
                            : "";
                          const [otherValue, setOtherValue] = useState(initialOtherValue);

                          return (
                            <FormItem>
                              <FormLabel>Nature of Business</FormLabel>
                              <FormControl>
                                <Select
                                  onValueChange={(value) => {
                                    if (value !== "Other") {
                                      field.onChange(value);
                                      setOtherValue("");
                                    } else {
                                      // For "other", use the existing other value if available
                                      if (otherValue) {
                                        field.onChange(otherValue);
                                      } else {
                                        field.onChange("Other");
                                      }
                                    }
                                    setShowOtherInput(value === "Other");
                                  }}
                                  defaultValue={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select business nature" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="Service-based">Service-based</SelectItem>
                                    <SelectItem value="Manufacturer">Manufacturer</SelectItem>
                                    <SelectItem value="Distributor">Distributor</SelectItem>
                                    <SelectItem value="Elevator Inspector">Elevator Inspector</SelectItem>
                                    <SelectItem value="Elevator Consultant">Elevator Consultant</SelectItem>
                                    <SelectItem value="Other">Other</SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              {showOtherInput && (
                                <FormItem className="mt-2">
                                  <FormControl>
                                    <Input
                                      placeholder="Please specify nature of business"
                                      value={otherValue}
                                      onChange={(e) => {
                                        setOtherValue(e.target.value);
                                        // Update the actual field value with the custom input
                                        field.onChange(e.target.value);
                                      }}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                              <FormMessage className="text-xs" />
                            </FormItem>
                          );
                        }}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={signupForm.control}
                        name="website"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Website</FormLabel>
                            <FormControl>
                              <Input placeholder="https://yourcompany.com" {...field} />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={signupForm.control}
                        name="maxton_account"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Maxton Account (if any)</FormLabel>
                            <FormControl>
                              <Input placeholder="Account number" {...field} />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={signupForm.control}
                      name="has_mechanic"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>On-site Elevator Mechanic/Technician?</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select option" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="yes">Yes, elevator mechanics employed</SelectItem>
                                <SelectItem value="no">No elevator mechanics employed</SelectItem>
                                <SelectItem value="use service company">We use an elevator service company</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={signupForm.control}
                      name="number_of_elevators"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Number of Elevators</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="How many elevators do you manage?"
                              min={0}
                              {...field}
                              onChange={e => field.onChange(e.target.value === '' ? undefined : Number(e.target.value))}
                              onWheel={(e) => e.currentTarget.blur()}
                            />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />

                    <h3 className="text-lg font-medium pt-2">Contact Information</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={signupForm.control}
                        name="authorized_contact_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Authorized Contact Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Full name" {...field} />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={signupForm.control}
                        name="buyer_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Buyer Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Buyer's full name" {...field} />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={signupForm.control}
                        name="buyer_phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Buyer Phone</FormLabel>
                            <FormControl>
                              <PhoneInput
                                placeholder="Enter phone number"
                                defaultCountry={countryCode || "US"}
                                onCountryChange={(country) => {
                                  console.log("Buyer phone country:", country?.name);
                                }}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={signupForm.control}
                        name="technical_contact"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Technical Contact</FormLabel>
                            <FormControl>
                              <Input placeholder="Technical contact name" {...field} />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={signupForm.control}
                        name="technical_contact_phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Technical Contact Phone</FormLabel>
                            <FormControl>
                              <PhoneInput
                                placeholder="Enter phone number"
                                defaultCountry={countryCode || "US"}
                                onCountryChange={(country) => {
                                  console.log("Technical contact country:", country?.name);
                                }}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={signupForm.control}
                        name="account_payable_contact"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Accounts Payable Contact</FormLabel>
                            <FormControl>
                              <Input placeholder="AP contact name" {...field} />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={signupForm.control}
                        name="account_payable_phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Accounts Payable Phone</FormLabel>
                            <FormControl>
                              <PhoneInput
                                placeholder="Enter phone number"
                                defaultCountry={countryCode || "US"}
                                onCountryChange={(country) => {
                                  console.log("Accounts payable country:", country?.name);
                                }}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={signupForm.control}
                        name="technician"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Technician Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Technician name" {...field} />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="pt-4 flex justify-between">
                      <Button
                        type="button"
                        onClick={prevStep}
                        variant="outline"
                      >
                        <ArrowLeft className="ml-2 h-4 w-4" />
                        Back to Billing Information
                      </Button>

                      <Button
                        type="submit"
                        className="bg-primary"
                        disabled={signupForm.formState.isSubmitting}
                        aria-disabled={signupForm.formState.isSubmitting}
                      >
                        <span>Create account</span>
                        {signupForm.formState.isSubmitting && (
                          <SvgSpinners90Ring />
                        )}
                      </Button>
                    </div>
                  </div>
                )}

                {signupMutation.isError ? (
                  <div className="relative w-full h-fit">
                    <p className="text-red-500 text-sm">
                      {signupMutation.error?.message}
                    </p>
                  </div>
                ) : null}

                <div className="w-full flex items-center justify-center pt-4">
                  <span className="flex items-center gap-2 text-sm">
                    Already have an account?
                    <Link
                      target="_self"
                      prefetch
                      href="/log-in"
                      className="underline text-primary"
                    >
                      Log in
                    </Link>
                  </span>
                </div>
              </form>
            </Form>
          </Card>
        </div>
      </section>
      {footerData.data && (
        <div className="w-full h-full">
          <Footer data={footerData.data?.data} />
        </div>
      )}
    </React.Fragment>
  );
}
