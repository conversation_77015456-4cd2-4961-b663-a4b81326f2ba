import { SvgSpinners90Ring } from "@/components/common/icons";
import { BillingAddresses } from "@/components/features/store/customers/billing-address";
import { ShippingAddresses } from "@/components/features/store/customers/shipping-address";
import StoreLayout from "@/components/features/store/layout";
import { OrderStatusBadge } from "@/components/features/store/orders-table";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/shadcn-button";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/hooks/use-toast";
import { formatPrice } from "@/lib/utils";
import {
  OrderItemOption,
  OrderWithItems,
  useAddNewOrderMutation,
  useGetBillingAddressesQuery,
  useGetCustomerQuery,
  useGetOrderByIdQuery,
  useGetShippingAddressesQuery,
  useUploadFile,
  useGetImage,
  useAddShippingAddressMutation,
} from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import useCartStore, { CartItem } from "@/stores/cart-store";
import { supabaseClient } from "@/supabase";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  AlertCircle,
  ArrowLeft,
  CheckCircle2,
  CreditCard,
  FileText,
  Package,
  Receipt,
  ShoppingBag,
  ShoppingCart,
  Truck,
} from "lucide-react";
import Link from "next/link";
import Head from "next/head";
import { useRouter } from "next/router";
import { useEffect, useState, useRef } from "react";
import { useForm, UseFormReturn } from "react-hook-form";
import { ShippingAddress } from "supabase/types";
import { z } from "zod";
import { Checkbox } from "@/components/ui/checkbox";
import { getTaxRateForCity, calculateTaxAmount, getCountyForCity } from "../../lib/utils/nevada-tax-rates";

export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACCEPTED_DOCUMENT_TYPES = [
  "application/pdf",
  "application/docx",
  "application/doc",
];

const formSchema = z.object({
  // Billing Information
  billingAddressId: z.string().min(1, "Please select a billing address"),
  // Shipping Information
  shippingAddressId: z.string().min(1, "Please select a shipping address"),
  // Delivery Method
  ship_collect: z.boolean().optional(),
  ups_account_number: z.string().optional(),
  delivery_method: z.string().min(1, "Please select a delivery method"),
  // Payment Information
  paymentType: z.enum(["credit_card", "purchase_order"]),
  // cardNumber: z.string().optional(),
  // expiryDate: z.string().optional(),
  // cvc: z.string().optional(),
  // PO Information
  poNumber: z.string().optional(),
  tax_exempt: z.boolean().default(false),
  poFile: z
    .instanceof(File)
    .refine(
      (file) => (file?.size || 0) <= MAX_FILE_SIZE,
      `Max file size is ${MAX_FILE_SIZE} bytes.`
    )
    .refine(
      (file) => ACCEPTED_DOCUMENT_TYPES.includes(file?.type || ""),
      "Only PDF, DOC, and DOCX files are allowed."
    )
    .optional(),
  items: z
    .array(
      z.object({
        id: z.string(),
        quantity: z.number().min(1),
        options: z
          .array(
            z.object({
              name: z.string(),
              value: z.union([z.string(), z.number()]),
            })
          )
          .optional(),
        calculatorData: z
          .object({
            providedData: z
              .object({
                "Jack Type": z.string(),
                "Piston Dia.": z.string(),
                "Car Speed": z.string(),
                "Empty Car": z.string(),
                Capacity: z.string(),
                "Down Speed Regulation": z.string(),
              })
              .optional(),
            results: z
              .object({
                "Rated Flow": z.number(),
                "Empty Static Pressure": z.number(),
                "Loaded Car Pressure": z.number(),
              })
              .optional(),
          })
          .optional(),
      })
    )
    .default([]),
});

export type CheckoutFormValues = z.infer<typeof formSchema>;

export default function Checkout() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const items = useCartStore((state) => state.items);
  const clearCart = useCartStore((state) => state.clearCart);
  const userData = useAuthStore((state) => state.data);
  const userId = userData.id;
  const customerData = useGetCustomerQuery(userId);

  const billingAddresses = useGetBillingAddressesQuery(userId);
  const shippingAddresses = useGetShippingAddressesQuery(userId);

  const addNewOrderMutation = useAddNewOrderMutation(userId);
  const uploadFileMutation = useUploadFile(userId, "purchase-orders");
  const addShippingAddressMutation = useAddShippingAddressMutation(userId);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      billingAddressId: "",
      shippingAddressId: "",
      poNumber: "",
      poFile: undefined,
      items: [],
      paymentType: "credit_card",
      tax_exempt: false,
    },
  });

  useEffect(
    function setItems() {
      form.setValue(
        "items",
        items.map((item) => {
          return {
            id: item.id,
            quantity: item.quantity,
            options: item.selectedOptions ?? undefined,
            calculatorData: item.calculatorData ?? undefined,
          };
        })
      );
    },
    [items]
  );

  useEffect(
    function setBillingAddress() {
      if (billingAddresses.data?.length === 0) return;
      if (billingAddresses.data?.length === 1) {
        const defaultAddress = billingAddresses.data[0].id ?? "";
        form.setValue("billingAddressId", defaultAddress);
      }
      const defaultAddress = billingAddresses.data?.find(
        (address) => address.default
      );
      form.setValue("billingAddressId", defaultAddress?.id ?? "");
    },
    [billingAddresses.data]
  );

  useEffect(
    function setShippingAddress() {
      if (shippingAddresses.data?.length === 0) return;
      if (shippingAddresses.data?.length === 1) {
        const defaultAddress = shippingAddresses.data[0].id ?? "";
        form.setValue("shippingAddressId", defaultAddress);
      }
      const defaultAddress = shippingAddresses.data?.find(
        (address) => address.default
      );
      form.setValue("shippingAddressId", defaultAddress?.id ?? "");
    },
    [shippingAddresses.data]
  );

  useEffect(
    function setTaxExemptBasedOnShippingState() {
      const shippingAddressId = form.getValues("shippingAddressId");
      if (!shippingAddressId || !shippingAddresses.data) return;

      const shippingAddress = shippingAddresses.data.find(
        (addr) => addr.id === shippingAddressId
      );

      // If shipping state is not Nevada, automatically set tax_exempt to false
      if (shippingAddress?.state !== "Nevada") {
        form.setValue("tax_exempt", false);
      }
    },
    [form.watch("shippingAddressId"), shippingAddresses.data]
  );

  // useEffect(function redirectToOrderConfirmation() {
  //     if (!addNewOrderMutation.isSuccess || !addNewOrderMutation.data) return;

  //     // Immediate redirect instead of setTimeout
  //     router.push(`/store/order-confirmation?orderId=${addNewOrderMutation.data}`);
  // }, [addNewOrderMutation.isSuccess, addNewOrderMutation.data, router])

  const MINIMUM_ORDER_AMOUNT = 50;
  const [showMinOrderAlert, setShowMinOrderAlert] = useState(false);

  const cartSubtotal = useCartStore((state) => state.getSubtotal)();
  const additionalAmount = useCartStore((state) => state.getAdditionalAmount)();
  const total = cartSubtotal + additionalAmount;

  const [activeTab, setActiveTab] = useState("shipping");
  const [orderConfirmed, setOrderConfirmed] = useState(false);
  const [orderId, setOrderId] = useState<string | null>(null);
  const [isAttachment, setSwitchToAttachment] = useState(false);

  const goToTab = (tab: string) => {
    setActiveTab(tab);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  useEffect(() => {
    if (activeTab === "payment") {
      // Get the current shipping address
      const shippingAddressId = form.getValues("shippingAddressId");
      const shippingAddress = shippingAddresses.data?.find(
        (addr) => addr.id === shippingAddressId
      );

      // Check if the shipping address is for a special country
      const isSpecialCountry =
        shippingAddress?.country === "Mexico" ||
        shippingAddress?.country === "Puerto Rico" ||
        shippingAddress?.state === "Puerto Rico" ||
        shippingAddress?.state?.includes("Mexico");

      // Check shipping requirements based on country
      const shipCollectValue = form.getValues("ship_collect");
      const deliveryMethodValue = form.getValues("delivery_method");

      // Determine validation conditions based on country
      const hasShippingError = isSpecialCountry
        ? !deliveryMethodValue || deliveryMethodValue.trim() === ""
        : shipCollectValue === undefined ||
          !deliveryMethodValue ||
          deliveryMethodValue.trim() === "";

      if (hasShippingError) {
        // If shipping requirements aren't met, go back to shipping tab
        setActiveTab("shipping");

        // Show appropriate errors
        if (!isSpecialCountry && shipCollectValue === undefined) {
          form.setError("ship_collect", {
            message: "Please select whether to ship collect via UPS",
          });
        }

        if (!deliveryMethodValue || deliveryMethodValue.trim() === "") {
          form.setError("delivery_method", {
            message: "Please select a delivery method",
          });
        }

        // Scroll to the first error
        setTimeout(() => {
          const errorElement = document.querySelector(
            '[aria-invalid="true"], .text-destructive'
          );
          if (errorElement) {
            errorElement.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
          }
        }, 100);
      }
    }
  }, [activeTab]);

  async function onSubmit(values: CheckoutFormValues) {
    console.log("onSubmit function called with values:", values);
    if (total < MINIMUM_ORDER_AMOUNT) {
      console.log("Order below minimum amount:", total);
      setShowMinOrderAlert(true);
      return;
    }

    setIsSubmitting(true);
    if (items.length === 0) {
      toast({
        title: "No items in cart",
        description: "Please add items to your cart before checking out",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    // Get the current shipping address
    const shippingAddressId = form.getValues("shippingAddressId");
    console.log("Current shipping address ID:", shippingAddressId);

    if (!shippingAddressId) {
      toast({
        title: "Missing shipping address",
        description: "Please select a shipping address before proceeding",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    const shippingAddress = shippingAddresses.data?.find(
      (addr) => addr.id === shippingAddressId
    );

    // Check if the shipping address is for a special country
    const isSpecialCountry =
      shippingAddress?.country === "Mexico" ||
      shippingAddress?.country === "Puerto Rico" ||
      shippingAddress?.state === "Puerto Rico" ||
      shippingAddress?.state?.includes("Mexico");

    const poFile = form.getValues("poFile");
    const poNumber = form.getValues("poNumber");

    if (values.paymentType === "purchase_order" && !poFile && !poNumber) {
      setIsSubmitting(false);
      form.setError("poFile", {
        message: "Please upload a PO file or enter a PO number",
      });
      form.setError("poNumber", {
        message: "Please upload a PO file or enter a PO number",
      });
      return;
    }

    // Explicitly check ship_collect is defined for non-special countries
    if (!isSpecialCountry && values.ship_collect === undefined) {
      setIsSubmitting(false);
      form.setError("ship_collect", {
        message: "Please select whether to ship collect via UPS",
      });
      return;
    }

    // Only check UPS account number if ship_collect is true and not a special country
    if (
      !isSpecialCountry &&
      values.ship_collect &&
      (!values.ups_account_number || values.ups_account_number.trim() === "")
    ) {
      setIsSubmitting(false);
      form.setError("ups_account_number", {
        message: "Please enter your UPS account number",
      });
      return;
    }

    let fileData: { path: string } | undefined;

    try {
      if (poFile) {
        try {
          fileData = await uploadFileMutation.mutateAsync(poFile);
        } catch (e: any) {
          if (e.message === "The resource already exists") {
            toast({
              title: "PO file already exists",
              description: "File with the same name already exists.",
              variant: "destructive",
            });
          } else {
            toast({
              title: "Error uploading PO file",
              description: "Error uploading file.",
              variant: "destructive",
            });
          }
          setIsSubmitting(false);
          return;
        }
      }

      const orderData = {
        billingAddressId: values.billingAddressId,
        shippingAddressId: values.shippingAddressId,
        ship_collect:
          values.ship_collect === undefined ? false : values.ship_collect,
        ups_account_number: values.ups_account_number,
        delivery_method: values.delivery_method,
        items: values.items,
        poFile: fileData?.path,
        poNumber: values.poNumber,
        paymentType: values.paymentType,
        tax_exempt: values.tax_exempt,
      };

      console.log("Submitting order data:", orderData);

      const result = await addNewOrderMutation.mutateAsync(orderData);
      console.log("Order submission result:", result);

      if (result) {
        setOrderId(result);
        setOrderConfirmed(true);
        goToTab("confirmation");
        clearCart();
        form.reset();
      }
    } catch (e) {
      console.error("Error during order submission:", e);
      if (fileData?.path) {
        await supabaseClient.storage
          .from("maxton-bucket")
          .remove([fileData.path]);
      }
      toast({
        title: "Error placing order",
        description: "An error occurred while placing your order",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <StoreLayout>
      <Head>
        <title>Checkout</title>
      </Head>
      <div className="min-h-screen bg-white">
        <div className="flex-1 space-y-8 p-8 pt-6">
          {/* Header Section */}
          <div className="flex flex-col space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                {/* <Button variant="ghost" size="icon" className="shrink-0" asChild>
                                <Link href="/store/cart">
                                    <ArrowLeft className="h-5 w-5" />
                                    <span className="sr-only">Back to cart</span>
                                </Link>
                            </Button> */}
                <div>
                  <h1 className="text-3xl font-bold tracking-tight">
                    Checkout
                  </h1>
                  <p className="text-muted-foreground">
                    Complete your order details
                  </p>
                </div>
              </div>
              <Badge variant="outline" className="px-6 py-2 text-lg">
                <ShoppingBag className="mr-2 h-4 w-4" />
                <span>
                  {items.length} {items.length === 1 ? "item" : "items"}
                </span>
              </Badge>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={goToTab} className="space-y-8">
            <TabsList className="w-full grid grid-cols-3 gap-4 bg-transparent p-0">
              <TabsTrigger
                value="shipping"
                className="data-[state=active]:bg-muted data-[state=active]:text-foreground"
              >
                <div className="flex items-center gap-2">
                  <Badge
                    variant={activeTab === "shipping" ? "default" : "outline"}
                    className="h-8 w-8 rounded-full p-0 flex items-center justify-center"
                  >
                    1
                  </Badge>
                  <span>Shipping</span>
                </div>
              </TabsTrigger>
              <TabsTrigger
                value="payment"
                disabled={(() => {
                  // Get current shipping address to check if it's a special country
                  const shippingAddressId = form.getValues("shippingAddressId");
                  const shippingAddress = shippingAddresses.data?.find(
                    (addr) => addr.id === shippingAddressId
                  );

                  // Check if it's a special country
                  const isSpecialCountry =
                    shippingAddress?.country === "Mexico" ||
                    shippingAddress?.country === "Puerto Rico" ||
                    shippingAddress?.state === "Puerto Rico" ||
                    shippingAddress?.state?.includes("Mexico");

                  // For special countries, only check delivery method
                  // For regular countries, check both ship_collect and delivery method
                  return isSpecialCountry
                    ? !form.getValues("delivery_method") ||
                        form.getValues("delivery_method").trim() === ""
                    : form.getValues("ship_collect") === undefined ||
                        !form.getValues("delivery_method") ||
                        form.getValues("delivery_method").trim() === "";
                })()}
                className="data-[state=active]:bg-muted data-[state=active]:text-foreground"
              >
                <div className="flex items-center gap-2">
                  <Badge
                    variant={activeTab === "payment" ? "default" : "outline"}
                    className="h-8 w-8 rounded-full p-0 flex items-center justify-center"
                  >
                    2
                  </Badge>
                  <span>Payment</span>
                </div>
              </TabsTrigger>
              <TabsTrigger
                value="confirmation"
                disabled={!orderConfirmed}
                className="data-[state=active]:bg-muted data-[state=active]:text-foreground"
              >
                <div className="flex items-center gap-2">
                  <Badge
                    variant={
                      activeTab === "confirmation" ? "default" : "outline"
                    }
                    className="h-8 w-8 rounded-full p-0 flex items-center justify-center"
                  >
                    3
                  </Badge>
                  <span>Confirmation</span>
                </div>
              </TabsTrigger>
            </TabsList>

            <div
              className={`gap-8 ${
                activeTab === "confirmation" ? "" : "grid lg:grid-cols-12"
              }`}
            >
              <div className="lg:col-span-8 space-y-6">
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    <TabsContent value="shipping" className="space-y-6 mt-0">
                      <BillingInformation checkoutForm={form} />
                      <ShippingInformation checkoutForm={form} />
                      <DeliveryMethod checkoutForm={form} />
                      <div className="flex justify-end">
                        <Button
                          type="button"
                          onClick={() => {
                            // Get the current shipping address
                            const shippingAddressId =
                              form.getValues("shippingAddressId");
                            const shippingAddress =
                              shippingAddresses.data?.find(
                                (addr) => addr.id === shippingAddressId
                              );

                            // Check if the shipping address is for a special country
                            const isSpecialCountry =
                              shippingAddress?.country === "Mexico" ||
                              shippingAddress?.country === "Puerto Rico" ||
                              shippingAddress?.state === "Puerto Rico" ||
                              shippingAddress?.state?.includes("Mexico");

                            // Only validate ship_collect if not a special country
                            if (!isSpecialCountry) {
                              if (
                                form.getValues("ship_collect") !== undefined
                              ) {
                                form.clearErrors("ship_collect");
                              } else {
                                form.setError("ship_collect", {
                                  message:
                                    "Please select whether to ship collect via UPS",
                                });
                              }
                            }

                            // Check if delivery_method has a value and clear error if it does
                            if (
                              form.getValues("delivery_method") &&
                              form.getValues("delivery_method").trim() !== ""
                            ) {
                              form.clearErrors("delivery_method");
                            } else {
                              form.setError("delivery_method", {
                                message: "Please select a delivery method",
                              });
                            }

                            // Proceed to payment tab only if validation passes
                            const shouldProceed = isSpecialCountry
                              ? form.getValues("delivery_method") &&
                                form.getValues("delivery_method").trim() !== ""
                              : form.getValues("ship_collect") !== undefined &&
                                form.getValues("delivery_method") &&
                                form.getValues("delivery_method").trim() !== "";

                            if (shouldProceed) {
                              goToTab("payment");
                            } else {
                              // Scroll to the first error
                              setTimeout(() => {
                                const errorElement = document.querySelector(
                                  '[aria-invalid="true"], .text-destructive'
                                );
                                if (errorElement) {
                                  errorElement.scrollIntoView({
                                    behavior: "smooth",
                                    block: "center",
                                  });
                                }
                              }, 100);
                            }
                          }}
                        >
                          Continue to Payment
                          <ArrowLeft className="ml-2 h-4 w-4 rotate-180" />
                        </Button>
                      </div>
                    </TabsContent>

                    <TabsContent value="payment" className="space-y-6 mt-0">
                      <Card className="hover:shadow-md transition-all">
                        <CardHeader className="flex flex-row items-center gap-4">
                          <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                            <CreditCard className="h-5 w-5 text-primary" />
                          </div>
                          <div>
                            <CardTitle>Payment Method</CardTitle>
                            <CardDescription>
                              Select your preferred payment method
                            </CardDescription>
                          </div>
                        </CardHeader>
                        <CardContent className="grid gap-6">
                          <div className="grid grid-cols-2 gap-4">
                            <Card
                              className="relative cursor-pointer hover:border-primary [&:has(input:checked)]:border-primary"
                              onClick={() =>
                                form.setValue("paymentType", "credit_card")
                              }
                            >
                              <input
                                type="radio"
                                name="paymentType"
                                value="credit_card"
                                className="sr-only"
                                checked={
                                  form.watch("paymentType") === "credit_card"
                                }
                                onChange={() => {}}
                              />
                              <CardContent className="pt-6 text-center">
                                <CreditCard className="mx-auto h-6 w-6 mb-2" />
                                <p className="font-medium">Credit Card</p>
                              </CardContent>
                            </Card>
                            <Card
                              className="relative cursor-pointer hover:border-primary [&:has(input:checked)]:border-primary"
                              onClick={() =>
                                form.setValue("paymentType", "purchase_order")
                              }
                            >
                              <input
                                type="radio"
                                name="paymentType"
                                value="purchase_order"
                                className="sr-only"
                                checked={
                                  form.watch("paymentType") === "purchase_order"
                                }
                                onChange={() => {}}
                              />
                              <CardContent className="pt-6 text-center">
                                <Receipt className="mx-auto h-6 w-6 mb-2" />
                                <p className="font-medium">PO Number</p>
                              </CardContent>
                            </Card>
                          </div>

                          {form.watch("paymentType") === "credit_card" ? (
                            <div className="rounded-lg border bg-muted/50 p-4">
                              <p className="text-sm text-muted-foreground">
                                Credit cards are no longer accepted online. If
                                you wish to pay via credit card instead of
                                submitting a purchase order, payments may be
                                made with one of the 4 major bank credit/debit
                                cards by calling Maxton at{" "}
                                <a
                                  className="underline text-primary hover:text-primary/80"
                                  href="tel:************"
                                >
                                  ************
                                </a>{" "}
                                or faxing{" "}
                                <a
                                  className="underline text-primary hover:text-primary/80"
                                  href="tel:************"
                                >
                                  ************
                                </a>
                                .
                              </p>
                            </div>
                          ) : (
                            <div className="space-y-4">
                              <div className="flex items-center justify-end">
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() =>
                                    setSwitchToAttachment(!isAttachment)
                                  }
                                  className="flex items-center gap-2"
                                >
                                  {isAttachment ? (
                                    <>
                                      <Receipt className="h-4 w-4" />
                                      Switch to PO Number
                                    </>
                                  ) : (
                                    <>
                                      <Package className="h-4 w-4" />
                                      Switch to PO File
                                    </>
                                  )}
                                </Button>
                              </div>

                              <FormField
                                control={form.control}
                                name="poNumber"
                                render={({ field }) => (
                                  <FormItem
                                    data-show={!isAttachment}
                                    className="data-[show=true]:block hidden"
                                  >
                                    <FormLabel>PO Number</FormLabel>
                                    <FormControl>
                                      <Input
                                        placeholder="Enter your purchase order number"
                                        {...field}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name="poFile"
                                render={({
                                  field: { value, onChange, ...field },
                                }) => (
                                  <FormItem
                                    data-show={isAttachment}
                                    className="data-[show=true]:block hidden"
                                  >
                                    <FormLabel>Attach PO Document</FormLabel>
                                    <FormControl>
                                      <div className="flex flex-col gap-2">
                                        <Input
                                          type="file"
                                          accept=".pdf,.doc,.docx"
                                          onChange={(e) => {
                                            const file = e.target.files?.[0];
                                            if (
                                              file &&
                                              file.size <= MAX_FILE_SIZE
                                            ) {
                                              onChange(file);
                                            }
                                          }}
                                          {...field}
                                        />
                                        <p className="text-xs text-muted-foreground">
                                          Accepted file types: PDF, DOC, DOCX
                                          (Max size: 5MB)
                                        </p>
                                      </div>
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          )}
                        </CardContent>
                      </Card>

                      <TaxExemptCard
                        form={form}
                        shippingAddresses={shippingAddresses}
                      />
                    </TabsContent>

                    <TabsContent
                      value="confirmation"
                      className="space-y-6 mt-0"
                    >
                      <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                          <div className="rounded-full bg-green-100 p-3 mb-4">
                            <CheckCircle2 className="h-12 w-12 text-green-600" />
                          </div>
                          <h2 className="text-2xl font-bold mb-2">
                            Order Confirmed!
                          </h2>
                          <p className="text-muted-foreground mb-6">
                            Thank you for your order. We&apos;ve sent you a
                            confirmation email with the details.
                          </p>
                        </CardContent>
                      </Card>

                      {orderId && (
                        <OrderConfirmationDetails orderId={orderId} />
                      )}

                      <div className="flex justify-end gap-4">
                        <Button variant="outline" asChild>
                          <Link href="/store/orders">View All Orders</Link>
                        </Button>
                        <Button asChild>
                          <Link href="/store/products">Continue Shopping</Link>
                        </Button>
                      </div>
                    </TabsContent>
                  </form>
                </Form>
              </div>

              {activeTab !== "confirmation" && (
                <div className="lg:col-span-4 space-y-6">
                  {customerData.isLoading ? (
                    <OrderSummarySkeleton />
                  ) : (
                    <>
                      <OrderSummary
                        items={items}
                        activeTab={activeTab}
                        orderConfirmed={orderConfirmed}
                        isSubmitting={isSubmitting}
                        form={form}
                        uploadFileMutation={uploadFileMutation}
                        addNewOrderMutation={addNewOrderMutation}
                        onSubmit={onSubmit}
                        shippingAddresses={shippingAddresses}
                      />
                    </>
                  )}
                </div>
              )}
            </div>
          </Tabs>
        </div>
      </div>

      <AlertDialog open={showMinOrderAlert} onOpenChange={setShowMinOrderAlert}>
        <AlertDialogContent className="sm:max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-amber-700">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Minimum Order Requirement
            </AlertDialogTitle>
            <AlertDialogDescription>
              Your order total must be at least{" "}
              {formatPrice(MINIMUM_ORDER_AMOUNT)} to proceed with the order.
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex justify-between items-center">
              <span className="font-medium">Current total:</span>
              <span className="text-lg">{formatPrice(total)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium">Amount needed:</span>
              <span className="text-lg text-amber-600">
                {formatPrice(MINIMUM_ORDER_AMOUNT - total)}
              </span>
            </div>
          </div>

          <div className="flex justify-end">
            <Link
              href="/store/products"
              className="bg-neutral-900 p-3 rounded-md text-neutral-50 hover:bg-neutral-900/90 dark:bg-neutral-50 dark:text-neutral-900 dark:hover:bg-neutral-50/90"
            >
              Continue Shopping
            </Link>
          </div>
        </AlertDialogContent>
      </AlertDialog>
    </StoreLayout>
  );
}

interface TaxExemptCardProps {
  form: UseFormReturn<CheckoutFormValues>;
  shippingAddresses: any;
}

function TaxExemptCard({ form, shippingAddresses }: TaxExemptCardProps) {
  // Get the current shipping address
  const shippingAddressId = form.watch("shippingAddressId");
  const shippingAddress = shippingAddresses.data?.find(
    (addr: any) => addr.id === shippingAddressId
  );

  // Only show Tax Exempt card if shipping state is Nevada
  if (shippingAddress?.state !== "Nevada" && shippingAddress?.state !== "NV") {
    return null;
  }

  // Calculate current tax rate for the shipping city
  const currentTaxRate = getTaxRateForCity(
    shippingAddress?.city || "",
    shippingAddress?.state || ""
  );
  const county = getCountyForCity(shippingAddress?.city || "");

  return (
    <Card className="hover:shadow-md transition-all">
      <CardHeader className="flex flex-row items-center gap-4">
        <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
          <FileText className="h-5 w-5 text-primary" />
        </div>
        <div>
          <CardTitle>Tax Exempt</CardTitle>
          <CardDescription>
            Indicate if this order is tax exempt
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="rounded-lg border bg-muted/50 p-4">
            <div className="mb-3">
              <p className="text-sm font-medium text-foreground mb-1">
                Tax Information for {shippingAddress?.city || "your location"}
              </p>
              <p className="text-xs text-muted-foreground">
                {county ? `${county} County` : "Nevada"} • Tax Rate: {currentTaxRate.toFixed(3)}%
              </p>
            </div>
            <p className="text-sm text-muted-foreground">
              Maxton only collects sales tax for the state of Nevada. If you are
              a non-taxable entity, please email tax exempt certificate to{" "}
              <a
                className="text-primary underline"
                href="mailto:<EMAIL>"
              >
                <EMAIL>
              </a>{" "}
              or fax it to{" "}
              <a className="text-primary underline" href="tel:+17757821701">
                (*************
              </a>
              . All tax exempt claims must be backed by a tax exempt certificate
              as proof prior to shipment of product.
            </p>
          </div>
          <FormField
            control={form.control}
            name="tax_exempt"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormControl>
                  <RadioGroup
                    onValueChange={(value) => {
                      field.onChange(value === "true");
                    }}
                    defaultValue={field.value ? "true" : "false"}
                    className="flex flex-col space-y-2"
                  >
                    <Label className="flex items-center gap-3 rounded-lg border p-4 cursor-pointer [&:has([data-state=checked])]:border-primary">
                      <RadioGroupItem value="true" id="tax-exempt-true" />
                      <span>
                        This is a tax exempt order and I will provide the
                        required documentation
                      </span>
                    </Label>
                    <Label className="flex items-center gap-3 rounded-lg border p-4 cursor-pointer [&:has([data-state=checked])]:border-primary">
                      <RadioGroupItem value="false" id="tax-exempt-false" />
                      <span>This order is not tax exempt</span>
                    </Label>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </CardContent>
    </Card>
  );
}

function BillingInformation({
  checkoutForm,
}: {
  checkoutForm: UseFormReturn<CheckoutFormValues>;
}) {
  return (
    <Card className="hover:shadow-md transition-all">
      <CardHeader className="flex flex-row items-center gap-4">
        <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
          <Receipt className="h-5 w-5 text-primary" />
        </div>
        <div>
          <CardTitle>Billing Information</CardTitle>
          <CardDescription>Select your billing address</CardDescription>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <BillingAddresses checkoutForm={checkoutForm} />
      </CardContent>
    </Card>
  );
}

function ShippingInformation({
  checkoutForm,
}: {
  checkoutForm: UseFormReturn<CheckoutFormValues>;
}) {
  const [useBillingAddress, setUseBillingAddress] = useState(false);
  const userData = useAuthStore((state) => state.data);
  const userId = userData.id;
  const customerData = useGetCustomerQuery(userId);
  const billingAddressId = checkoutForm.watch("billingAddressId");
  const billingAddresses = useGetBillingAddressesQuery(userId);
  const shippingAddresses = useGetShippingAddressesQuery(userId);
  const addShippingAddressMutation = useAddShippingAddressMutation(userId);
  const [selectedBillingAddress, setSelectedBillingAddress] = useState<{
    id: string;
    address: string;
    address_2: string | null;
    address_type: "residential" | "commercial" | null;
    city: string;
    country: string;
    state: string;
    zip_code: string;
    company_name: string | null;
    contact_name: string | null;
    contact_email: string | null;
    contact_number: string | null;
  } | null>(null);

  // Effect to update shipping address when billing address is selected
  useEffect(() => {
    if (useBillingAddress && billingAddressId && billingAddresses.data) {
      console.log(
        "Updating shipping address with billing address:",
        billingAddressId
      );
      const billingAddress = billingAddresses.data.find(
        (addr) => addr.id === billingAddressId
      );

      console.log("Found billing address:", billingAddress);
      console.log("Customer data:", customerData.data);

      if (billingAddress) {
        // Convert billing address to display format with proper types
        const addressData = {
          id: billingAddress.id,
          address: billingAddress.address || "",
          address_2: billingAddress.address_2,
          address_type: (billingAddress.address_type || "commercial") as
            | "residential"
            | "commercial",
          city: billingAddress.city || "",
          country: billingAddress.country || "",
          state: billingAddress.state || "",
          zip_code: billingAddress.zip_code || "",
          company_name: billingAddress.company_name || null,
          contact_name:
            customerData.data?.customer?.primary_contact_name || null,
          contact_email: userData.email || null,
          contact_number: customerData.data?.customer?.phone || null,
        };

        console.log("Created address data for display:", addressData);
        setSelectedBillingAddress(addressData);

        // Check if a shipping address with the same details already exists
        const existingShippingAddress = shippingAddresses.data?.find(
          (addr) =>
            addr.address === billingAddress.address &&
            addr.address_2 === billingAddress.address_2 &&
            addr.city === billingAddress.city &&
            addr.state === billingAddress.state &&
            addr.zip_code === billingAddress.zip_code &&
            addr.country === billingAddress.country &&
            addr.option_name === "Created from billing address"
        );

        if (existingShippingAddress) {
          console.log(
            "Using existing shipping address:",
            existingShippingAddress
          );
          checkoutForm.setValue(
            "shippingAddressId",
            existingShippingAddress.id,
            {
              shouldValidate: false,
              shouldDirty: true,
              shouldTouch: true,
            }
          );

          handleAddressUpdate(billingAddress, existingShippingAddress.id);
        } else {
          // Create a new shipping address from billing address with proper type casting
          const shippingAddressData = {
            address: billingAddress.address || "",
            address_2: billingAddress.address_2 || "",
            address_type: (billingAddress.address_type || "commercial") as
              | "residential"
              | "commercial",
            city: billingAddress.city || "",
            country: billingAddress.country || "",
            state: billingAddress.state || "",
            zip_code: billingAddress.zip_code || "",
            contact_name:
              customerData.data?.customer?.primary_contact_name || "",
            contact_email: userData.email || "",
            contact_number: customerData.data?.customer?.phone || "",
            company_name: billingAddress.company_name || "",
            option_name: "Created from billing address",
            default: false,
          };

          console.log(
            "Creating new shipping address with data:",
            shippingAddressData
          );
          console.log(
            "Customer primary contact name:",
            customerData.data?.customer?.primary_contact_name
          );
          console.log(
            "Billing address company name:",
            billingAddress.company_name
          );

          // Create new shipping address
          addShippingAddressMutation
            .mutateAsync(shippingAddressData)
            .then((newShippingAddress) => {
              console.log("New shipping address created:", newShippingAddress);
              // Set the new shipping address ID in the form
              checkoutForm.setValue(
                "shippingAddressId",
                newShippingAddress.id,
                {
                  shouldValidate: false,
                  shouldDirty: true,
                  shouldTouch: true,
                }
              );

              handleAddressUpdate(billingAddress, newShippingAddress.id);
            })
            .catch((error) => {
              console.error("Error creating shipping address:", error);
              toast({
                title: "Error",
                description:
                  "Failed to create shipping address from billing address",
                variant: "destructive",
              });
            });
        }
      }
    } else {
      if (!useBillingAddress) {
        console.log("Clearing shipping address");
        checkoutForm.setValue("shippingAddressId", "", {
          shouldValidate: false,
        });
        setSelectedBillingAddress(null);
      }
    }
  }, [useBillingAddress, billingAddressId, billingAddresses.data]);

  // Helper function to handle address updates
  const handleAddressUpdate = (
    billingAddress: any,
    shippingAddressId: string
  ) => {
    // Check if billing address is in a special country/state
    const isSpecialCountry =
      billingAddress.country === "Mexico" ||
      billingAddress.country === "Puerto Rico" ||
      billingAddress.state === "Puerto Rico" ||
      billingAddress.state?.includes("Mexico");

    // For special countries, ship_collect must be false
    if (isSpecialCountry) {
      checkoutForm.setValue("ship_collect", false, {
        shouldValidate: false,
      });
      // Clear UPS account number if it was set
      checkoutForm.setValue("ups_account_number", "", {
        shouldValidate: false,
      });
    } else {
      // For non-special countries, reset ship_collect to undefined to force selection
      checkoutForm.setValue("ship_collect", undefined, {
        shouldValidate: false,
      });
      checkoutForm.clearErrors("ship_collect");
    }

    // Always reset delivery_method when changing shipping address
    checkoutForm.setValue("delivery_method", "", {
      shouldValidate: false,
    });
    checkoutForm.clearErrors("delivery_method");
  };

  return (
    <Card className="hover:shadow-md transition-all">
      <CardHeader className="flex flex-row items-center gap-4">
        <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
          <Truck className="h-5 w-5 text-primary" />
        </div>
        <div>
          <CardTitle>Shipping Information</CardTitle>
          <CardDescription>Select your shipping address</CardDescription>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center space-x-2 pb-4">
          <Checkbox
            id="use-billing-address"
            checked={useBillingAddress}
            onCheckedChange={(checked) => {
              setUseBillingAddress(checked === true);
              if (!checked) {
                // Clear shipping address when unchecking
                checkoutForm.setValue("shippingAddressId", "", {
                  shouldValidate: false,
                });
                setSelectedBillingAddress(null);
              }
            }}
          />
          <label
            htmlFor="use-billing-address"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Use billing address as shipping address
          </label>
        </div>

        {useBillingAddress && selectedBillingAddress ? (
          <div className="rounded-lg border p-4 space-y-2">
            <p className="font-medium">Selected Billing Address:</p>
            <div className="space-y-1 text-sm">
              {selectedBillingAddress.contact_name && (
                <p>{selectedBillingAddress.contact_name}</p>
              )}
              {selectedBillingAddress.company_name && (
                <p>{selectedBillingAddress.company_name}</p>
              )}
              <p>{selectedBillingAddress.address}</p>
              {selectedBillingAddress.address_2 && (
                <p>{selectedBillingAddress.address_2}</p>
              )}
              <p>
                {selectedBillingAddress.city}, {selectedBillingAddress.state}{" "}
                {selectedBillingAddress.zip_code}
              </p>
              <p>{selectedBillingAddress.country}</p>
              {selectedBillingAddress.contact_number && (
                <p>Phone: {selectedBillingAddress.contact_number}</p>
              )}
              {selectedBillingAddress.contact_email && (
                <p>Email: {selectedBillingAddress.contact_email}</p>
              )}
            </div>
          </div>
        ) : !useBillingAddress ? (
          <ShippingAddresses checkoutForm={checkoutForm} />
        ) : null}
      </CardContent>
    </Card>
  );
}

interface DeliveryMethodProps {
  checkoutForm: UseFormReturn<CheckoutFormValues>;
}

function DeliveryMethod({ checkoutForm }: DeliveryMethodProps) {
  const userData = useAuthStore((state) => state.data);
  const userId = userData.id;
  const shippingAddressId = checkoutForm.watch("shippingAddressId");
  const isShipCollect = checkoutForm.watch("ship_collect");
  const shippingAddresses = useGetShippingAddressesQuery(userId);
  const billingAddresses = useGetBillingAddressesQuery(userId);
  const [selectedAddress, setSelectedAddress] =
    useState<ShippingAddress | null>(null);
  const useBillingAddressRef = useRef(false);

  useEffect(() => {
    if (
      !shippingAddressId ||
      (!shippingAddresses.data && !billingAddresses.data)
    )
      return;

    // Check if the shipping address ID matches any billing address ID
    const billingAddress = billingAddresses.data?.find(
      (addr) => addr.id === shippingAddressId
    );

    if (billingAddress) {
      // Convert billing address to shipping address format
      const shippingAddress: ShippingAddress = {
        id: billingAddress.id,
        address: billingAddress.address || null,
        address_2: billingAddress.address_2,
        address_type: billingAddress.address_type || "commercial",
        city: billingAddress.city || null,
        country: billingAddress.country || null,
        state: billingAddress.state || null,
        zip_code: billingAddress.zip_code || null,
        contact_name: billingAddress.company_name || null,
        contact_email: null,
        contact_number: null,
        option_name: null,
        default: false,
        created_at: billingAddress.created_at,
        updated_at: billingAddress.updated_at || billingAddress.created_at,
        customer_id: userId,
      };
      setSelectedAddress(shippingAddress);
      useBillingAddressRef.current = true;
    } else {
      // Regular shipping address
      const address = shippingAddresses.data?.find(
        (addr) => addr.id === shippingAddressId
      );
      if (address) {
        setSelectedAddress(address);
        useBillingAddressRef.current = false;
      }
    }
  }, [shippingAddressId, shippingAddresses.data, billingAddresses.data]);

  // Check for all special country conditions
  const isSpecialCountry = selectedAddress
    ? selectedAddress.country === "Mexico" ||
      selectedAddress.country === "Puerto Rico" ||
      selectedAddress.state === "Puerto Rico" ||
      selectedAddress.state?.includes("Mexico")
    : false;

  const upsOptions = [
    "UPS Ground",
    "UPS Next Day Air",
    "UPS 2nd Day Air",
    "UPS 3 Day Select",
    "UPS Next Day Air Saver",
    "UPS Next Day Air Early A.M.",
    "UPS 2nd Day Air A.M.",
  ];

  return (
    <Card className="hover:shadow-md transition-all">
      <CardHeader className="flex flex-row items-center gap-4">
        <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
          <Package className="h-5 w-5 text-primary" />
        </div>
        <div>
          <CardTitle>Delivery Method</CardTitle>
          <CardDescription>
            Select your preferred shipping method
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {!selectedAddress ? (
          <div className="flex items-center justify-center p-6 text-muted-foreground border rounded-lg bg-muted/50">
            Please select a shipping address first
          </div>
        ) : isSpecialCountry ? (
          <div className="space-y-6">
            <div className="rounded-lg border bg-muted/50 p-4">
              <p className="text-sm text-muted-foreground">
                Prepaid delivery is used for shipments to{" "}
                {selectedAddress.country === "Mexico" ||
                selectedAddress.country === "Puerto Rico"
                  ? selectedAddress.country
                  : selectedAddress.state}
              </p>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium flex items-center gap-2">
                <Package className="h-4 w-4" />
                United Parcel Service
              </h3>
              <FormField
                control={checkoutForm.control}
                name="delivery_method"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        value={field.value}
                        onValueChange={(value) => {
                          field.onChange(value);
                          checkoutForm.clearErrors("delivery_method");
                        }}
                        className="grid gap-2"
                      >
                        {upsOptions.map((option) => (
                          <Label
                            key={option}
                            className="flex items-center gap-3 rounded-lg border p-4 cursor-pointer [&:has([data-state=checked])]:border-primary"
                          >
                            <RadioGroupItem value={option} id={option} />
                            <span>{option}</span>
                          </Label>
                        ))}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            <FormField
              control={checkoutForm.control}
              name="ship_collect"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    Ship Collect UPS<span className="text-destructive">*</span>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value === "yes");
                        checkoutForm.clearErrors("ship_collect");
                      }}
                      value={
                        field.value === true
                          ? "yes"
                          : field.value === false
                          ? "no"
                          : undefined
                      }
                      className="flex items-center gap-4"
                    >
                      <Label className="flex items-center gap-3 rounded-lg border p-4 cursor-pointer [&:has([data-state=checked])]:border-primary">
                        <RadioGroupItem value="yes" id="ship-collect-yes" />
                        <span>Yes</span>
                      </Label>
                      <Label className="flex items-center gap-3 rounded-lg border p-4 cursor-pointer [&:has([data-state=checked])]:border-primary">
                        <RadioGroupItem value="no" id="ship-collect-no" />
                        <span>No</span>
                      </Label>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {isShipCollect && (
              <FormField
                control={checkoutForm.control}
                name="ups_account_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      UPS Account Number
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your UPS account number"
                        {...field}
                        required={isShipCollect}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                          if (e.target.value.trim() !== "") {
                            checkoutForm.clearErrors("ups_account_number");
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="space-y-4">
              <h3 className="font-medium flex items-center gap-2">
                <Package className="h-4 w-4" />
                United Parcel Service<span className="text-destructive">*</span>
              </h3>
              <FormField
                control={checkoutForm.control}
                name="delivery_method"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        value={field.value}
                        onValueChange={(value) => {
                          field.onChange(value);
                          checkoutForm.clearErrors("delivery_method");
                        }}
                        className="grid gap-2"
                      >
                        {upsOptions.map((option) => (
                          <Label
                            key={option}
                            className="flex items-center gap-3 rounded-lg border p-4 cursor-pointer [&:has([data-state=checked])]:border-primary"
                          >
                            <RadioGroupItem value={option} id={option} />
                            <span>{option}</span>
                          </Label>
                        ))}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

interface OrderSummaryProps {
  items: CartItem[];
  activeTab: string;
  orderConfirmed: boolean;
  isSubmitting: boolean;
  form: UseFormReturn<CheckoutFormValues>;
  uploadFileMutation: any;
  addNewOrderMutation: any;
  onSubmit: (values: CheckoutFormValues) => void;
  shippingAddresses: any;
}

function OrderSummary({
  items,
  activeTab,
  orderConfirmed,
  isSubmitting,
  form,
  uploadFileMutation,
  addNewOrderMutation,
  onSubmit,
  shippingAddresses,
}: OrderSummaryProps) {
  const cartSubtotal = useCartStore((state) => state.getSubtotal)();
  const additionalAmount = useCartStore((state) => state.getAdditionalAmount)();
  const [subtotal, setSubtotal] = useState(0);
  const [taxAmount, setTaxAmount] = useState(0);
  const [taxRate, setTaxRate] = useState(0);
  const [total, setTotal] = useState(0);

  useEffect(function calculateTotals() {
    const orderSubtotal = cartSubtotal + additionalAmount;
    setSubtotal(cartSubtotal);

    // Get shipping address for tax calculation
    const shippingAddressId = form.watch("shippingAddressId");
    const isTaxExempt = form.watch("tax_exempt");
    const shippingAddress = shippingAddresses.data?.find(
      (addr: any) => addr.id === shippingAddressId
    );

    let currentTaxRate = 0;
    let currentTaxAmount = 0;

    if (shippingAddress?.state === "Nevada" || shippingAddress?.state === "NV") {
      currentTaxRate = getTaxRateForCity(shippingAddress.city || "", shippingAddress.state || "");
      currentTaxAmount = calculateTaxAmount(orderSubtotal, currentTaxRate, isTaxExempt);
    }

    setTaxRate(currentTaxRate);
    setTaxAmount(currentTaxAmount);
    setTotal(orderSubtotal + currentTaxAmount);
  }, [cartSubtotal, additionalAmount, form.watch("shippingAddressId"), form.watch("tax_exempt"), shippingAddresses.data]);

  return (
    <Card className="sticky top-44">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingBag className="h-5 w-5" />
          Order Summary
        </CardTitle>
        <CardDescription>Review your order details</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {items.map((item, index) => {
            return (
              <OrderSummaryItem
                key={`order-summary-item-${item.id}-${index}`}
                item={item}
              />
            );
          })}

          <Separator />

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Subtotal</span>
              <span>{formatPrice(subtotal)}</span>
            </div>
            {additionalAmount > 0 && (
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Options Total</span>
                <span className="text-green-600">
                  +{formatPrice(additionalAmount)}
                </span>
              </div>
            )}
            {taxRate > 0 && (
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">
                  Tax ({taxRate}%)
                  {form.watch("tax_exempt") && (
                    <span className="text-orange-600 ml-1">(Exempt)</span>
                  )}
                </span>
                <span className={form.watch("tax_exempt") ? "line-through text-muted-foreground" : ""}>
                  {formatPrice(taxAmount)}
                </span>
              </div>
            )}
          </div>

          <Separator />

          <div className="flex justify-between font-medium">
            <span>Total</span>
            <span className="text-lg">{formatPrice(total)}</span>
          </div>

          <div className="text-sm text-muted-foreground">
            <p>Shipping costs will be calculated at time of shipment.</p>
            <p>For shipping estimates, please contact sales.</p>
          </div>

          {/* Place Order Button - Only show on payment tab and not after order is confirmed */}
          {activeTab === "payment" && !orderConfirmed && (
            <div className="pt-4">
              {(() => {
                const isPlacingOrder =
                  isSubmitting ||
                  uploadFileMutation.isPending ||
                  addNewOrderMutation.isPending ||
                  form.formState.isSubmitting ||
                  form.formState.isLoading;

                // Check form validity in much more detail
                const validationState = {
                  isValid: form.formState.isValid,
                  isDirty: form.formState.isDirty,
                  errors: form.formState.errors,
                  dirtyFields: form.formState.dirtyFields,
                  touchedFields: form.formState.touchedFields,
                  defaultValues: form.formState.defaultValues,
                  isUpsAccountRequired:
                    form.getValues("ship_collect") === true &&
                    (!form.getValues("ups_account_number") ||
                      form.getValues("ups_account_number")?.trim() === ""),
                  isPONumberOrFileRequired:
                    form.getValues("paymentType") === "purchase_order" &&
                    !form.getValues("poFile") &&
                    !form.getValues("poNumber"),
                  ship_collect: form.getValues("ship_collect"),
                  delivery_method: form.getValues("delivery_method"),
                  poNumber: form.getValues("poNumber"),
                  poFile: form.getValues("poFile") !== undefined,
                  ups_account_number: form.getValues("ups_account_number"),
                  billingAddressId: form.getValues("billingAddressId"),
                  shippingAddressId: form.getValues("shippingAddressId"),
                  paymentType: form.getValues("paymentType"),
                };

                console.log("Detailed validation state:", validationState);

                return (
                  <Button
                    variant="default"
                    disabled={isPlacingOrder}
                    aria-disabled={isPlacingOrder}
                    type="button"
                    onClick={async () => {
                      console.log("Place order button clicked");

                      // Check if shipping address is for a special country
                      const shippingAddressId =
                        form.getValues("shippingAddressId");
                      const shippingAddress = shippingAddresses.data?.find(
                        (addr) => addr.id === shippingAddressId
                      );

                      const isSpecialCountry =
                        shippingAddress?.country === "Mexico" ||
                        shippingAddress?.country === "Puerto Rico" ||
                        shippingAddress?.state === "Puerto Rico" ||
                        shippingAddress?.state?.includes("Mexico");

                      // Check UPS account number if ship_collect is true (not checking for undefined anymore)
                      if (
                        !isSpecialCountry &&
                        form.getValues("ship_collect") === undefined
                      ) {
                        form.setError("ship_collect", {
                          message:
                            "Please select whether to ship collect via UPS",
                        });

                        // Scroll to the error
                        setTimeout(() => {
                          const errorElement = document.querySelector(
                            '[aria-invalid="true"], .text-destructive'
                          );
                          if (errorElement) {
                            errorElement.scrollIntoView({
                              behavior: "smooth",
                              block: "center",
                            });
                          }
                        }, 100);

                        return;
                      } else if (
                        !isSpecialCountry &&
                        form.getValues("ship_collect") === true &&
                        (!form.getValues("ups_account_number") ||
                          form.getValues("ups_account_number")?.trim() === "")
                      ) {
                        form.setError("ups_account_number", {
                          message: "Please enter your UPS account number",
                        });

                        // Scroll to the error
                        setTimeout(() => {
                          const errorElement = document.querySelector(
                            '[aria-invalid="true"], .text-destructive'
                          );
                          if (errorElement) {
                            errorElement.scrollIntoView({
                              behavior: "smooth",
                              block: "center",
                            });
                          }
                        }, 100);

                        return;
                      }

                      // Manual validation before submission - await the promise
                      const isValid = await form.trigger();
                      console.log("Form trigger result:", isValid);

                      if (isValid) {
                        // Only proceed if validation passed
                        const data = form.getValues();
                        console.log("Form data after validation:", data);
                        onSubmit(data);
                      } else {
                        console.log("Form validation failed, not submitting");
                        // Scroll to the first error
                        setTimeout(() => {
                          const errorElement = document.querySelector(
                            '[aria-invalid="true"], .text-destructive'
                          );
                          if (errorElement) {
                            errorElement.scrollIntoView({
                              behavior: "smooth",
                              block: "center",
                            });
                          }
                        }, 100);
                      }
                    }}
                    className="w-full h-12 text-lg"
                  >
                    {isPlacingOrder ? (
                      <>
                        <SvgSpinners90Ring className="mr-2" />
                        Processing...
                      </>
                    ) : (
                      <>
                        Place Order
                        <ShoppingCart className="ml-2 h-5 w-5" />
                      </>
                    )}
                  </Button>
                );
              })()}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

interface OrderSummaryItemProps {
  item: CartItem;
}

function OrderSummaryItem({ item }: OrderSummaryItemProps) {
  const totalPrice = item.price * item.quantity;
  const optionPriceTotal =
    item.selectedOptions?.reduce(
      (total, option) => total + (option.price || 0) * item.quantity,
      0
    ) || 0;

  const image = useGetImage(item.image ?? "").data ?? item.image;

  return (
    <div className="flex items-start gap-4">
      <div className="h-16 w-16 rounded-lg border bg-muted/50 overflow-hidden">
        {item.image && (
          <img
            src={image}
            alt={item.name}
            className="h-full w-full object-cover"
          />
        )}
      </div>
      <div className="flex-1 space-y-1">
        <div className="flex justify-between">
          <p className="font-medium line-clamp-1">{item.name}</p>
          <p className="font-medium">
            {formatPrice(totalPrice + optionPriceTotal)}
          </p>
        </div>
        <p className="text-sm text-muted-foreground">Qty: {item.quantity}</p>
        {item.selectedOptions?.map((option, index) => {
          const price = option.price
            ? ` (+${formatPrice(option.price * item.quantity)})`
            : "";
          return (
            <p
              key={`${item.id}-${option.name}-${option.value}-${index}`}
              className="text-sm text-muted-foreground flex justify-between"
            >
              <span>
                {option.name}: {option.value}
              </span>
              {price && <span className="text-green-600">{price}</span>}
            </p>
          );
        })}
      </div>
    </div>
  );
}

function OrderSummarySkeleton() {
  return (
    <Card className="sticky top-6">
      <CardHeader>
        <div className="flex items-center gap-2">
          <Skeleton className="h-5 w-5 rounded" />
          <Skeleton className="h-6 w-32" />
        </div>
        <Skeleton className="h-4 w-40 mt-1" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {[1, 2].map((i) => (
            <div key={i} className="flex items-start gap-4">
              <Skeleton className="h-16 w-16 rounded-lg" />
              <div className="flex-1 space-y-2">
                <div className="flex justify-between">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-5 w-20" />
                </div>
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-40" />
              </div>
            </div>
          ))}

          <Separator />

          <div className="space-y-2">
            <div className="flex justify-between">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-20" />
            </div>
            <div className="flex justify-between">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-20" />
            </div>
          </div>

          <Separator />

          <div className="flex justify-between">
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-24" />
          </div>

          <div className="space-y-1">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function OrderConfirmationDetails({ orderId }: { orderId: string }) {
  const userData = useAuthStore((state) => state.data);
  const userId = userData?.id;
  const order = useGetOrderByIdQuery(userId, orderId);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(
    function hideLoading() {
      if (order.isSuccess) {
        setIsLoading(false);
      }
    },
    [order.isSuccess]
  );

  if (isLoading) {
    return <OrderDetailsSkeleton />;
  }

  return <OrderDetails order={order.data ?? ({} as OrderWithItems)} />;
}

interface OrderDetailsProps {
  order: OrderWithItems;
}

function OrderDetails({ order }: OrderDetailsProps) {
  const orderId = order.id;
  const items = order.order_items ?? [];
  const shippingAddress = order.shipping_addresses ?? [];

  const date = order.created_at;
  const dateFormat = new Intl.DateTimeFormat("en-US", {
    dateStyle: "medium",
    timeStyle: "short",
  });

  const dateString = date
    ? dateFormat.format(new Date(date))
    : new Date(date).toString();

  const orderStatus = order.order_statuses?.at(0);
  const totalAmount = order.total_amount ?? 0;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-base font-medium">
          Order #{orderId}
        </CardTitle>
        <OrderStatusBadge status={orderStatus?.status ?? "pending"} />
      </CardHeader>
      <CardContent className="grid gap-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <ShoppingCart className="h-5 w-5 text-muted-foreground" />
            <div className="flex flex-col gap-2">
              <p className="text-sm font-medium">Order Details</p>
              <p className="text-sm text-muted-foreground">{dateString}</p>
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <p className="text-sm font-medium">
              Total: {formatPrice(totalAmount)}
            </p>
          </div>
        </div>
        <Separator />
        <div className="flex flex-col gap-4">
          <h3 className="text-sm font-medium">Items</h3>
          <div className="grid gap-4">
            {items && items.length > 0 ? (
              items.map((item) => {
                const options =
                  (item.options as unknown as OrderItemOption[]) ?? [];
                const optionString = options
                  .map((option) => `${option.name}: ${option.value}`)
                  .join(", ");

                let itemTotal = 0;
                if (item.item_price) {
                  itemTotal = item.item_price * item.quantity;
                } else {
                  itemTotal = item.products.price * item.quantity;
                }

                const image =
                  useGetImage(item.products.image ?? "").data ??
                  item.products.image;

                return (
                  <div
                    key={`${item.products.id}-${item.quantity}-${item.products.name}-${optionString}`}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-start gap-4">
                      <div className="aspect-square h-16 w-16 rounded-lg">
                        <img
                          src={`${image}`}
                          alt={item.products.name}
                          className="h-full w-full rounded-lg object-cover"
                        />
                      </div>
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-medium">
                          {item.products.name}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Quantity: {item.quantity}
                        </p>
                        {options &&
                          options.length > 0 &&
                          options.map((itemOption, index) => {
                            const option =
                              itemOption as unknown as OrderItemOption;
                            const optionPrice = option.price
                              ? formatPrice(option.price)
                              : "";

                            return (
                              <p
                                key={`${item.products.id}-${option.name}-${option.name}-${option.value}-${index}`}
                                className="text-sm text-muted-foreground"
                              >
                                {option.name}: {option.value}
                                <span className="text-green-500">
                                  {optionPrice ? ` (+${optionPrice})` : ""}
                                </span>
                              </p>
                            );
                          })}
                      </div>
                    </div>
                    <div className="flex items-end">
                      <p className="text-sm font-medium">
                        {formatPrice(itemTotal)}
                      </p>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium">No items in order</p>
              </div>
            )}
          </div>
        </div>
        <Separator />
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Truck className="h-5 w-5 text-muted-foreground" />
            <div className="flex flex-col gap-2">
              <p className="text-sm font-medium">Shipping Address</p>
              <p
                className="text-sm text-muted-foreground"
                key={shippingAddress.id}
              >
                {shippingAddress.contact_name}
                <br />
                {shippingAddress.address}
                <br />
                {shippingAddress.city}, {shippingAddress.state}{" "}
                {shippingAddress.zip_code}
              </p>
            </div>
          </div>
        </div>
        <Separator />
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Package className="h-5 w-5 text-muted-foreground" />
            <div className="flex flex-col gap-2">
              <p className="text-sm font-medium">Delivery Method</p>
              <div className="text-sm text-muted-foreground">
                {order.delivery_method}
                {order.ship_collect && (
                  <>
                    <br />
                    Ship Collect UPS
                    {order.ups_account_number && (
                      <>
                        <br />
                        Account Number: {order.ups_account_number}
                      </>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
        <Separator />
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <FileText className="h-5 w-5 text-muted-foreground" />
            <div className="flex flex-col gap-2">
              <p className="text-sm font-medium">Tax Information</p>
              <div className="text-sm text-muted-foreground">
                {order.tax_exempt ? "Tax Exempt" : "Taxable"}
                {(order as any).tax_rate && (order as any).tax_rate > 0 && (
                  <>
                    <br />
                    Tax Rate: {(order as any).tax_rate.toFixed(3)}%
                    <br />
                    Tax Amount: {(order as any).tax_amount ? formatPrice((order as any).tax_amount) : "$0.00"}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
        <Separator />
        <div className="flex flex-col gap-4">
          {(order as any).tax_rate && (order as any).tax_rate > 0 && (
            <>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Subtotal</span>
                <span>{formatPrice((totalAmount || 0) - ((order as any).tax_amount || 0))}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">
                  Tax ({(order as any).tax_rate.toFixed(3)}%)
                  {order.tax_exempt && (
                    <span className="text-orange-600 ml-1">(Exempt)</span>
                  )}
                </span>
                <span className={order.tax_exempt ? "line-through text-muted-foreground" : ""}>
                  {order.tax_exempt ? "$0.00" : formatPrice((order as any).tax_amount || 0)}
                </span>
              </div>
            </>
          )}
          <div className="flex justify-between font-medium">
            <p className="text-sm">Total</p>
            <p className="text-sm font-medium">{formatPrice(totalAmount)}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function OrderDetailsSkeleton() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-base font-medium">
          <Skeleton className="h-6 w-24" />
        </CardTitle>
        <Skeleton className="h-6 w-24" />
      </CardHeader>
      <CardContent className="grid gap-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Skeleton className="h-12 w-12" />
            <div className="flex flex-col gap-2">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-6 w-24" />
            </div>
          </div>
          <Skeleton className="h-5 w-5" />
        </div>
        <Separator />
        <div className="flex flex-col gap-4">
          <Skeleton className="h-6 w-24" />
          <div className="grid gap-4">
            <div className="flex items-center justify-between">
              <div className="flex items-start gap-4">
                <Skeleton className="h-16 w-16" />
                <div className="flex flex-col gap-2">
                  <Skeleton className="h-6 w-96" />
                  <Skeleton className="h-6 w-96" />
                </div>
              </div>
              <Skeleton className="h-6 w-12" />
            </div>
          </div>
        </div>
        <Separator />
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Skeleton className="h-12 w-12" />
            <div className="flex flex-col gap-2">
              <Skeleton className="h-6 w-96" />
              <Skeleton className="h-24 w-24" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
