import emailTemplate from ".";
import { CalculatorData } from "@/pages/api/orders";

interface OrderUpdateProps {
  to: string;
  orderId: string;
  invoice: string;
  oldStatus: string;
  newStatus: string;
  statusDate: string;
  // Account information
  name?: string;
  firstName?: string;
  lastName?: string;
  companyName?: string;
  phoneNumber?: string;
  accountNumber?: string;
  maxtonAccountNum?: number;
  // Order payment details
  datePurchased?: string;
  paymentMethod?: string;
  poNumber?: string;
  deliveryMethod?: string;
  shipCollect?: boolean;
  upsAccountNumber?: string;
  // Order items
  items: {
    name: string;
    quantity: number;
    price?: number;
    item_price?: number;
    options?: { name: string; value: string; price?: number }[];
    calculator_data?: CalculatorData;
  }[];
  // Shipping info
  trackingNumber?: string;
  trackingUrl?: string;
  estimatedDeliveryDate?: string;
  additionalInfo?: string;
  // Shipping and contact details
  shippingAddress?: {
    contactName?: string;
    address?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    phone?: string;
    country?: string;
  };
  // Billing details
  billingAddress?: {
    contactName?: string;
    address?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    phone?: string;
    country?: string;
  };
}

// Helper function to format payment method string
function formatPaymentMethod(method?: string): string {
  if (!method) return "";
  return method
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

export default function createOrderUpdateTemplate({
  to,
  orderId,
  invoice,
  oldStatus,
  newStatus,
  statusDate,
  name,
  firstName,
  lastName,
  companyName,
  phoneNumber,
  accountNumber,
  maxtonAccountNum,
  datePurchased,
  paymentMethod,
  poNumber,
  deliveryMethod,
  shipCollect,
  upsAccountNumber,
  items,
  trackingNumber,
  trackingUrl,
  estimatedDeliveryDate,
  additionalInfo,
  shippingAddress,
  billingAddress,
}: OrderUpdateProps) {
  const APP_NAME = process.env.APP_NAME ?? "Maxton Manufacturing Company";
  const APP_EMAIL_FROM = process.env.MAIL_FROM ?? "<EMAIL>";
  const from = `${APP_NAME} <${APP_EMAIL_FROM}>`;
  const orderUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/store/orders/${orderId}`;

  // We're only handling completed orders now
  const statusTitle = "Your Order is Complete";
  const statusMessage =
    "Your order has been successfully completed. Thank you for your business!";

  // Format date if available
  const formattedDate = new Date(statusDate).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });

  // Format purchase date if available
  const formattedPurchaseDate = datePurchased
    ? new Date(datePurchased).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    : "";

  // Display name properly
  const displayName =
    firstName || lastName
      ? `${firstName || ""} ${lastName || ""}`.trim()
      : name || "";

  let subtotal = 0;
  let optionsTotal = 0;

  const itemsList = items
    .map((item) => {
      // Calculate option prices if they exist
      const itemPrice = item.item_price || item.price || 0;
      const itemSubtotal = itemPrice * item.quantity;
      let itemOptionsTotal = 0;

      // Build options HTML with detailed pricing
      let optionsHtml = "";
      if (item.options && item.options.length > 0) {
        optionsHtml += '<div style="margin-top: 6px; padding-left: 15px;">';
        optionsHtml +=
          '<div style="font-size: 12px; font-weight: bold; color: #555; margin-bottom: 4px;">Selected Options:</div>';

        item.options.forEach((opt) => {
          const optionPrice = opt.price || 0;
          const optionTotalPrice = optionPrice * item.quantity;
          itemOptionsTotal += optionTotalPrice;

          optionsHtml += `
          <div style="display: flex; justify-content: space-between; font-size: 12px; color: #666; margin-bottom: 2px;">
            <div>${opt.name}: ${opt.value}</div>
            ${
              optionPrice > 0
                ? `<div style="text-align: right; min-width: 90px;">
                <span style="color: #28a745;">+$${optionPrice.toFixed(
                  2
                )}</span> × ${
                  item.quantity
                } = <span style="color: #28a745;">$${optionTotalPrice.toFixed(
                  2
                )}</span>
              </div>`
                : ""
            }
          </div>`;
        });

        optionsHtml += "</div>";
      }

      // Update running totals
      subtotal += itemSubtotal;
      optionsTotal += itemOptionsTotal;
      const itemTotal = itemSubtotal + itemOptionsTotal;

      return `<tr>
      <td style="padding: 12px 8px; border-bottom: 1px solid #ddd;">
        <div style="font-weight: bold;">${item.name}</div>
        ${optionsHtml}
      </td>
      <td style="padding: 12px 8px; border-bottom: 1px solid #ddd; text-align: center; vertical-align: top;">${
        item.quantity
      }</td>
      <td style="padding: 12px 8px; border-bottom: 1px solid #ddd; text-align: right; vertical-align: top;">
        <div>$${itemPrice.toFixed(2)}</div>
        ${
          itemOptionsTotal > 0
            ? `<div style="margin-top: 4px; font-size: 13px;">
            <div style="color: #28a745;">Options: $${(
              itemOptionsTotal / item.quantity
            ).toFixed(2)}</div>
            <div style="font-weight: bold; margin-top: 4px; border-top: 1px solid #eee; padding-top: 4px;">
              Item Total: $${(itemTotal / item.quantity).toFixed(2)}
            </div>
          </div>`
            : `<div style="margin-top: 4px; font-size: 13px; color: #666;">
            Item Total: $${(itemTotal / item.quantity).toFixed(2)}
          </div>`
        }
      </td>
    </tr>`;
    })
    .join("");

  // Calculate totals
  const merchandiseTotal = subtotal + optionsTotal;
  const calculatorData = items?.[0]?.calculator_data;

  return emailTemplate({
    to,
    subject: `${statusTitle} - ${invoice}`,
    from: from,
    bcc: "<EMAIL>",
    body: `
      <h2 style="color: #28a745; background-color: #e6ffe6; padding: 10px; border-left: 4px solid #28a745;">${statusTitle} - ${invoice}</h2>
      <p>${statusMessage}</p>
      
      <div style="margin: 20px 0;">
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
          <tr>
            <th style="padding: 12px; text-align: left; background-color: #f0f0f0; border: 1px solid #ddd; font-size: 18px; font-weight: 600;" colspan="2">Order Details</th>
          </tr>
          <tr>
            <td style="width: 50%; padding: 15px; vertical-align: top; border: 1px solid #ddd;">
              <ul style="list-style-type: none; padding-left: 0; margin: 0;">
                <li style="margin-bottom: 4px;"><strong>Order ID:</strong> ${orderId.slice(
                  0,
                  6
                )}</li>
                <li style="margin-bottom: 4px;"><strong>Completion Date:</strong> ${formattedDate}</li>
                ${
                  formattedPurchaseDate
                    ? `<li style="margin-bottom: 4px;"><strong>Date Purchased:</strong> ${formattedPurchaseDate}</li>`
                    : ""
                }
                ${
                  paymentMethod
                    ? `<li style="margin-bottom: 4px;"><strong>Payment Method:</strong> ${formatPaymentMethod(
                        paymentMethod
                      )}</li>`
                    : ""
                }
                ${
                  poNumber
                    ? `<li style="margin-bottom: 4px;"><strong>Purchase Order Number:</strong> ${poNumber}</li>`
                    : ""
                }
                ${
                  deliveryMethod
                    ? `<li style="margin-bottom: 4px;"><strong>Shipping Method:</strong> ${deliveryMethod}</li>`
                    : ""
                }
                ${
                  // Check if shipping address is for a special country
                  (() => {
                    const isSpecialCountry =
                      shippingAddress?.country === "Mexico" ||
                      shippingAddress?.country === "Puerto Rico" ||
                      shippingAddress?.state === "Puerto Rico" ||
                      (shippingAddress?.state &&
                        shippingAddress.state.includes("Mexico"));

                    // Only show Ship Collect UPS info if not a special country
                    return !isSpecialCountry && shipCollect !== undefined
                      ? `<li style="margin-bottom: 4px;">
                          <strong>Ship Collect UPS:</strong> ${
                            shipCollect ? "Yes" : "No"
                          }
                          ${
                            shipCollect && upsAccountNumber
                              ? `<br><span style="margin-left: 20px;"><strong>UPS Account Number:</strong> ${upsAccountNumber}</span>`
                              : ""
                          }
                        </li>`
                      : "";
                  })()
                }
              </ul>
            </td>
            <td style="width: 50%; padding: 15px; vertical-align: top; border: 1px solid #ddd;">
              <ul style="list-style-type: none; padding-left: 0; margin: 0;">
                <li style="margin-bottom: 4px;"><strong>Account ID:</strong> ${
                  maxtonAccountNum !== undefined ? maxtonAccountNum : "N/A"
                }</li>
                ${
                  displayName
                    ? `<li style="margin-bottom: 4px;"><strong>Name:</strong> ${displayName}</li>`
                    : ""
                }
                <li style="margin-bottom: 4px;"><strong>Email:</strong> ${to}</li>
                ${
                  companyName
                    ? `<li style="margin-bottom: 4px;"><strong>Company Name:</strong> ${companyName}</li>`
                    : ""
                }
                ${
                  phoneNumber
                    ? `<li style="margin-bottom: 4px;"><strong>Phone Number:</strong> ${phoneNumber}</li>`
                    : ""
                }
                ${
                  trackingNumber
                    ? `<li style="margin-bottom: 4px;"><strong>Tracking Number:</strong> ${trackingNumber}</li>`
                    : ""
                }
                ${
                  trackingUrl
                    ? `<li style="margin-bottom: 4px;"><strong>Tracking Link:</strong> <a href="${trackingUrl}" style="color: #28a745; text-decoration: none;">Track Your Package</a></li>`
                    : ""
                }
                ${
                  estimatedDeliveryDate
                    ? `<li style="margin-bottom: 4px;"><strong>Estimated Delivery:</strong> ${estimatedDeliveryDate}</li>`
                    : ""
                }
                ${
                  additionalInfo
                    ? `<li style="margin-bottom: 4px;"><strong>Additional Information:</strong> ${additionalInfo}</li>`
                    : ""
                }
              </ul>
            </td>
          </tr>
        </table>
      </div>
      
      <div style="margin: 20px 0;">
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
          <tr>
            <th style="width: 50%; padding: 10px; text-align: left; background-color: #f0f0f0; border: 1px solid #ddd;">Billing Address</th>
            <th style="width: 50%; padding: 10px; text-align: left; background-color: #f0f0f0; border: 1px solid #ddd;">Shipping Address</th>
          </tr>
          <tr>
            <td style="padding: 15px; vertical-align: top; border: 1px solid #ddd;">
              ${
                billingAddress
                  ? `
                ${billingAddress.contactName || ""}<br>
                ${billingAddress.address || ""}<br>
                ${billingAddress.city || ""}, ${billingAddress.state || ""} ${
                  billingAddress.zipCode || ""
                }
                ${billingAddress.country ? `<br>${billingAddress.country}` : ""}
                ${
                  billingAddress.phone
                    ? `<br>Phone: ${billingAddress.phone}`
                    : ""
                }
              `
                  : "Contact your account manager for billing details"
              }
            </td>
            <td style="padding: 15px; vertical-align: top; border: 1px solid #ddd;">
              ${
                shippingAddress
                  ? `
                Company: ${companyName || "N/A"}<br>
                ${shippingAddress.contactName || ""}<br>
                ${shippingAddress.address || ""}<br>
                ${shippingAddress.city || ""}, ${shippingAddress.state || ""} ${
                  shippingAddress.zipCode || ""
                }
                ${
                  shippingAddress.country
                    ? `<br>${shippingAddress.country}`
                    : ""
                }
                ${
                  shippingAddress.phone
                    ? `<br>Phone: ${shippingAddress.phone}`
                    : ""
                }
              `
                  : "No shipping address provided"
              }
            </td>
          </tr>
        </table>
      </div>

      <p><strong>Order Items:</strong></p>
      <table style="width: 100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th style="padding: 12px 8px; text-align: left; border-bottom: 2px solid #ddd; background-color: #f8f9fa;">Product</th>
            <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #ddd; background-color: #f8f9fa;">Quantity</th>
            <th style="padding: 12px 8px; text-align: right; border-bottom: 2px solid #ddd; background-color: #f8f9fa;">Price</th>
          </tr>
        </thead>
        <tbody>
          ${itemsList}
        </tbody>
        <tfoot>
          <tr>
            <td colspan="2" style="padding: 12px 8px; text-align: right; font-weight: bold; font-size: 16px; border-top: 2px solid #ddd;">Grand Total:</td>
            <td style="padding: 12px 8px; text-align: right; font-weight: bold; font-size: 16px; border-top: 2px solid #ddd;">$${merchandiseTotal.toFixed(
              2
            )}</td>
          </tr>
        </tfoot>
      </table>

      ${
        calculatorData && Object.keys(calculatorData).length > 0
          ? `
        <h3 style="margin-top: 10px;">Job Specifications</h3>
        <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
          <thead>
            <tr>
              <th colspan="2" style="padding: 8px; background-color: #f1f1f1; text-align: left;">Provided Data</th>
            </tr>
          </thead>
          <tbody>
            ${Object.entries(calculatorData.providedData || {})
              .map(
                ([key, value]) => `
                  <tr>
                    <td style="padding: 6px 8px; border: 1px solid #ddd;">${key}</td>
                    <td style="padding: 6px 8px; border: 1px solid #ddd;">${value}</td>
                  </tr>
                `
              )
              .join("")}
          </tbody>
        </table>
        
        <table style="width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 14px;">
          <thead>
            <tr>
              <th colspan="2" style="padding: 8px; background-color: #f1f1f1; text-align: left;">Results</th>
            </tr>
          </thead>
          <tbody>
            ${Object.entries(calculatorData.results || {})
              .map(
                ([key, value]) => `
                  <tr>
                    <td style="padding: 6px 8px; border: 1px solid #ddd;">${key}</td>
                    <td style="padding: 6px 8px; border: 1px solid #ddd;">${value}</td>
                  </tr>
                `
              )
              .join("")}
          </tbody>
        </table>
      `
          : ""
      }

      <p style="margin-top: 25px;">You can view your order details anytime by visiting your <a href="${orderUrl}" style="color: #28a745; text-decoration: none;">order details page</a>.</p>

      <p>Thank you for choosing Maxton Manufacturing Company. We appreciate your business!</p>
    `,
  });
}
