import { checkAdmin, checkPermission } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient, supabaseClient } from "@/supabase";
import { CustomerGroup } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default checkAdmin(
  matchRoute({
    GET: getCustomerGroupsHandler,
    POST: checkPermission("create:customer_groups", createCustomerGroupHandler),
    DELETE: checkPermission(
      "delete:customer_groups",
      deleteCustomerGroupHandler,
    ),
  }),
);

export interface GetCustomerGroupsResponse {
  error?: string;
  customerGroups?: CustomerGroup[];
  total?: number;
  totalPages?: number;
}

async function getCustomerGroupsHandler(
  req: NextApiRequest,
  res: NextApiResponse<GetCustomerGroupsResponse>,
) {
  const query = req.query;
  const page = query.page ? parseInt(query.page as string) : 1;
  const limit = query.limit ? parseInt(query.limit as string) : 10;

  const { data, error } = await supabaseClient
    .schema("public")
    .from("customer_groups")
    .select("*", { count: "exact" })
    .range((page - 1) * limit, page * limit - 1);

  if (error) {
    return res.status(400).json({ error: error.message });
  }

  const total = data?.length;
  const totalPages = Math.ceil(total / limit);

  return res.status(200).json({ customerGroups: data, total, totalPages });
}

export interface CreateCustomerGroupResponse {
  error?: string;
  customerGroup?: CustomerGroup[];
}

export const assignGroupToCustomerSchema = z.object({
  customer_ids: z.array(z.string().uuid("Customer ID is required")),
  group_id: z.string().uuid("Group ID is required"),
});

export type AssignGroupToCustomerRequest = z.infer<
  typeof assignGroupToCustomerSchema
>;

async function createCustomerGroupHandler(
  req: NextApiRequest,
  res: NextApiResponse<CreateCustomerGroupResponse>,
) {
  const { customer_ids, group_id } = assignGroupToCustomerSchema.parse(
    req.body,
  );

  const supabaseAdminClient = createSupabaseAdminClient();

  const { data, error } = await supabaseAdminClient
    .schema("public")
    .from("customer_groups")
    .update({ group_id })
    .in("customer_id", customer_ids)
    .select("*");

  if (data?.length === 0) {
    const bulkInsertData = customer_ids.map((customer_id) => ({
      customer_id,
      group_id,
    }));

    const { data: _, error } = await supabaseAdminClient
      .schema("public")
      .from("customer_groups")
      .insert(bulkInsertData)
      .select("*");

    if (error) {
      return res.status(400).json({ error: "Failed to add user to group." });
    }
  }

  const now = new Date();
  const updatedAt = now.toISOString();

  const updateCustomers = await supabaseAdminClient
    .from("customers")
    .update({ updated_at: updatedAt })
    .in("id", customer_ids)
    .select("user_id");

  const userIds =
    updateCustomers?.data?.map((user) => user.user_id ?? "") ?? [];

  await supabaseAdminClient
    .from("users")
    .update({ updated_at: updatedAt })
    .in("id", userIds);

  if (error) {
    return res.status(400).json({ error: "Failed to add user to group." });
  }

  return res.status(200).json({ customerGroup: data });
}

export const deleteCustomerGroupSchema = z.object({
  id: z.string().uuid("CustomerGroup ID is required"),
});

export type DeleteCustomerGroupRequest = z.infer<
  typeof deleteCustomerGroupSchema
>;

export interface DeleteCustomerGroupResponse {
  error?: string;
  message?: string;
}

async function deleteCustomerGroupHandler(
  req: NextApiRequest,
  res: NextApiResponse<DeleteCustomerGroupResponse>,
) {
  const parsedData = deleteCustomerGroupSchema.safeParse(req.body);

  if (parsedData.error) {
    return res.status(400).json({ error: parsedData.error.issues[0].message });
  }

  const { id } = parsedData.data;

  const supabaseAdminClient = createSupabaseAdminClient();

  const { data, error } = await supabaseAdminClient
    .schema("public")
    .from("customer_groups")
    .delete()
    .eq("id", id);

  if (error) {
    return res.status(400).json({ error: error.message });
  }

  return res
    .status(200)
    .json({ message: "Customer group deleted successfully." });
}
