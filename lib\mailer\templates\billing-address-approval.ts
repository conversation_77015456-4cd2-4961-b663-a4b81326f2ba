import emailTemplate from ".";

interface BillingAddressApprovalProps {
  to: string;
  name: string;
  addressId: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  approvalDate: string;
  accountUrl: string;
  additionalNotes?: string;
}

export default function createBillingAddressApprovalTemplate({
  to,
  name,
  addressId,
  addressLine1,
  addressLine2,
  city,
  state,
  postalCode,
  country,
  approvalDate,
  accountUrl,
  additionalNotes,
}: BillingAddressApprovalProps) {
  const APP_NAME = process.env.APP_NAME ?? "Maxton Valve";
  const APP_EMAIL_FROM = process.env.MAIL_FROM ?? "<EMAIL>";
  const from = `${APP_NAME} <${APP_EMAIL_FROM}>`;

  // Format the address for display
  const formattedAddress = `
    ${addressLine1}<br/>
    ${addressLine2 ? `${addressLine2}<br/>` : ""}
    ${city}, ${state} ${postalCode}<br/>
    ${country}
  `;

  return emailTemplate({
    to,
    subject: `Billing Address Approved - ${APP_NAME}`,
    from: from,
    bcc: "<EMAIL>",
    body: `
      <h2>Billing Address Approval Confirmation</h2>
      <p>Dear ${name},</p>
      
      <p>We're pleased to inform you that your billing address has been approved and is now active on your account.</p>
      
      <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9;">
        <h3 style="margin-top: 0;">Approved Billing Address</h3>
        <p style="margin-bottom: 0;">
          ${formattedAddress}
        </p>
      </div>
      
      ${
        additionalNotes
          ? `
      <div style="margin: 20px 0;">
        <h3>Additional Information</h3>
        <p>${additionalNotes}</p>
      </div>
      `
          : ""
      }
      
      <div style="margin: 25px 0; text-align: center;">
        <a href="${accountUrl}" 
           style="display: inline-block; padding: 12px 24px; background-color: #0045d8; color: white; text-decoration: none; border-radius: 4px; font-weight: bold;">
          View Your Account
        </a>
      </div>
      
      <p>With this approved billing address, you can now:</p>
      <ul>
        <li>Place orders using this billing address</li>
        <li>Set up payment methods associated with this address</li>
        <li>Receive invoices at this location</li>
      </ul>
      
      <p>If you have any questions or need to make changes to this address, please visit your account settings or contact our customer service team.</p>
      
      <p>Thank you for choosing ${APP_NAME}. We look forward to serving you!</p>
    `,
  });
}
