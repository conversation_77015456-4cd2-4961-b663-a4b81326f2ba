import AdminLayout from "@/components/features/admin/layout";
import { SettingsLayout } from "@/components/features/admin/settings/layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/shadcn-button";
import { Users2, <PERSON>culator } from "lucide-react";
import Head from "next/head";
import Link from "next/link";

export default function AdminSettingsPage() {
  return (
    <AdminLayout>
      <Head>
        <title>Settings - Admin</title>
        <meta name="description" content="Manage application settings" />
      </Head>

      <SettingsLayout title="Settings" description="Manage your application settings and configurations">
        <div className="space-y-6">
          {/* Settings Overview */}
          <div>
            <h2 className="text-xl font-semibold mb-4">Settings Overview</h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">

              {/* Users Management Card */}
              <Card className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex items-center gap-2">
                    <Users2 className="h-5 w-5 text-blue-600" />
                    <CardTitle className="text-lg">Users</CardTitle>
                  </div>
                  <CardDescription>
                    Manage user roles and permissions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Promote or demote users between Customer/Manager, Staff, and Admin roles.
                  </p>
                  <Button asChild className="w-full">
                    <Link href="/admin/settings/users">
                      Manage Users
                    </Link>
                  </Button>
                </CardContent>
              </Card>

              {/* Tax Rates Management Card */}
              <Card className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex items-center gap-2">
                    <Calculator className="h-5 w-5 text-green-600" />
                    <CardTitle className="text-lg">Tax Rates</CardTitle>
                  </div>
                  <CardDescription>
                    Manage Nevada tax rates by city
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Update tax rates for Nevada cities that are applied during checkout.
                  </p>
                  <Button asChild className="w-full">
                    <Link href="/admin/settings/tax-rates">
                      Manage Tax Rates
                    </Link>
                  </Button>
                </CardContent>
              </Card>

              {/* Placeholder for future settings */}
              <Card className="opacity-50">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Store Settings</CardTitle>
                  <CardDescription>
                    Configure store preferences
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Manage store information, payment methods, and other preferences.
                  </p>
                  <Button variant="outline" disabled className="w-full">
                    Coming Soon
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Quick Actions */}
          <div>
            <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
            <div className="flex gap-4 flex-wrap">
              <Button variant="outline" asChild>
                <Link href="/admin/settings/users">
                  <Users2 className="mr-2 h-4 w-4" />
                  View All Users
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="/admin/settings/tax-rates">
                  <Calculator className="mr-2 h-4 w-4" />
                  Manage Tax Rates
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </SettingsLayout>
    </AdminLayout>
  );
}