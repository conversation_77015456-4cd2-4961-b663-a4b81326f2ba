import { Button } from "@/components/ui/shadcn-button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { useDismissNotificationMutation } from "@/queries/notification-queries";
import useAuthStore from "@/stores/auth-store";
import { useNotificationsStore } from "@/stores/notifications-store";
import { Notification, NotificationType } from "@/supabase/types";
import { BellIcon, X, Info, AlertTriangle, AlertCircle } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { cn } from "@/lib/utils";

interface NotificationsDropdownProps {
  notifications: Notification[];
}

// Get icon based on notification type
function getNotificationIcon(type: NotificationType) {
  switch (type) {
    case "info":
      return <Info className="h-5 w-5 text-blue-500" />;
    case "warning":
      return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    case "error":
      return <AlertCircle className="h-5 w-5 text-red-500" />;
    default:
      return <Info className="h-5 w-5 text-blue-500" />;
  }
}

// Get badge variant based on notification type
function getBadgeVariant(type: NotificationType): "default" | "secondary" | "destructive" | "outline" {
  switch (type) {
    case "info":
      return "default";
    case "warning":
      return "secondary";
    case "error":
      return "destructive";
    default:
      return "outline";
  }
}

// Individual notification item component
function NotificationItem({ notification }: { notification: Notification }) {
  const userData = useAuthStore((state) => state.data);
  const removeNotification = useNotificationsStore((state) => state.removeNotification);
  const dismissMutation = useDismissNotificationMutation(userData?.id ?? "");

  const handleDismiss = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      await dismissMutation.mutateAsync(notification.id);
      removeNotification(notification.id);
    } catch (error) {
      console.error("Failed to dismiss notification:", error);
    }
  };

  const handleClick = () => {
    if (notification.link) {
      window.open(notification.link, "_blank");
    }
  };

  return (
    <DropdownMenuItem
      className={cn(
        "flex items-start gap-3 p-4 py-4 cursor-pointer hover:bg-muted/30 transition-colors min-h-[80px]",
        notification.link && "cursor-pointer"
      )}
      onClick={handleClick}
    >
      {/* Icon container - inspired by the reference image */}
      <div className="flex-shrink-0 mt-1">
        <div className="w-8 h-8 rounded-full bg-muted/50 flex items-center justify-center">
          {getNotificationIcon(notification.type)}
        </div>
      </div>

      {/* Content area */}
      <div className="flex-1 min-w-0 space-y-2">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <Badge variant={getBadgeVariant(notification.type)} className="text-xs px-2 py-0.5">
              {notification.type.toUpperCase()}
            </Badge>
            {/* Show NEW badge for notifications less than 24 hours old */}
            {new Date().getTime() - new Date(notification.created_at).getTime() < 24 * 60 * 60 * 1000 && (
              <Badge variant="default" className="text-xs px-2 py-0.5 bg-green-500 hover:bg-green-600">
                NEW
              </Badge>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 hover:bg-destructive hover:text-destructive-foreground opacity-60 hover:opacity-100"
            onClick={handleDismiss}
            disabled={dismissMutation.isPending}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>

        {notification.body && (
          <p className="text-sm text-foreground line-clamp-2 leading-relaxed">
            {notification.body}
          </p>
        )}

        <p className="text-xs text-muted-foreground mt-2">
          {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
        </p>
      </div>
    </DropdownMenuItem>
  );
}

export function NotificationsDropdown({ notifications }: NotificationsDropdownProps) {
  const hasNotifications = notifications.length > 0;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative text-muted-foreground">
          {hasNotifications && (
            <span className="absolute -top-2 -left-2 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-[10px] font-bold text-white">
              {notifications.length}
            </span>
          )}
          <BellIcon className="h-5 w-5" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Notifications</span>
          {hasNotifications && (
            <Badge variant="secondary" className="text-xs">
              {notifications.length} new
            </Badge>
          )}
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        {!hasNotifications ? (
          <div className="p-4 text-center text-muted-foreground">
            <BellIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No new notifications</p>
          </div>
        ) : (
          <div className="max-h-96 overflow-hidden">
            <ScrollArea className="h-full pr-3">
              <div className="space-y-2 pr-1">
                {notifications.map((notification) => (
                  <NotificationItem key={notification.id} notification={notification} />
                ))}
              </div>
            </ScrollArea>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
