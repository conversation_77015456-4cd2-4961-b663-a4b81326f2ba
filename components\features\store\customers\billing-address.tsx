import { SvgSpinners90Ring } from "@/components/common/icons";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormControl, FormField, FormItem } from "@/components/ui/form";
import { Button } from "@/components/ui/shadcn-button";
import { Skeleton } from "@/components/ui/skeleton";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { toast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { CheckoutFormValues } from "@/pages/store/checkout";
import { useDeleteBillingAddressMutation, useGetBillingAddressesQuery, useUpdateBillingAddressMutation } from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import { BillingAddress } from "@/supabase/types";
import { CheckCircle, Star, Trash2, XCircle } from "lucide-react";
import { useMemo, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { AddBillingAddressDialog } from "./add-billing-address";
import { Badge } from "@/components/ui/badge";
import { approveBillingAddressSchema } from "@/pages/api/billing-addresses";

interface BillingAddressCardProps {
    address: BillingAddress;
    onClick: () => void;
    isSelected: boolean;
}

export function BillingAddressesSkeleton() {
    return <Card className="w-[240px] h-[240px]">
        <CardHeader>
            <Skeleton className="w-full h-full object-cover rounded-t-lg" />
        </CardHeader>
        <CardContent>
            <div>
                <div className="space-y-1">
                    <Skeleton className="w-2/3 h-4" />
                    <Skeleton className="w-1/3 h-4" />
                </div>
                <div className="space-y-1">
                    <Skeleton className="w-2/3 h-4" />
                    <Skeleton className="w-1/3 h-4" />
                    <Skeleton className="w-1/3 h-4" />
                </div>
            </div>
        </CardContent>
    </Card>
}

export function BillingAddresses({
    checkoutForm
}: {
    checkoutForm: UseFormReturn<CheckoutFormValues>
}) {
    const userData = useAuthStore((state) => state.data);
    const userId = userData.id;
    const [selectedAddressId, setSelectedAddressId] = useState<string | undefined>(undefined);
    const billingAddresses = useGetBillingAddressesQuery(userId);

    const sortByDefaultAddress = useMemo(() => billingAddresses?.data?.sort((a, b) => {
        if (a.default) return -1;
        if (b.default) return 1;
        return 0;
    }), [billingAddresses.data]);
    const pendingBillingAddress = billingAddresses.data?.find((address) => !address.approved);
    const sortedBillingAddresses = sortByDefaultAddress?.filter((address) => address.id !== pendingBillingAddress?.id) ?? [];

    return (
        <section>
            {billingAddresses.isLoading ? (
                <div>
                    <BillingAddressesSkeleton />
                </div>
            ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {
                        billingAddresses.data
                        && billingAddresses.data?.length === 0
                        && <div>No Billing Addresses.</div>
                    }
                    {billingAddresses.data
                        && billingAddresses.data?.length > 0
                        && sortedBillingAddresses?.map((address, index) => {
                            return <FormField
                                key={`billing-address-${address.id}-${index}`}
                                control={checkoutForm.control}
                                name="billingAddressId"
                                render={({ field }) => {
                                    const isSelected = address.id === selectedAddressId || (selectedAddressId === undefined && address.default);

                                    return <FormItem>
                                        <FormControl>
                                            <BillingAddressCard
                                                address={address}
                                                isSelected={isSelected}
                                                onClick={() => {
                                                    if (!address.approved) return;
                                                    if (address.id) {
                                                        setSelectedAddressId(address.id);
                                                        field.onChange(address.id);
                                                    }
                                                }}
                                            />
                                        </FormControl>
                                    </FormItem>
                                }}
                            />
                        })
                    }

                    {/* Removed the ability to add new billing address at checkout since it's now required at registration */}
                    {/* <AddBillingAddressDialog /> */}
                </div>
            )}
        </section>
    );
}

export function BillingAddressCard({ address, onClick, isSelected }: BillingAddressCardProps) {
    const userData = useAuthStore((state) => state.data);
    const userId = userData.id;

    const updateBillingAddressMutation = useUpdateBillingAddressMutation(userId, address?.id?.toString() ?? "");
    const deleteBillingAddressMutation = useDeleteBillingAddressMutation(userId, address?.id?.toString() ?? "");

    const setBillingAddressAsDefault = async () => {
        if (!address.approved) return;

        updateBillingAddressMutation.mutateAsync({
            default: true,
        })
            .then(() => {
                toast({
                    title: "Success",
                    description: "Billing address set as default",
                    duration: 3000,
                    variant: "success",
                });
            })
            .catch(e => {
                console.log(e);
            });
    }

    const deleteBillingAddress = async () => {
        deleteBillingAddressMutation.mutateAsync()
            .then(() => {
                toast({
                    title: "Success",
                    description: "Billing address deleted",
                    duration: 3000,
                    variant: "success",
                });
            })
            .catch(e => {
                console.log(e);
            });
    }

    return <Card
        key={address.id}
        className={cn(
            "relative w-[240px] h-[240px] flex flex-col cursor-pointer",
            isSelected ? "border-primary border-2 shadow-lg" : "border-gray-300",
            !address.approved ? "disabled cursor-not-allowed" : ""
        )}
        aria-disabled={!address.approved}
        onClick={onClick}
    >
        <TooltipProvider>
            {address.approved ? (
                <Tooltip>
                    <TooltipTrigger asChild>
                        <div className="absolute top-2 right-2">
                            <Button
                                variant="ghost"
                                size='icon'
                                disabled={!address.approved || isSelected || updateBillingAddressMutation.isPending}
                                className="hover:bg-transparent"
                                onClick={address.approved ? setBillingAddressAsDefault : () => { }}
                            >
                                {
                                    updateBillingAddressMutation.isPending ? (
                                        <SvgSpinners90Ring />
                                    ) : (
                                        <Star
                                            size={16}
                                            className={cn(
                                                isSelected
                                                    ? "text-yellow-500 fill-yellow-500" : "text-gray-500"
                                            )}
                                        />
                                    )
                                }
                            </Button>
                        </div>
                    </TooltipTrigger>
                    <TooltipContent>
                        {
                            address.default ? (
                                <p>Default</p>
                            ) : (
                                <p>Set as Default</p>
                            )
                        }
                    </TooltipContent>
                </Tooltip>
            ) : null}
            {/* <Tooltip>
                <TooltipTrigger asChild>
                    <div className="absolute bottom-2 right-2">
                        <Button
                            variant="ghost"
                            size='icon'
                            disabled={deleteBillingAddressMutation.isPending}
                            className="hover:bg-transparent"
                            onClick={deleteBillingAddress}
                        >
                            <Trash2
                                size={16}
                                className={cn(
                                    isSelected
                                        ? "text-red-500" : "text-gray-500"
                                )}
                            />
                        </Button>
                    </div>
                </TooltipTrigger>
                <TooltipContent>
                    <p>Delete Billing Address</p>
                </TooltipContent>
            </Tooltip> */}
        </TooltipProvider>
        <CardHeader className="flex-none p-4 pt-10 uppercase">
            <div className="absolute top-2 left-2">
                {!address.approved ? (
                    <div className="flex items-center gap-1">
                        <Badge variant="pending">
                            Pending
                        </Badge>
                    </div>
                ) : null}
            </div>
            <CardTitle className="text-base">
                {/* Assuming no 'name' field, using 'address' as a fallback */}
                {address.address?.split(",")[0] ?? "Billing Address"}
            </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center px-4 uppercase">
            <div className="text-sm font-medium flex flex-col items-start justify-center gap-2">
                <p>
                    {address.city?.substring(0, 12)}, {address.state?.substring(0, 12)}...
                </p>
                <p>{address.country}</p>
                <p>{address.zip_code}</p>
            </div>
        </CardContent>
    </Card>
}