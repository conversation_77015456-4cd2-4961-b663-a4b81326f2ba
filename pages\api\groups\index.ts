import { checkAdmin, checkPermission } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { Group } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default checkAdmin(
  matchRoute({
    GET: getGroupsHandler,
    POST: checkPermission("create:groups", createGroupHandler),
  }),
);

export interface GroupWithMembers extends Group {
  members: {
    id: string;
    customer_data: {
      id: string;
      created_at: string;
      user_data: {
        id: string;
        email: string;
        first_name: string;
        last_name: string;
      };
    };
  }[];
  discount_percentage?: number;
}

export interface GetGroupsResponse {
  error?: string;
  groups?: GroupWithMembers[];
  total?: number;
  totalPages?: number;
}

async function getGroupsHandler(
  req: NextApiRequest,
  res: NextApiResponse<GetGroupsResponse>,
) {
  const query = req.query;
  const page = query.page ? parseInt(query.page as string) : 1;
  const limit = query.limit ? parseInt(query.limit as string) : 10;

  const supabaseAdminClient = createSupabaseAdminClient();

  const { data, count, error } = await supabaseAdminClient
    .schema("public")
    .from("groups")
    .select(
      "*, members:customer_groups(id, customer_data:customers(id, created_at, user_data:users(id, email, first_name, last_name)))",
      { count: "exact" },
    )
    .neq("name", "AMI") // disable for now
    .neq("name", "Sembit") // disable for now
    .range((page - 1) * limit, page * limit - 1);

  if (error) {
    console.error(error);
    return res.status(400).json({ error: error.message });
  }

  const total = count ?? 0;
  const totalPages = Math.ceil(total / limit);

  const groups = data as unknown as GroupWithMembers[];

  return res.status(200).json({ groups, total, totalPages });
}

export interface CreateGroupResponse {
  error?: string;
  group?: Group;
}

export const createGroupSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  discount_id: z.string().optional(),
});

export type CreateGroupRequest = z.infer<typeof createGroupSchema>;

async function createGroupHandler(
  req: NextApiRequest,
  res: NextApiResponse<CreateGroupResponse>,
) {
  const parsedData = createGroupSchema.safeParse(req.body);

  if (parsedData.error) {
    return res.status(400).json({ error: parsedData.error.message });
  }

  const { name, description, discount_id } = parsedData.data;

  const supabaseAdminClient = createSupabaseAdminClient();
  const { data, error } = await supabaseAdminClient
    .schema("public")
    .from("groups")
    .insert({ name, description, discount_id })
    .select("*")
    .single();

  if (error) {
    return res.status(400).json({ error: error.message });
  }

  return res.status(200).json({ group: data });
}
