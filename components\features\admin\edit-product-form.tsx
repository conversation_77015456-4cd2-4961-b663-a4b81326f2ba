import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useForm, UseFormReturn } from "react-hook-form";
import { z } from "zod";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/shadcn-button";
import { useToast } from "@/hooks/use-toast";
import { CategoryWithParent } from "@/pages/api/categories";
import {
  useGetAllCategoriesQuery,
  useUpdateProductMutation,
} from "@/queries/admin-queries";
import {
  useGetAllImages,
  useGetImage,
  useGetImages,
  useUploadImage,
} from "@/queries/customer-queries";
import { useGetProductByIdQuery } from "@/queries/product-queries";
import useAuthStore from "@/stores/auth-store";
import { useRouter } from "next/router";
import AdditionalInformation from "./product/additional-information";
import BasicInformation from "./product/basic-information";
import { EditProductFormSkeleton } from "./product/edit-product-form-skeleton";
import GroupPricing from "./product/group-pricing";
import { ImageSelector, SelectedImagesPreview } from "./product/image-selector";
import ProductOptions from "./product/product-options";

export const ACCEPTED_IMAGE_TYPES = [
  "image/png",
  "image/jpeg",
  "image/jpg",
  "image/gif",
  "image/webp",
];
type OptionType = "select" | "text" | "number";

interface OptionValue {
  name: string;
  value: string;
  price?: number;
}

interface ProductOption {
  name: string;
  type: OptionType;
  options?: OptionValue[];
}

interface Specification {
  key: string;
  value: string;
}

interface UrlLabelItem {
  url: string;
  label: string;
}

// Form schema - used for form validation
const formSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  brand: z.string().optional(),
  description: z.string().optional(),
  price: z.coerce.number().min(0, "Price must be a positive number"),
  sku: z.string().optional(),
  available: z.boolean().default(true),
  variant: z.string().optional(),
  options: z.array(z.record(z.any())).optional(),
  features: z.array(z.string()).optional(),
  finish_and_material: z.string().optional(),
  category_id: z.string().optional(),
  draft: z.boolean().default(false),
  group_prices: z
    .array(
      z.object({
        group_id: z.string(),
        custom_price: z.number(),
      })
    )
    .nullable()
    .optional(),
  tags: z.array(z.string()).optional(),
  is_quote: z.boolean().default(false),
});

type ProductFormValues = z.infer<typeof formSchema>;

type ProductApiData = ProductFormValues & {
  image?: string | null;
  helpful_hints?: UrlLabelItem[] | null;
  installation_instructions?: string | null;
  delivery_and_shipping?: { shipping: UrlLabelItem } | null;
  warranty?: UrlLabelItem | null;
  user_id: string;
};

interface EditProductFormProps {
  productId: string;
}

interface EditProductFormProps {
  productId: string;
}

export default function EditProductForm({ productId }: EditProductFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const token = useAuthStore((state) => state.token);
  const userId = useAuthStore((state) => state.data?.id || "");
  const [file, setFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<string | null>(null);
  const [additionalFiles, setAdditionalFiles] = useState<File[]>([]);
  const [installationFile, setInstallationFile] = useState<File | null>(null);
  const [isDiscardDialogOpen, setIsDiscardDialogOpen] = useState(false);
  const allImagesQuery = useGetAllImages("product-images");
  const allImages = useMemo(
    () => allImagesQuery.data?.map((image) => `product-images/${image}`) ?? [],
    [allImagesQuery.data]
  );

  // For stored images from existing library
  const [selectedMainImage, setSelectedMainImage] = useState<string | null>(
    null
  );
  const [selectedAdditionalImages, setSelectedAdditionalImages] = useState<
    string[]
  >([]);
  const [mainImageUrl, setMainImageUrl] = useState<string | null>(null);
  const [additionalImageUrls, setAdditionalImageUrls] = useState<string[]>([]);

  // Product data query
  const {
    data,
    isLoading: isProductLoading,
    isError: isProductError,
  } = useGetProductByIdQuery(productId);
  const { product } = data ?? {};
  const productUuid = product?.id ?? "";

  // For specification table rows
  const [specs, setSpecs] = useState<Specification[]>([{ key: "", value: "" }]);

  // For features list
  const [features, setFeatures] = useState<string[]>([""]);

  // For helpful hints
  const [helpfulHints, setHelpfulHints] = useState<UrlLabelItem[]>([
    { url: "", label: "" },
  ]);

  // For shipping
  const [shipping, setShipping] = useState<UrlLabelItem>({
    url: "",
    label: "Shipping calculated at checkout.",
  });

  // For warranty
  const [warranty, setWarranty] = useState<UrlLabelItem>({
    url: "",
    label: "Subject to the terms and conditions of this Warranty.",
  });

  // For group pricing
  const [groupPrices, setGroupPrices] = useState<Record<string, number>>({});

  // For option editing UI state (not part of form data directly)
  const [currentOption, setCurrentOption] = useState<ProductOption>({
    name: "",
    type: "select",
    options: [{ name: "", value: "" }],
  });

  // Use useUploadImage with the correct product-images path
  const uploadImageMutation = useUploadImage("product-images");
  const updateProductMutation = useUpdateProductMutation(productUuid, token);
  const categoriesQuery = useGetAllCategoriesQuery(1, 100, token);
  const isLoading =
    uploadImageMutation.isPending || updateProductMutation.isPending;

  const form = useForm<ProductFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      brand: "",
      sku: "",
      price: 0,
      description: "",
      available: true,
      variant: "",
      options: [],
      features: [],
      finish_and_material: "",
      category_id: "",
      draft: false,
      group_prices: [],
      tags: [],
      is_quote: false,
    },
  });

  // Memoize the current price value to prevent unnecessary rerenders
  const currentPrice = useMemo(
    () => form.watch("price"),
    [form.watch("price")]
  );

  // Set up form with product data when loaded
  useEffect(() => {
    if (product) {
      // Set file preview if product has an image
      if (product.image) {
        setFilePreview(product.image);
        setSelectedMainImage(product.image);
      }

      // Set additional images if they exist
      const additionalImages = product.additional_images as
        | string[]
        | undefined;
      if (
        additionalImages &&
        Array.isArray(additionalImages) &&
        additionalImages.length > 0
      ) {
        setSelectedAdditionalImages(additionalImages);
      }

      // Get category ID from product_categories if it exists
      let categoryId = "";
      // Use type assertions to access nested properties
      const productCategories = product["product_categories"] as
        | any[]
        | undefined;
      if (productCategories && productCategories.length > 0) {
        const firstCategory = productCategories[0];
        if (
          firstCategory.category_id &&
          typeof firstCategory.category_id === "object"
        ) {
          categoryId = firstCategory.category_id.id;
        }
      }

      // Access specifications and other complex properties safely with type assertions
      const specifications = product["specifications"] as any;
      const options = product["options"] as any[] | null;

      // Set up form values
      form.reset({
        name: product.name || "",
        brand: product.brand || "",
        sku: product.sku || "",
        price: product.price || 0,
        description: product.description || "",
        available: product.available !== undefined ? product.available : true,
        variant: product.variant || "",
        // Handle options safely by ensuring it's an array
        options: Array.isArray(options) ? options : [],
        // Handle specifications safely
        features:
          specifications &&
            typeof specifications === "object" &&
            Array.isArray(specifications.features)
            ? specifications.features
            : [],
        finish_and_material:
          specifications && typeof specifications === "object"
            ? specifications.finish_and_material || ""
            : "",
        // Use the extracted categoryId
        category_id: categoryId,
        draft: product.draft || false,
        tags: product.tags || [],
        is_quote: product.is_quote !== undefined ? product.is_quote : false,
      });

      // Set up specifications
      if (
        specifications &&
        typeof specifications === "object" &&
        specifications.specifications &&
        typeof specifications.specifications === "object"
      ) {
        const specEntries = Object.entries(specifications.specifications).map(
          ([key, value]) => ({ key, value: String(value) })
        );
        setSpecs(
          specEntries.length > 0 ? specEntries : [{ key: "", value: "" }]
        );
      }

      // Set up features
      if (
        specifications &&
        typeof specifications === "object" &&
        Array.isArray(specifications.features) &&
        specifications.features.length > 0
      ) {
        setFeatures(specifications.features);
      }

      // Set up helpful hints safely with type assertion
      const helpfulHintsData = product["helpful_hints"] as any[] | null;
      if (
        helpfulHintsData &&
        Array.isArray(helpfulHintsData) &&
        helpfulHintsData.length > 0
      ) {
        setHelpfulHints(helpfulHintsData as UrlLabelItem[]);
      }

      // Set up shipping safely with type assertion
      const deliveryAndShipping = product["delivery_and_shipping"] as any;
      if (
        deliveryAndShipping &&
        typeof deliveryAndShipping === "object" &&
        deliveryAndShipping.shipping &&
        typeof deliveryAndShipping.shipping === "object"
      ) {
        setShipping(deliveryAndShipping.shipping as UrlLabelItem);
      }

      // Set up warranty safely with type assertion
      const warrantyData = product["warranty"] as any;
      if (warrantyData && typeof warrantyData === "object") {
        setWarranty(warrantyData as UrlLabelItem);
      }

      // Set up group prices safely with type assertion
      const productGroupPrices = product["product_group_prices"] as
        | any[]
        | undefined;
      if (
        productGroupPrices &&
        Array.isArray(productGroupPrices) &&
        productGroupPrices.length > 0
      ) {
        const groupPricesObj = productGroupPrices.reduce(
          (acc, item) => {
            acc[item.group_id] = item.custom_price;
            return acc;
          },
          {} as Record<string, number>
        );
        setGroupPrices(groupPricesObj);
      }
    }
  }, [product, form]);

  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files[0]) {
        const selectedFile = e.target.files[0];
        setFile(selectedFile);

        // Create a preview URL
        const url = URL.createObjectURL(selectedFile);
        setFilePreview(url);

        // Clear any selected existing main image since we're uploading a new one
        setSelectedMainImage(null);
      }
    },
    []
  );

  const handleAdditionalFilesChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files.length > 0) {
        const selectedFiles = Array.from(e.target.files);
        setAdditionalFiles((prevFiles) => [...prevFiles, ...selectedFiles]);
      }
    },
    []
  );

  const removeAdditionalFile = useCallback((index: number) => {
    setAdditionalFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  }, []);

  // Handler for selecting main image from library
  const handleMainImageSelect = useCallback((imagePath: string | null) => {
    // This will only be called when the main image is explicitly changed
    setSelectedMainImage(imagePath);

    if (imagePath) {
      setFile(null); // Clear any uploaded file as we're using an existing image
      setFilePreview(null);
    }
  }, []);

  // Handler for selecting additional images from library
  const handleAdditionalImagesSelect = useCallback((imagePaths: string[]) => {
    // Update additional images without affecting other state
    setSelectedAdditionalImages((prevPaths) => {
      // Compare to see if we're actually changing the selection
      const pathsSet = new Set(imagePaths);
      const prevPathsSet = new Set(prevPaths);

      // Only update if there's an actual change
      if (
        pathsSet.size !== prevPathsSet.size ||
        imagePaths.some((path) => !prevPathsSet.has(path)) ||
        prevPaths.some((path) => !pathsSet.has(path))
      ) {
        return imagePaths;
      }

      // If no change, return previous value to prevent unnecessary rerenders
      return prevPaths;
    });
  }, []);

  const handleInstallationFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files[0]) {
        const selectedFile = e.target.files[0];
        // Check if file is PDF
        if (selectedFile.type !== "application/pdf") {
          toast({
            title: "Invalid file",
            description:
              "Please upload a PDF file for installation instructions",
            variant: "destructive",
          });
          return;
        }
        setInstallationFile(selectedFile);
      }
    },
    [toast]
  );

  const addSpecification = useCallback(() => {
    setSpecs((prev) => [...prev, { key: "", value: "" }]);
  }, []);

  const removeSpecification = useCallback((index: number) => {
    setSpecs((prev) => prev.filter((_, i) => i !== index));
  }, []);

  const updateSpecification = useCallback(
    (index: number, field: keyof Specification, value: string) => {
      setSpecs((prev) => {
        const newSpecs = [...prev];
        newSpecs[index] = {
          ...newSpecs[index],
          [field]: value,
        };
        return newSpecs;
      });
    },
    []
  );

  const addFeature = useCallback(() => {
    setFeatures((prev) => [...prev, ""]);
  }, []);

  const removeFeature = useCallback((index: number) => {
    setFeatures((prev) => prev.filter((_, i) => i !== index));
  }, []);

  const updateFeature = useCallback((index: number, value: string) => {
    setFeatures((prev) => {
      const newFeatures = [...prev];
      newFeatures[index] = value;
      return newFeatures;
    });
  }, []);

  const addHelpfulHint = useCallback(() => {
    setHelpfulHints((prev) => [...prev, { url: "", label: "" }]);
  }, []);

  const removeHelpfulHint = useCallback((index: number) => {
    setHelpfulHints((prev) => prev.filter((_, i) => i !== index));
  }, []);

  const updateHelpfulHint = useCallback(
    (index: number, field: keyof UrlLabelItem, value: string) => {
      setHelpfulHints((prev) => {
        const newHints = [...prev];
        newHints[index] = {
          ...newHints[index],
          [field]: value,
        };
        return newHints;
      });
    },
    []
  );

  const onSubmit = useCallback(
    async (values: ProductFormValues) => {
      const groupPricesArray = Object.entries(groupPrices).map(
        ([groupId, price]) => ({
          group_id: groupId,
          custom_price: price,
        })
      );

      form.setValue("group_prices", groupPricesArray);

      try {
        let imagePath: string | null = null;

        // Priority for main image: 1. Uploaded file, 2. Selected image from library, 3. Existing preview
        if (file) {
          const uploadResult = await uploadImageMutation.mutateAsync(file);
          if (uploadResult) {
            imagePath = uploadResult.path;
          }
        } else if (selectedMainImage) {
          // Use existing image from library
          imagePath = selectedMainImage;
        } else if (filePreview && !file) {
          // If we have a filePreview but no file, it's an existing image path
          imagePath = filePreview;
        }

        // Upload additional images (if any)
        const additionalImagePaths: string[] = [...selectedAdditionalImages]; // Start with selected existing images

        if (additionalFiles.length > 0) {
          for (const additionalFile of additionalFiles) {
            const uploadResult =
              await uploadImageMutation.mutateAsync(additionalFile);
            if (uploadResult) {
              additionalImagePaths.push(uploadResult.path);
            }
          }
        }

        let installationPdfPath: string | null = null;
        // Safely access installation_instructions with type assertion
        const installationInstructions = product
          ? product["installation_instructions"]
          : null;
        if (
          installationInstructions &&
          typeof installationInstructions === "string"
        ) {
          installationPdfPath = installationInstructions;
        }

        if (installationFile) {
          // Upload installation PDF file
          const uploadResult =
            await uploadImageMutation.mutateAsync(installationFile);
          if (uploadResult) {
            installationPdfPath = uploadResult.path;
          }
        }

        const processedOptions = values.options?.map((option) => {
          const plainOption: Record<string, any> = {
            name: option.name,
            type: option.type,
          };

          if (option.options) {
            plainOption.options = option.options.map((opt) => ({
              name: opt.name,
              value: opt.value,
              price: opt.price,
            }));
          }

          return plainOption;
        });

        // Convert specifications table to JSON format
        const specificationsJson: Record<string, any> = {};

        // Only add valid specifications (non-empty key and value)
        const specsObj = specs.reduce(
          (acc, spec) => {
            if (spec.key && spec.value) {
              acc[spec.key] = spec.value;
            }
            return acc;
          },
          {} as Record<string, string>
        );

        // Only add valid features (non-empty)
        const validFeatures = features.filter((f) => f.trim() !== "");

        // Build the complete specifications object
        if (Object.keys(specsObj).length > 0) {
          specificationsJson.specifications = specsObj;
        }

        if (validFeatures.length > 0) {
          specificationsJson.features = validFeatures;
        }

        if (values.finish_and_material) {
          specificationsJson.finish_and_material = values.finish_and_material;
        }

        // Process group prices
        const productGroupPrices = Object.entries(groupPrices)
          .filter(([_, price]) => price > 0)
          .map(([groupId, price]) => ({
            group_id: groupId,
            custom_price: price,
            hash: `${values.name}-${groupId}`
              .replace(/\s+/g, "-")
              .toLowerCase(),
          }));

        // Create the API payload with proper typing
        const productData = {
          ...values,
          image: imagePath,
          additional_images: additionalImagePaths, // Always include it, even if empty
          options: processedOptions?.length ? processedOptions : undefined,
          specifications:
            Object.keys(specificationsJson).length > 0
              ? specificationsJson
              : undefined,
          helpful_hints:
            helpfulHints.filter((hint) => hint.label.trim() !== "").length > 0
              ? helpfulHints
                .filter((hint) => hint.label.trim() !== "")
                .map((hint) => ({ ...hint }))
              : undefined,
          installation_instructions: installationPdfPath,
          delivery_and_shipping: {
            shipping: {
              url: shipping.url,
              label: shipping.label,
            },
          },
          warranty: {
            url: warranty.url,
            label: warranty.label,
          },
          group_prices:
            productGroupPrices.length > 0 ? productGroupPrices : undefined,
          user_id: userId,
        };

        await updateProductMutation.mutateAsync(productData);

        toast({
          title: "Success",
          description: "Product has been updated successfully",
          variant: "success",
        });

        router.push("/admin/products");
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to update product",
          variant: "destructive",
        });
      }
    },
    [
      file,
      filePreview,
      selectedMainImage,
      selectedAdditionalImages,
      additionalFiles,
      product,
      installationFile,
      specs,
      features,
      groupPrices,
      helpfulHints,
      shipping,
      warranty,
      uploadImageMutation,
      updateProductMutation,
      form,
      toast,
      router,
      userId,
    ]
  );

  const handleDiscard = useCallback(() => {
    router.push("/admin/products");
  }, [router]);

  const handleSaveDraft = useCallback(() => {
    form.setValue("draft", true);
    form.handleSubmit(onSubmit)();
  }, [form, onSubmit]);

  const handlePublish = useCallback(() => {
    form.setValue("draft", false);
    form.handleSubmit(onSubmit)();
  }, [form, onSubmit]);

  const addOption = useCallback(() => {
    if (!currentOption.name) return;

    const optionToAdd: Record<string, any> = {
      name: currentOption.name,
      type: currentOption.type,
    };

    if (currentOption.type === "select" && currentOption.options) {
      const validOptions = currentOption.options.filter(
        (opt) => opt.name && opt.value
      );

      if (validOptions.length === 0) return;

      optionToAdd.options = validOptions;
    }

    const currentOptions = form.getValues("options") || [];
    form.setValue("options", [...currentOptions, optionToAdd], {
      shouldValidate: true,
    });

    setCurrentOption({
      name: "",
      type: "select",
      options: [{ name: "", value: "" }],
    });
  }, [currentOption, form]);

  const removeOption = useCallback(
    (index: number) => {
      const currentOptions = form.getValues("options") || [];
      form.setValue(
        "options",
        currentOptions.filter((_, i) => i !== index),
        { shouldValidate: true }
      );
    },
    [form]
  );

  const addOptionValue = useCallback(() => {
    if (!currentOption.options) return;

    setCurrentOption((prev) => ({
      ...prev,
      options: [...(prev.options || []), { name: "", value: "" }],
    }));
  }, [currentOption]);

  const removeOptionValue = useCallback(
    (index: number) => {
      if (!currentOption.options) return;

      setCurrentOption((prev) => ({
        ...prev,
        options: prev.options?.filter((_, i) => i !== index),
      }));
    },
    [currentOption]
  );

  const updateOptionValue = useCallback(
    (index: number, field: keyof OptionValue, value: string | number) => {
      if (!currentOption.options) return;

      setCurrentOption((prev) => {
        const newOptions = [...(prev.options || [])];
        newOptions[index] = {
          ...newOptions[index],
          [field]: value,
        };

        return {
          ...prev,
          options: newOptions,
        };
      });
    },
    [currentOption]
  );

  // Use useGetImage hook for the main image
  const mainImageQuery = useGetImage(selectedMainImage || "");

  // Set the mainImageUrl when the query data changes - with optimization
  useEffect(() => {
    // If there's a file uploaded, use filePreview as the main image URL
    if (file || filePreview) {
      return;
    }

    // Only update if the data has changed
    if (
      mainImageQuery.data &&
      selectedMainImage &&
      mainImageUrl !== mainImageQuery.data
    ) {
      setMainImageUrl(mainImageQuery.data);
    } else if (!selectedMainImage && mainImageUrl !== null) {
      // Reset mainImageUrl if selectedMainImage is cleared
      setMainImageUrl(null);
    }
  }, [mainImageQuery.data, selectedMainImage, file, filePreview, mainImageUrl]);

  // Memoize the images to fetch to prevent infinite loops
  const imagesToFetch = useMemo(
    () => (selectedAdditionalImages.length > 0 ? selectedAdditionalImages : []),
    [selectedAdditionalImages]
  );

  // Use useGetImages for additional images with memoized value
  const additionalImageQueries = useGetImages(imagesToFetch);

  // Optimized useEffect for additional image URLs
  useEffect(() => {
    // Skip this effect if queries haven't loaded or if there are no additional images selected
    if (!additionalImageQueries || additionalImageQueries.length === 0) {
      if (additionalImageUrls.length > 0) {
        setAdditionalImageUrls([]);
      }
      return;
    }

    // Create a new array with all URLs from queries that have data
    const urls = additionalImageQueries
      .filter((query) => query.data)
      .map((query) => query.data as string);

    // Compare arrays by value to prevent unnecessary updates
    const prevUrls = JSON.stringify(additionalImageUrls.sort());
    const newUrls = JSON.stringify(urls.sort());

    if (prevUrls !== newUrls) {
      setAdditionalImageUrls(urls);
    }
  }, [additionalImageQueries, additionalImageUrls]);

  if (isProductLoading) {
    return <EditProductFormSkeleton />;
  }

  if (isProductError || !product) {
    return (
      <div className="text-center p-8">
        <h2 className="text-2xl font-bold text-red-500">
          Error loading product
        </h2>
        <p className="mt-2">
          Unable to load product details. Please try again later.
        </p>
        <Button onClick={() => router.push("/admin/products")} className="mt-4">
          Back to Products
        </Button>
      </div>
    );
  }

  return (
    <Card className="w-full border-0 bg-transparent max-w-5xl mx-auto">
      <CardContent className="pt-6 bg-transparent px-0">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center justify-between">
                <h1 className="text-4xl font-bold tracking-tight">
                  Edit Product
                </h1>
              </div>
              <div className="flex justify-end gap-4">
                <AlertDialog
                  open={isDiscardDialogOpen}
                  onOpenChange={setIsDiscardDialogOpen}
                >
                  <AlertDialogTrigger asChild>
                    <Button
                      data-active={form.formState.isDirty}
                      type="button"
                      variant="secondary"
                      disabled={isLoading}
                      className="w-fit data-[active=true]:block"
                    >
                      Discard
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will discard all changes made to this product. This
                        action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel type="button" className="">
                        Cancel
                      </AlertDialogCancel>
                      <AlertDialogAction
                        type="button"
                        onClick={handleDiscard}
                        className=" bg-red-500"
                      >
                        Discard
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
                <Button
                  type="button"
                  variant="outline"
                  disabled={isLoading}
                  className="w-fit"
                  onClick={handleSaveDraft}
                >
                  {isLoading ? "Saving as Draft..." : "Save as Draft"}
                </Button>
                <Button
                  type="button"
                  variant="primary"
                  disabled={isLoading}
                  className="w-fit"
                  onClick={handlePublish}
                >
                  {isLoading ? "Publishing..." : "Publish"}
                </Button>
              </div>
            </div>

            {/* First row: Product Details and Pricing */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Product Details Column */}
              <Card className="h-full">
                <CardContent className="pt-6 space-y-4">
                  <h3 className="text-lg font-semibold">Product Details</h3>
                  <div className="space-y-4">
                    <BasicInformation.NameField form={form} />
                    <BasicInformation.BrandField form={form} />
                    <BasicInformation.VariantField form={form} />
                    <BasicInformation.DescriptionField form={form} />
                    <BasicInformation.TagsField form={form} />
                  </div>
                </CardContent>
              </Card>

              {/* Group Pricing row */}
              <div className="grid grid-cols-1 gap-6">
                {/* Pricing Column */}
                <PricingSection form={form} />

                {/* Group Pricing Card - Pass memoized price value */}
                <GroupPricing
                  groupPrices={groupPrices}
                  setGroupPrices={setGroupPrices}
                  basePrice={currentPrice}
                />
              </div>
            </div>

            {/* Second row: Images and Categories */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Images Column */}
              <ImagesSection
                file={file}
                filePreview={filePreview}
                handleFileChange={handleFileChange}
                additionalFiles={additionalFiles}
                handleAdditionalFilesChange={handleAdditionalFilesChange}
                removeAdditionalFile={removeAdditionalFile}
                selectedMainImage={selectedMainImage}
                selectedAdditionalImages={selectedAdditionalImages}
                handleMainImageSelect={handleMainImageSelect}
                handleAdditionalImagesSelect={handleAdditionalImagesSelect}
                mainImageUrl={mainImageUrl}
                additionalImageUrls={additionalImageUrls}
                allImages={allImages}
              />

              {/* Categories Column */}
              <CategoriesSection
                form={form}
                categories={categoriesQuery.data?.categories || []}
              />
            </div>

            {/* Third row: Others (Options and Additional Info) */}
            <div className="grid grid-cols-1 gap-6">
              {/* Product Options */}
              <ProductOptions
                form={form}
                currentOption={currentOption}
                setCurrentOption={setCurrentOption}
                addOption={addOption}
                removeOption={removeOption}
                addOptionValue={addOptionValue}
                removeOptionValue={removeOptionValue}
                updateOptionValue={updateOptionValue}
              />

              {/* Additional Information */}
              <AdditionalInformation
                form={form}
                specs={specs}
                addSpecification={addSpecification}
                removeSpecification={removeSpecification}
                updateSpecification={updateSpecification}
                features={features}
                addFeature={addFeature}
                removeFeature={removeFeature}
                updateFeature={updateFeature}
                helpfulHints={helpfulHints}
                addHelpfulHint={addHelpfulHint}
                removeHelpfulHint={removeHelpfulHint}
                updateHelpfulHint={updateHelpfulHint}
                installationFile={installationFile}
                handleInstallationFileChange={handleInstallationFileChange}
                setInstallationFile={setInstallationFile}
                shipping={shipping}
                setShipping={setShipping}
                warranty={warranty}
                setWarranty={setWarranty}
              />
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

interface PricingSectionProps {
  form: UseFormReturn<ProductFormValues>;
}

// Extract pricing-related fields from BasicInformation
const PricingSection = ({ form }: PricingSectionProps) => (
  <Card className="h-fit">
    <CardContent className="pt-6 space-y-4">
      <h3 className="text-lg font-semibold">Pricing</h3>
      <div className="space-y-4">
        {/* Price Field */}
        <BasicInformation.PriceField form={form} />

        {/* SKU Field */}
        <BasicInformation.SkuField form={form} />

        {/* Available Switch */}
        <BasicInformation.AvailableField form={form} />
        <BasicInformation.QuoteField form={form} />
      </div>
    </CardContent>
  </Card>
);

interface ImagesSectionProps {
  file: File | null;
  filePreview: string | null;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  additionalFiles: File[];
  handleAdditionalFilesChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  removeAdditionalFile: (index: number) => void;
  selectedMainImage: string | null;
  selectedAdditionalImages: string[];
  handleMainImageSelect: (imagePath: string | null) => void;
  handleAdditionalImagesSelect: (imagePaths: string[]) => void;
  mainImageUrl: string | null;
  additionalImageUrls: string[];
  allImages: string[];
}

// Extract image-related fields
const ImagesSection = ({
  file,
  filePreview,
  handleFileChange,
  additionalFiles,
  handleAdditionalFilesChange,
  removeAdditionalFile,
  selectedMainImage,
  selectedAdditionalImages,
  handleMainImageSelect,
  handleAdditionalImagesSelect,
  mainImageUrl,
  additionalImageUrls,
  allImages,
}: ImagesSectionProps) => (
  <Card className="h-full">
    <CardContent className="pt-6 space-y-4">
      <h3 className="text-lg font-semibold">Images</h3>
      <div className="space-y-6">
        {/* Image Selector Component */}
        {allImages && allImages.length > 0 && (
          <ImageSelector
            allImages={allImages}
            file={file}
            additionalFiles={additionalFiles}
            handleFileChange={handleFileChange}
            handleAdditionalFilesChange={handleAdditionalFilesChange}
            removeAdditionalFile={removeAdditionalFile}
            onMainImageSelect={handleMainImageSelect}
            onAdditionalImagesSelect={handleAdditionalImagesSelect}
            selectedMainImage={selectedMainImage}
            selectedAdditionalImages={selectedAdditionalImages}
          />
        )}

        {/* Preview of selected images */}
        <SelectedImagesPreview
          mainImageUrl={mainImageUrl}
          filePreview={filePreview}
          additionalImageUrls={additionalImageUrls}
          file={file}
          additionalFiles={additionalFiles}
        />
      </div>
    </CardContent>
  </Card>
);

interface CategoriesSectionProps {
  form: UseFormReturn<ProductFormValues>;
  categories: CategoryWithParent[];
}

// Extract category field
const CategoriesSection = ({ form, categories }: CategoriesSectionProps) => (
  <Card className="h-full">
    <CardContent className="pt-6 space-y-4">
      <h3 className="text-lg font-semibold">Categories</h3>
      <div className="space-y-4">
        <BasicInformation.CategoryField form={form} categories={categories} />
      </div>
    </CardContent>
  </Card>
);
