import { Button } from "@/components/ui/shadcn-button";
import { SidebarTrigger, useSidebar } from "@/components/ui/sidebar";
import { useNotifications } from "@/hooks/use-notifications";
import { useRandomProfile } from "@/hooks/use-random-profile";
import { cn } from "@/lib/utils";
import useAuthStore from "@/stores/auth-store";
import { useGlobalSettings } from "@/stores/global-settings";
import { Menu } from "lucide-react";
import { useEffect } from "react";
import { CartContent } from "../store/customers/cart";
import { NavUser } from "../store/nav-user";
import { NotificationsDropdown } from "./notifications-dropdown";

interface HeaderProps {
  onMenuClick: () => void;
}

export function AdminHeader({ onMenuClick }: HeaderProps) {
  const userData = useAuthStore((state) => state.data);
  const { url: jdenticonProfile } = useRandomProfile({ value: userData?.email ?? "" });
  const setCollapseSidebar = useGlobalSettings((state) => state.setCollapseSidebar);
  const collapseSidebar = useGlobalSettings((state) => state.collapseSidebar);
  const notifications = useNotifications();

  const { setOpen } = useSidebar();

  useEffect(() => {
    setOpen(!collapseSidebar);
  }, [collapseSidebar, setOpen]);

  return (
    <header className={cn(
      "sticky left-0 top-0 w-full z-30 backdrop-blur-md bg-transparent transition-all duration-200 h-fit py-4",
    )}>
      <div className="relative w-full px-4 flex h-16 items-center justify-between">
        <SidebarTrigger onClick={() => setCollapseSidebar(!collapseSidebar)} />
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden mr-2"
            onClick={onMenuClick}
          >
            <Menu className="h-5 w-5" />
          </Button>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-4">
          <NotificationsDropdown notifications={notifications} />
          <CartContent />
          <NavUser user={{
            name: userData?.email?.split('@')[0] || "Admin",
            email: userData?.email || "",
            avatar: jdenticonProfile
          }} />
        </div>
      </div>
    </header>
  );
} 