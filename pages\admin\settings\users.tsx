import AdminLayout from "@/components/features/admin/layout";
import { AddStaffAdminDialog } from "@/components/features/admin/role-management/add-staff-admin-dialog";
import { RolePromotionDialog } from "@/components/features/admin/role-management/role-promotion-dialog";
import { SettingsLayout } from "@/components/features/admin/settings/layout";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  DataTable,
  DataTableSkeleton,
  DataTableToolbar
} from "@/components/ui/data-table";
import { DataTableColumnHeader } from "@/components/ui/data-table-column-header";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { RoleBadge } from "@/components/ui/role-badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/shadcn-button";
import { PublicUserWithCustomer } from "@/pages/api/users/index";
import {
  useGetAllUsersQuery,
  useSearchUsersQuery,
} from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { UserRole } from "@/supabase/types";
import {
  ColumnDef,
  ColumnFiltersState,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  Row,
  SortingState,
  Table,
  useReactTable,
} from "@tanstack/react-table";
import { MoreHorizontal, Settings2, User } from "lucide-react";
import Head from "next/head";
import React, { useEffect, useMemo, useRef, useState } from "react";

const columns: ColumnDef<PublicUserWithCustomer>[] = [
  {
    id: "name",
    header: "Name",
    accessorFn: (row) => `${row.first_name} ${row.last_name}`,
    cell: ({ row }) => {
      const user = row.original;
      return (
        <div className="flex items-center gap-2">
          <Avatar className="h-8 w-8">
            <AvatarFallback className="text-xs">
              {user.first_name?.charAt(0)}
              {user.last_name?.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">
              {user.first_name} {user.last_name}
            </div>
            <div className="text-sm text-gray-500">{user.email}</div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "role",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Role" />
    ),
    cell: ({ row }) => {
      const user = row.original;
      return <RoleBadge role={user.role} />;
    },
    enableSorting: true,
    filterFn: (row, columnId, value) => {
      if (!value || value === "all") return true;
      return row.getValue(columnId) === value;
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const user = row.original;
      const statusColors = {
        approved: "bg-green-50 text-green-700 border-green-200",
        pending: "bg-yellow-50 text-yellow-700 border-yellow-200",
        rejected: "bg-red-50 text-red-700 border-red-200",
      };

      return (
        <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-md border ${statusColors[user.status] || statusColors.pending}`}>
          {user.status}
        </span>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created At" />
    ),
    cell: ({ row }) => {
      const createdAt = row.getValue("created_at") as string;
      const date = new Date(createdAt);
      return (
        <div className="text-sm">
          {date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
          })}
        </div>
      );
    },
    enableSorting: true,
  },
  {
    header: "Actions",
    accessorKey: "actions",
    cell: ({ row }) => <UserRoleActions row={row} />,
  },
];

interface UserRoleActionsProps {
  row: Row<PublicUserWithCustomer>;
}

function UserRoleActions({ row }: UserRoleActionsProps) {
  const user = row.original;
  const [showRoleDialog, setShowRoleDialog] = useState(false);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuItem
            onClick={() => navigator.clipboard.writeText(user.id)}
          >
            Copy user ID
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => setShowRoleDialog(true)}>
            <Settings2 className="mr-2 h-4 w-4" />
            Change Role
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <RolePromotionDialog
        user={user}
        open={showRoleDialog}
        onOpenChange={setShowRoleDialog}
      />
    </>
  );
}

function UserSearchFilter({
  onSearch,
}: {
  onSearch: (value: string) => void;
}) {
  const [searchValue, setSearchValue] = useState("");
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);

    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      onSearch(value);
    }, 300);
  };

  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return (
    <Input
      placeholder="Search users..."
      value={searchValue}
      onChange={handleSearchChange}
      className="max-w-md rounded-full"
    />
  );
}

function RoleFilter({ table }: { table: Table<PublicUserWithCustomer> }) {
  const roleOptions: UserRole[] = ["admin", "staff"];

  const handleRoleChange = (role: string) => {
    if (role === "all") {
      table.getColumn("role")?.setFilterValue(undefined);
    } else {
      table.getColumn("role")?.setFilterValue(role);
    }
  };

  return (
    <Select onValueChange={handleRoleChange} defaultValue="all">
      <SelectTrigger className="min-w-32 rounded-full">
        <SelectValue placeholder="Filter by role" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Roles</SelectItem>
        {roleOptions.map((role) => (
          <SelectItem key={role} value={role}>
            <div className="flex items-center gap-2">
              <RoleBadge role={role} />
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export default function SettingsUsersPage() {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchQuery, setSearchQuery] = useState("");
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([
    { id: "created_at", desc: true },
  ]);
  const [showAddDialog, setShowAddDialog] = useState(false);

  const token = useAuthStore((state) => state.token);

  // Roles array for admin and staff users only
  const roles = ['admin', 'staff'];

  // Regular query for all users when not searching
  const {
    data: regularData,
    isLoading: isRegularLoading,
    isError: isRegularError,
  } = useGetAllUsersQuery(page, pageSize, token, roles);

  // Search query for when user is searching
  const {
    data: searchData,
    isLoading: isSearchLoading,
    isError: isSearchError,
  } = useSearchUsersQuery(searchQuery, page, pageSize, token, roles);

  const data = searchQuery ? searchData : regularData;
  const isLoading = searchQuery ? isSearchLoading : isRegularLoading;
  const isError = searchQuery ? isSearchError : isRegularError;

  const users = data?.data ?? [];
  const totalItems = data?.total ?? 0;
  const totalPages = Math.max(1, Math.ceil(totalItems / pageSize));

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    // Reset to first page when searching
    setPage(1);
  };

  const table = useReactTable({
    data: users,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    onSortingChange: setSorting,
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const newPagination = updater({
          pageIndex: page - 1,
          pageSize,
        });
        setPage(newPagination.pageIndex + 1);
        setPageSize(newPagination.pageSize);
      } else {
        setPage(updater.pageIndex + 1);
        setPageSize(updater.pageSize);
      }
    },
    state: {
      sorting,
      columnFilters,
      pagination: {
        pageIndex: page - 1,
        pageSize,
      },
    },
    manualPagination: true,
    pageCount: totalPages,
  });

  return (
    <AdminLayout>
      <Head>
        <title>Users - Settings</title>
        <meta name="description" content="Manage user roles and permissions" />
      </Head>

      <SettingsLayout
        title="User Management"
        description="Manage user roles and permissions for your application"
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div>
            </div>
            <Button className="md:w-auto" onClick={() => setShowAddDialog(true)}>
              <User className="mr-2 h-4 w-4" />
              Promote to Staff/Admin
            </Button>
          </div>

          {/* Search */}
          <UserSearchFilter onSearch={handleSearchChange} />

          {/* Users Table */}
          {isLoading ? (
            <DataTableSkeleton />
          ) : isError ? (
            <div className="rounded-lg border border-red-200 p-4 text-center">
              <p className="text-red-600">Failed to load users</p>
            </div>
          ) : (
            <DataTable
              data={users}
              columns={columns}
              table={table}
              renderToolbar={(table) => (
                <DataTableToolbar table={table}>
                  <div className="flex gap-2">
                    <RoleFilter table={table} />
                  </div>
                </DataTableToolbar>
              )}
            />
          )}
        </div>
      </SettingsLayout>

      {/* Promote to Staff/Admin Dialog */}
      <AddStaffAdminDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
      />
    </AdminLayout>
  );
}