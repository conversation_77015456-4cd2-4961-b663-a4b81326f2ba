import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiRequest, NextApiResponse } from "next";
import { PublicUserWithCustomer } from ".";

export interface GetPendingUsersResponse {
  data: PublicUserWithCustomer[];
  total: number;
  error?: string;
}

async function getPendingUsers(
  req: NextApiRequest,
  res: NextApiResponse<GetPendingUsersResponse>
) {
  const { page = "1", limit = "10" } = req.query;

  const pageNumber = parseInt(page as string);
  const limitNumber = parseInt(limit as string);

  try {
    const supabaseAdminClient = createSupabaseAdminClient();

    // Get total count of pending users
    const { count, error: countError } = await supabaseAdminClient
      .from("users")
      .select("*", { count: "exact", head: true })
      .eq("status", "pending");

    if (countError) {
      throw new Error(countError.message);
    }

    // Get paginated pending users
    const { data, error } = await supabaseAdminClient
      .from("users")
      .select(
        `
                *,
                customer_data:customers(
                    *,
                    group_data:customer_groups(
                        *,
                        data:groups(*)
                    )
                )
            `
      )
      .eq("status", "pending")
      .order("created_at", { ascending: false })
      .range((pageNumber - 1) * limitNumber, pageNumber * limitNumber - 1);

    if (error) {
      throw new Error(error.message);
    }

    return res.status(200).json({
      data: data as PublicUserWithCustomer[],
      total: count || 0,
    });
  } catch (error: any) {
    return res.status(500).json({
      data: [],
      total: 0,
      error: error.message,
    });
  }
}

export default matchRoute({
  GET: checkAdmin(getPendingUsers),
});
