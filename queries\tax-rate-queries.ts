import { GetTaxRatesResponse } from "@/pages/api/tax-rates";
import { UpdateTaxRateResponse } from "@/pages/api/tax-rates/[id]";
import useAuthStore from "@/stores/auth-store";
import { NevadaTaxRate } from "@/supabase/types";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

// Get all Nevada tax rates
async function getTaxRates(): Promise<NevadaTaxRate[]> {
  const response = await fetch(`/api/tax-rates`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  const data = (await response.json()) as GetTaxRatesResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data.taxRates ?? [];
}

export function useGetTaxRatesQuery() {
  return useQuery({
    queryKey: ["tax-rates"],
    queryFn: getTaxRates,
  });
}

// Update a specific tax rate
export interface UpdateTaxRateRequest {
  tax_rate: number;
  city: string;
}

async function updateTaxRate(
  id: string,
  data: UpdateTaxRateRequest,
  token: string
): Promise<NevadaTaxRate> {
  const response = await fetch(`/api/tax-rates/${id}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });

  const responseData = (await response.json()) as UpdateTaxRateResponse;

  if (responseData.error) {
    throw new Error(responseData.error);
  }

  return responseData.taxRate ?? ({} as NevadaTaxRate);
}

export function useUpdateTaxRateMutation() {
  const token = useAuthStore((state) => state.token);
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["update-tax-rate"],
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateTaxRateRequest;
    }) => await updateTaxRate(id, data, token),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["tax-rates"],
      });
    },
    onError: (error) => {
      return error.message;
    },
  });
}

// Get tax rate for a specific city (for checkout)
export function getTaxRateForCityFromApi(
  taxRates: NevadaTaxRate[],
  city: string,
  state: string
): number {
  // Only calculate tax for Nevada
  if (state !== "Nevada" && state !== "NV") {
    return 0;
  }

  // Normalize city name for lookup
  const normalizedCity = city.trim();

  // Find tax rate for the city
  const taxRate = taxRates.find(
    (rate) =>
      rate.city?.toLowerCase() === normalizedCity.toLowerCase()
  );

  if (taxRate && taxRate.tax_rate) {
    return taxRate.tax_rate;
  }

  // Default to lowest rate if city not found
  return 6.85; // Fallback rate
}