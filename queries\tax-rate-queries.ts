import { GetTaxRatesResponse } from "@/pages/api/tax-rates";
import { UpdateTaxRateResponse, GetTaxRateByCityResponse } from "@/pages/api/tax-rates/[id]";
import useAuthStore from "@/stores/auth-store";
import { NevadaTaxRate } from "@/supabase/types";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

// Pagination parameters interface
export interface TaxRatesPaginationParams {
  page?: number;
  limit?: number;
}

// Response interface for paginated tax rates
export interface PaginatedTaxRatesResponse {
  taxRates: NevadaTaxRate[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Get all Nevada tax rates (without pagination - for checkout/utility functions)
async function getTaxRates(): Promise<NevadaTaxRate[]> {
  const response = await fetch(`/api/tax-rates?limit=1000`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  const data = (await response.json()) as GetTaxRatesResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return data.taxRates ?? [];
}

// Get paginated Nevada tax rates
async function getPaginatedTaxRates(params: TaxRatesPaginationParams): Promise<PaginatedTaxRatesResponse> {
  const { page = 1, limit = 10 } = params;

  const response = await fetch(`/api/tax-rates?page=${page}&limit=${limit}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  const data = (await response.json()) as GetTaxRatesResponse;

  if (data.error) {
    throw new Error(data.error);
  }

  return {
    taxRates: data.taxRates ?? [],
    total: data.total ?? 0,
    page: data.page ?? 1,
    limit: data.limit ?? 10,
    totalPages: data.totalPages ?? 0,
  };
}

export function useGetTaxRatesQuery() {
  return useQuery({
    queryKey: ["tax-rates"],
    queryFn: getTaxRates,
  });
}

export function useGetPaginatedTaxRatesQuery(params: TaxRatesPaginationParams) {
  return useQuery({
    queryKey: ["tax-rates", "paginated", params.page, params.limit],
    queryFn: () => getPaginatedTaxRates(params),
  });
}

// Get tax rate for a specific city
async function getTaxRateByCity(city: string): Promise<NevadaTaxRate | null> {
  const response = await fetch(`/api/tax-rates/${encodeURIComponent(city)}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  const data = (await response.json()) as GetTaxRateByCityResponse;

  if (response.status === 404) {
    return null; // City not found
  }

  if (data.error) {
    throw new Error(data.error);
  }

  return data.taxRate ?? null;
}

export function useGetTaxRateByCityQuery(city: string, enabled: boolean = true) {
  return useQuery({
    queryKey: ["tax-rate", city],
    queryFn: () => getTaxRateByCity(city),
    enabled: enabled && !!city,
  });
}

// Update a specific tax rate by city
export interface UpdateTaxRateRequest {
  tax_rate: number;
}

async function updateTaxRateByCity(
  city: string,
  data: UpdateTaxRateRequest,
  token: string
): Promise<NevadaTaxRate> {
  const response = await fetch(`/api/tax-rates/${encodeURIComponent(city)}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });

  const responseData = (await response.json()) as UpdateTaxRateResponse;

  if (responseData.error) {
    throw new Error(responseData.error);
  }

  return responseData.taxRate ?? ({} as NevadaTaxRate);
}

export function useUpdateTaxRateMutation() {
  const token = useAuthStore((state) => state.token);
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["update-tax-rate"],
    mutationFn: async ({
      city,
      data,
    }: {
      city: string;
      data: UpdateTaxRateRequest;
    }) => await updateTaxRateByCity(city, data, token),
    onSuccess: (updatedTaxRate) => {
      // Invalidate all tax rate related queries
      queryClient.invalidateQueries({
        queryKey: ["tax-rates"],
      });
      // Invalidate paginated queries
      queryClient.invalidateQueries({
        queryKey: ["tax-rates", "paginated"],
      });
      // Also invalidate the specific city query
      if (updatedTaxRate.city) {
        queryClient.invalidateQueries({ queryKey: ["tax-rate", updatedTaxRate.city] });
      }
    },
    onError: (error) => {
      return error.message;
    },
  });
}

// Get tax rate for a specific city (for checkout)
export function getTaxRateForCityFromApi(
  taxRates: NevadaTaxRate[],
  city: string,
  state: string
): number {
  // Only calculate tax for Nevada
  if (state !== "Nevada" && state !== "NV") {
    return 0;
  }

  // Normalize city name for lookup
  const normalizedCity = city.trim();

  // Find tax rate for the city
  const taxRate = taxRates.find(
    (rate) =>
      rate.city?.toLowerCase() === normalizedCity.toLowerCase()
  );

  if (taxRate && taxRate.tax_rate) {
    return taxRate.tax_rate;
  }

  // Default to lowest rate if city not found
  return 6.85; // Fallback rate
}