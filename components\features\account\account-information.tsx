import { Skeleton } from "@/components/ui/skeleton";
import { useGetBusinessDetailsQuery, useGetPublicUserQuery, useUpdateUserMutation } from "@/queries/user-queries";
import useAuthStore from "@/stores/auth-store";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/shadcn-button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { SvgSpinners90Ring } from "@/components/common/icons";

interface AccountFormValues {
  first_name: string;
  last_name: string;
  phone: string;
}

export function AccountInformation() {
  const userData = useAuthStore((state) => state.data);
  const updateUserData = useAuthStore((state) => state.updateUserData);
  const userEmail = userData.email;
  const userId = userData.id;
  const token = useAuthStore((state) => state.token);
  const [isEditing, setIsEditing] = useState(false);
  const { toast } = useToast();

  const publicUserQuery = useGetPublicUserQuery(userId, token);
  const updateUserMutation = useUpdateUserMutation(userId, token);
  const userBusinessDetails = useGetBusinessDetailsQuery(userId, token);
  const userBusinessDetailsData = userBusinessDetails.data?.data;

  const form = useForm<AccountFormValues>({
    defaultValues: {
      first_name: "",
      last_name: "",
      phone: ""
    }
  });

  // Sync form with query data when it loads
  useEffect(() => {
    if (publicUserQuery.data) {
      form.reset({
        first_name: publicUserQuery.data.first_name || "",
        last_name: publicUserQuery.data.last_name || "",
        phone: userData.phone || ""
      });
    }
  }, [publicUserQuery.data, userData.phone, form]);

  if (publicUserQuery.isLoading) {
    return <AccountInformationSkeleton />;
  }

  const userInfo = publicUserQuery.data;
  const firstName = userInfo?.first_name;
  const lastName = userInfo?.last_name;
  const phoneNumber = userData.phone;

  const onSubmit = async (data: AccountFormValues) => {
    try {
      await updateUserMutation.mutateAsync(data);
      toast({
        title: "Success",
        description: "Account information updated successfully",
        variant: "success",
        duration: 3000
      });
      setIsEditing(false);
      form.reset({
        first_name: firstName || "",
        last_name: lastName || "",
        phone: phoneNumber || ""
      });
    } catch (error) {
      toast({
        title: "Failed to update account information",
        description: error.message,
        variant: "destructive",
        duration: 3000
      });
    }
  };

  const handleCancel = () => {
    form.reset({
      first_name: firstName || "",
      last_name: lastName || "",
      phone: phoneNumber || ""
    });
    setIsEditing(false);
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Account Information</h2>
          <p className="text-sm text-gray-500">
            View or update your account information.
          </p>
        </div>
        {!isEditing && (
          <Button
            onClick={() => setIsEditing(true)}
            variant="outline"
          >
            Edit
          </Button>
        )}
      </div>

      {isEditing ? (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col gap-4 pt-6">
            <FormField
              control={form.control}
              name="first_name"
              rules={{ required: "First name is required" }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-bold uppercase">First Name</FormLabel>
                  <FormControl>
                    <Input className="max-w-md" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="last_name"
              rules={{ required: "Last name is required" }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-bold uppercase">Last Name</FormLabel>
                  <FormControl>
                    <Input className="max-w-md" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-bold uppercase">Phone Number</FormLabel>
                  <FormControl>
                    <Input className="max-w-md" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex flex-col gap-2">
              <p className="text-sm font-bold uppercase">Email Address</p>
              <p className="text-sm font-medium text-gray-500">{userEmail}</p>
            </div>

            <div className="flex flex-col gap-4 pt-6">
              {
                updateUserMutation.isError ? (
                  <p className="text-sm text-red-500">
                    {updateUserMutation.error.message}
                  </p>
                ) : null
              }
              <div className="flex gap-2">
                <Button
                  type="submit"
                  className="bg-primary text-white rounded-md py-2 text-sm font-medium"
                  disabled={updateUserMutation.isPending}
                >
                  {updateUserMutation.isPending ? "Saving..." : "Save Changes"}
                  {
                    updateUserMutation.isPending && (
                      <SvgSpinners90Ring className="w-4 h-4 ml-2 animate-spin" />
                    )
                  }
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={updateUserMutation.isPending}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </form>
        </Form>
      ) : (
        <div className="flex flex-col gap-4 pt-6">
          <div className="flex flex-col gap-2">
            <p className="text-sm font-bold uppercase">First Name</p>
            <p className="text-sm font-medium text-gray-500">{firstName}</p>
          </div>

          <div className="flex flex-col gap-2">
            <p className="text-sm font-bold uppercase">Last Name</p>
            <p className="text-sm font-medium text-gray-500">{lastName}</p>
          </div>

          <div className="flex flex-col gap-2">
            <p className="text-sm font-bold uppercase">Phone Number</p>
            <p className="text-sm font-medium text-gray-500">{phoneNumber ? phoneNumber : "N/A"}</p>
          </div>

          <div className="flex flex-col gap-2">
            <p className="text-sm font-bold uppercase">Email Address</p>
            <p className="text-sm font-medium text-gray-500">{userEmail}</p>
          </div>

          {/* Business Details Section */}
          <div className="flex flex-col gap-2 mt-8 border-t pt-6">
            <h3 className="text-lg font-bold">Business Details</h3>
            <p className="text-sm text-gray-500 mb-4">Your business information.</p>

            {userBusinessDetails.isLoading ? (
              <BusinessDetailsSkeleton />
            ) : userBusinessDetailsData ? (
              <div className="flex flex-col gap-6">
                {/* Company Information Section */}
                <div className="border-b pb-4">
                  <h4 className="text-md font-semibold mb-3">Company Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {userBusinessDetailsData?.business_type && (
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-bold uppercase">Business Type</p>
                        <p className="text-sm font-medium text-gray-500">{userBusinessDetailsData.business_type}</p>
                      </div>
                    )}

                    {userBusinessDetailsData?.business_nature && (
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-bold uppercase">Business Nature</p>
                        <p className="text-sm font-medium text-gray-500">{userBusinessDetailsData.business_nature}</p>
                      </div>
                    )}

                    {userBusinessDetailsData?.maxton_account && (
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-bold uppercase">Maxton Account</p>
                        <p className="text-sm font-medium text-gray-500">{userBusinessDetailsData.maxton_account}</p>
                      </div>
                    )}

                    {userBusinessDetailsData?.website && (
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-bold uppercase">Website</p>
                        <p className="text-sm font-medium underline text-primary">
                          <a href={userBusinessDetailsData.website} target="_blank" rel="noreferrer noopener">
                            {userBusinessDetailsData.website}
                          </a>
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Technical Information Section */}
                <div className="border-b pb-4">
                  <h4 className="text-md font-semibold mb-3">Technical Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {userBusinessDetailsData?.has_mechanic && (
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-bold uppercase">Has Mechanic</p>
                        <p className="text-sm font-medium text-gray-500">{userBusinessDetailsData.has_mechanic}</p>
                      </div>
                    )}

                    {userBusinessDetailsData?.number_of_elevators !== null && (
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-bold uppercase">Number of Elevators</p>
                        <p className="text-sm font-medium text-gray-500">{userBusinessDetailsData.number_of_elevators}</p>
                      </div>
                    )}

                    {userBusinessDetailsData?.technician && (
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-bold uppercase">Technician</p>
                        <p className="text-sm font-medium text-gray-500">{userBusinessDetailsData.technician}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Contact Information Section */}
                <div>
                  <h4 className="text-md font-semibold mb-3">Contact Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {userBusinessDetailsData?.authorized_contact_name && (
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-bold uppercase">Authorized Contact</p>
                        <p className="text-sm font-medium text-gray-500">{userBusinessDetailsData.authorized_contact_name}</p>
                      </div>
                    )}

                    {userBusinessDetailsData?.buyer_name && (
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-bold uppercase">Buyer Name</p>
                        <p className="text-sm font-medium text-gray-500">{userBusinessDetailsData.buyer_name}</p>
                      </div>
                    )}

                    {userBusinessDetailsData?.buyer_phone && (
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-bold uppercase">Buyer Phone</p>
                        <p className="text-sm font-medium text-gray-500">{userBusinessDetailsData.buyer_phone}</p>
                      </div>
                    )}

                    {userBusinessDetailsData?.account_payable_contact && (
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-bold uppercase">Account Payable Contact</p>
                        <p className="text-sm font-medium text-gray-500">{userBusinessDetailsData.account_payable_contact}</p>
                      </div>
                    )}

                    {userBusinessDetailsData?.account_payable_phone && (
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-bold uppercase">Account Payable Phone</p>
                        <p className="text-sm font-medium text-gray-500">{userBusinessDetailsData.account_payable_phone}</p>
                      </div>
                    )}

                    {userBusinessDetailsData?.technical_contact && (
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-bold uppercase">Technical Contact</p>
                        <p className="text-sm font-medium text-gray-500">{userBusinessDetailsData.technical_contact}</p>
                      </div>
                    )}

                    {userBusinessDetailsData?.technical_contact_phone && (
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-bold uppercase">Technical Contact Phone</p>
                        <p className="text-sm font-medium text-gray-500">{userBusinessDetailsData.technical_contact_phone}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-sm font-medium text-gray-500">No business details available.</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

function AccountInformationSkeleton() {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-2">
        <h1 className="text-2xl font-bold">Account Information</h1>
        <p className="text-sm text-gray-500">
          View or update your account information.
        </p>
      </div>
      <div className="flex flex-col gap-4 pt-6">
        <div className="flex flex-col gap-2">
          <Skeleton className="w-36 h-4" />
          <Skeleton className="w-48 h-4" />
        </div>

        <div className="flex flex-col gap-2">
          <Skeleton className="w-36 h-4" />
          <Skeleton className="w-48 h-4" />
        </div>
      </div>

      {/* Business Details Section */}
      <div className="flex flex-col gap-2 mt-8 border-t pt-6">
        <h3 className="text-lg font-bold">Business Details</h3>
        <p className="text-sm text-gray-500 mb-4">Your business information.</p>
        <BusinessDetailsSkeleton />
      </div>
    </div>
  );
}

function BusinessDetailsSkeleton() {
  return (
    <div className="flex flex-col gap-6">
      {/* Company Information Section Skeleton */}
      <div className="border-b pb-4">
        <Skeleton className="w-48 h-5 mb-3" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex flex-col gap-2">
            <Skeleton className="w-36 h-4" />
            <Skeleton className="w-48 h-4" />
          </div>
          <div className="flex flex-col gap-2">
            <Skeleton className="w-36 h-4" />
            <Skeleton className="w-48 h-4" />
          </div>
          <div className="flex flex-col gap-2">
            <Skeleton className="w-36 h-4" />
            <Skeleton className="w-48 h-4" />
          </div>
          <div className="flex flex-col gap-2">
            <Skeleton className="w-36 h-4" />
            <Skeleton className="w-48 h-4" />
          </div>
        </div>
      </div>
      
      {/* Technical Information Section Skeleton */}
      <div className="border-b pb-4">
        <Skeleton className="w-48 h-5 mb-3" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex flex-col gap-2">
            <Skeleton className="w-36 h-4" />
            <Skeleton className="w-48 h-4" />
          </div>
          <div className="flex flex-col gap-2">
            <Skeleton className="w-36 h-4" />
            <Skeleton className="w-48 h-4" />
          </div>
          <div className="flex flex-col gap-2">
            <Skeleton className="w-36 h-4" />
            <Skeleton className="w-48 h-4" />
          </div>
        </div>
      </div>
      
      {/* Contact Information Section Skeleton */}
      <div>
        <Skeleton className="w-48 h-5 mb-3" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex flex-col gap-2">
            <Skeleton className="w-36 h-4" />
            <Skeleton className="w-48 h-4" />
          </div>
          <div className="flex flex-col gap-2">
            <Skeleton className="w-36 h-4" />
            <Skeleton className="w-48 h-4" />
          </div>
          <div className="flex flex-col gap-2">
            <Skeleton className="w-36 h-4" />
            <Skeleton className="w-48 h-4" />
          </div>
          <div className="flex flex-col gap-2">
            <Skeleton className="w-36 h-4" />
            <Skeleton className="w-48 h-4" />
          </div>
        </div>
      </div>
    </div>
  );
}
