import { OrderStatusBadge } from "@/components/features/store/orders-table";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge, BadgeProps } from "@/components/ui/badge";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  DataTable,
  DataTableFilter,
  DataTableSkeleton,
  DataTableToolbar,
} from "@/components/ui/data-table";
import { DataTableViewOptions } from "@/components/ui/data-table-view-options";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog-shadcn";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/shadcn-button";
import { toast } from "@/hooks/use-toast";
import { CancellationRequestWithCustomerAndOrder } from "@/pages/api/orders/cancellation-requests";
import {
  useGetCancellationRequestsQuery,
  useUpdateCancellationRequestMutation,
} from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
  Table,
} from "@tanstack/react-table";
import { Loader2, MoreHorizontal } from "lucide-react";
import { useEffect, useMemo, useState, useRef } from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Input } from "@/components/ui/input";

// Define table columns
const columns: ColumnDef<CancellationRequestWithCustomerAndOrder>[] = [
  {
    header: "Request ID",
    accessorKey: "id",
    cell: ({ row }) => {
      const id = row.original.id;
      return <div>{id.split("-")[0]}...</div>;
    },
  },
  {
    header: "Customer",
    accessorKey: "user_data.email",
  },
  {
    header: "Order",
    accessorKey: "order_data.id",
    cell: ({ row }) => {
      const orderId = row.original.order_data?.id;
      return <div>{orderId ? orderId.split("-")[0] + "..." : "N/A"}</div>;
    },
  },
  {
    header: "Date",
    accessorKey: "created_at",
    cell: ({ row }) => {
      const date = new Date(row.original.created_at);
      const formattedDate = date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });

      return <div>{formattedDate}</div>;
    },
  },
  {
    header: "Status",
    accessorKey: "status",
    cell: ({ row }) => {
      const status = row.original.order_data?.order_status?.[0]?.status;
      return <OrderStatusBadge status={status ?? ""} />;
    },
  },
  {
    header: "Actions",
    id: "actions",
    cell: ({ row }) => <CancellationRequestActions row={row} />,
  },
];

export function CancellationRequestsDataTable() {
  const accessToken = useAuthStore((state) => state.token);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Query with the current search term
  const { data, isLoading, error } = useGetCancellationRequestsQuery(
    page,
    limit,
    accessToken,
    searchTerm
  );

  // Function to handle server-side search
  const handleSearch = (value: string) => {
    // Only set searching state if the value has changed
    if (value !== searchTerm) {
      setIsSearching(true);
      setSearchTerm(value);
      setPage(1); // Reset to first page when searching
    }
  };

  // When new data arrives, clear the searching state
  useEffect(() => {
    if (isSearching && !isLoading) {
      setIsSearching(false);
    }
  }, [isLoading, isSearching]);

  // Convert data to the correct format for the table
  const tableData = useMemo(() => data?.data || [], [data]);
  const total = data?.total || 0;
  const totalPages = data?.totalPages || 1;

  // Create table instance
  const table = useReactTable({
    data: tableData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    enableRowSelection: true,
    enableColumnResizing: true,
    enableSorting: true,
    manualPagination: true,
    pageCount: totalPages,
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const newPagination = updater({
          pageIndex: page - 1,
          pageSize: limit,
        });
        setPage(newPagination.pageIndex + 1);
        setLimit(newPagination.pageSize);
      } else {
        setPage(updater.pageIndex + 1);
        setLimit(updater.pageSize);
      }
    },
    state: {
      pagination: {
        pageIndex: page - 1, // convert to 0-indexed for the table
        pageSize: limit,
      },
    },
  });

  return (
    <Card className="container mx-auto border-none">
      <CardContent>
        {/* Separate toolbar from table rendering */}
        <div className="mb-4">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center gap-2 min-w-sm">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div>
                      <DataTableFilter
                        ref={searchInputRef}
                        table={table as Table<unknown>}
                        column="id"
                        placeholder="Search by request ID..."
                        className="max-w-md min-w-80 h-12"
                        onSearch={handleSearch}
                        serverSideSearch={true}
                        debounceTime={300}
                        preserveFocus={true}
                      />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="max-w-xs">
                    <p>
                      Search by full or partial request ID. For exact matches,
                      paste the complete ID.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <DataTableViewOptions table={table as Table<unknown>} />
          </div>
        </div>

        {/* Conditionally render table or skeleton */}
        {isLoading || isSearching ? (
          <DataTableSkeleton />
        ) : (
          <DataTable
            data={tableData}
            columns={columns}
            table={table}
            className="border-none"
            renderToolbar={() => null} // We're rendering the toolbar separately above
          />
        )}
      </CardContent>
    </Card>
  );
}

function StatusBadge({ status }: { status: string }) {
  const statusMap = {
    pending: "pending",
    approved: "success",
    rejected: "destructive",
  };

  const variant = statusMap[status as keyof typeof statusMap] ?? "default";

  return (
    <Badge className="uppercase" variant={variant as BadgeProps["variant"]}>
      {status}
    </Badge>
  );
}

function CancellationRequestDetails({
  request,
  onClose,
}: {
  request: CancellationRequestWithCustomerAndOrder;
  onClose: () => void;
}) {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Cancellation Request Details</h3>
        <div className="mt-2 grid grid-cols-2 gap-3 text-sm">
          <div className="font-medium">Request ID:</div>
          <div>{request.id}</div>
          <div className="font-medium">Status:</div>
          <div>
            <StatusBadge status={request.status || ""} />
          </div>
          <div className="font-medium">Created At:</div>
          <div>{new Date(request.created_at).toLocaleString()}</div>
          <div className="font-medium">Reason:</div>
          <div>{request.comment || "No reason provided"}</div>
        </div>
      </div>

      <div className="border-t pt-4">
        <h3 className="text-lg font-medium">Order Details</h3>
        <div className="mt-2 grid grid-cols-2 gap-3 text-sm">
          <div className="font-medium">Order ID:</div>
          <div>{request.order_id || request.order_data?.id || "N/A"}</div>
          <div className="font-medium">Total Amount:</div>
          <div>${request.order_data?.total_amount?.toFixed(2) || "N/A"}</div>
          <div className="font-medium">Order Status:</div>
          <div>
            <OrderStatusBadge
              status={request.order_data?.order_status?.[0]?.status || ""}
            />
          </div>
          <div className="font-medium">Tax Exempt:</div>
          <div>
            {request.order_data?.tax_exempt === true ? "Yes" : "No"}
          </div>
        </div>
      </div>

      <div className="border-t pt-4">
        <h3 className="text-lg font-medium">Customer Details</h3>
        <div className="mt-2 grid grid-cols-2 gap-3 text-sm">
          <div className="font-medium">Customer ID:</div>
          <div>{request.user_id || request.user_data?.id || "N/A"}</div>
          <div className="font-medium">Email:</div>
          <div>{request.user_data?.email || "N/A"}</div>
        </div>
      </div>

      <DialogFooter>
        <Button onClick={onClose}>Close</Button>
      </DialogFooter>
    </div>
  );
}

function CancellationRequestActions({ row }: { row: any }) {
  const accessToken = useAuthStore((state) => state.token);
  const cancellationRequestMutation =
    useUpdateCancellationRequestMutation(accessToken);
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);

  const handleStatusUpdate = (status: string) => {
    try {
      cancellationRequestMutation.mutate({
        status: status as "approved" | "rejected",
        id: row.original.id,
        order_id: row.original.order_id || row.original.order_data?.id || "",
      });
    } catch (e) {
      toast({
        title: "Error updating cancellation request",
        description: "Please try again later",
        variant: "destructive",
      });
    }
  };

  const handleApprove = () => {
    handleStatusUpdate("approved");
  };

  const handleReject = () => {
    handleStatusUpdate("rejected");
  };

  useEffect(
    function showToast() {
      if (cancellationRequestMutation.isSuccess) {
        toast({
          title: "Cancellation request updated",
          description: "The cancellation request has been updated successfully",
        });
        setShowApproveDialog(false);
        setShowRejectDialog(false);
      }

      if (cancellationRequestMutation.isError) {
        toast({
          title: "Error updating cancellation request",
          description: "Please try again later",
          variant: "destructive",
        });
      }
    },
    [cancellationRequestMutation.isSuccess, cancellationRequestMutation.isError]
  );

  return (
    <>
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Cancellation Request Details</DialogTitle>
            <DialogDescription>
              Detailed information about this cancellation request and
              associated order
            </DialogDescription>
          </DialogHeader>
          <CancellationRequestDetails
            request={row.original}
            onClose={() => setShowDetailsDialog(false)}
          />
        </DialogContent>
      </Dialog>

      <AlertDialog
        open={showApproveDialog}
        onOpenChange={(open) => {
          // Only allow closing if not processing
          if (!cancellationRequestMutation.isPending || !open) {
            setShowApproveDialog(open);
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Approve Cancellation Request</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to approve this cancellation request? This
              action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={cancellationRequestMutation.isPending}>
              Cancel
            </AlertDialogCancel>
            <Button
              onClick={handleApprove}
              disabled={cancellationRequestMutation.isPending}
              type="button"
            >
              {cancellationRequestMutation.isPending
                ? "Processing..."
                : "Approve"}
              {cancellationRequestMutation.isPending && (
                <Loader2 className="w-4 h-4 ml-2 animate-spin" />
              )}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog
        open={showRejectDialog}
        onOpenChange={(open) => {
          if (!cancellationRequestMutation.isPending || !open) {
            setShowRejectDialog(open);
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reject Cancellation Request</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to reject this cancellation request? This
              action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={cancellationRequestMutation.isPending}>
              Cancel
            </AlertDialogCancel>
            <Button
              onClick={handleReject}
              disabled={cancellationRequestMutation.isPending}
              type="button"
            >
              {cancellationRequestMutation.isPending
                ? "Processing..."
                : "Reject"}
              {cancellationRequestMutation.isPending && (
                <Loader2 className="w-4 h-4 ml-2 animate-spin" />
              )}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => setShowDetailsDialog(true)}>
            View details
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setShowApproveDialog(true)}>
            Approve
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setShowRejectDialog(true)}>
            Reject
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
}
