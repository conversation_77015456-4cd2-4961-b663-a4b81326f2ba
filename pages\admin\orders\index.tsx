import AdminLayout from "@/components/features/admin/layout";
import { OrderStatusBadge } from "@/components/features/store/orders-table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DataTable,
  DataTableFilter,
  DataTableSkeleton,
  DataTableToolbar,
} from "@/components/ui/data-table";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog-shadcn";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { But<PERSON> } from "@/components/ui/shadcn-button";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/hooks/use-toast";
import { formatPrice } from "@/lib/utils";
import {
  useGetOrderQuery,
  useGetOrdersQuery,
  useUpdateOrderStatusMutation,
} from "@/queries/admin-queries";
import { useGetImage } from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import { Order, OrderStatus } from "@/supabase/types";
import {
  ColumnDef,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
  SortingState,
  ColumnFiltersState,
} from "@tanstack/react-table";
import {
  Check,
  ChevronDown,
  ExternalLink,
  FileText,
  Link as LinkIcon,
  Loader2,
  MoreHorizontal,
  Package,
  ShoppingCart,
  Truck,
  Filter,
} from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useMemo, useState, useEffect } from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { OrderStatusType } from "@/pages/api/orders/[id]/status";

interface AdminOrderData extends Order {
  order_statuses: OrderStatus[];
  order_items: Array<{
    id: string;
    quantity: number;
    options: Array<{
      name: string;
      value: string;
      price?: number;
    }>;
    products: {
      id: string;
      name: string;
      price: number;
      image?: string;
    };
  }>;
  shipping_addresses: {
    id?: string;
    contact_name?: string;
    address?: string;
    city?: string;
    state?: string;
    zip_code?: string;
  };
  totalItems: number;
  totalAmount: number;
  currentStatus: string;
  customer_data?: any;
}

const ORDER_STATUSES: OrderStatusType[] = [
  "pending",
  "awaiting_payment",
  "processing",
  "expired",
  "shipped",
  "delivered",
  "completed",
  "processed",
  "canceled",
  "denied",
  "canceled_reversal",
  "failed",
  "refunded",
  "reversed",
  "chargeback",
  "voided",
];

// Define the StatusCell as a standalone component
function StatusCell({ row, token }: { row: any; token: string }) {
  const order = row.original;
  const orderId = order.id;
  const trackingUrl = order.tracking_link;
  const currentStatus = order.currentStatus;
  const updateStatusMutation = useUpdateOrderStatusMutation(orderId, token);
  const [open, setOpen] = useState(false);
  const [showTrackingDialog, setShowTrackingDialog] = useState(false);
  const [trackingLink, setTrackingLink] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<OrderStatusType | null>(
    null
  );

  const handleStatusChange = async (status: OrderStatusType) => {
    // If changing to shipped status and no tracking link exists, show dialog
    if (status === "shipped" && !trackingUrl) {
      setSelectedStatus(status);
      setShowTrackingDialog(true);
      setOpen(false);
      return;
    }

    try {
      await updateStatusMutation.mutateAsync({ status });
      toast({
        title: "Order Status Updated",
        description: `Order status changed to ${status}`,
        variant: "success",
        duration: 3000,
      });
      setOpen(false);
    } catch (error: any) {
      toast({
        title: "Error Updating Order Status",
        description: `Failed to update status: ${
          error.message || "Unknown error"
        }`,
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  const handleTrackingSubmit = async () => {
    if (!selectedStatus) return;

    try {
      await updateStatusMutation.mutateAsync({
        status: selectedStatus,
        trackingUrl: trackingLink,
      });
      toast({
        title: "Order Status Updated",
        description: `Order shipped with tracking information`,
        variant: "success",
        duration: 3000,
      });
      setShowTrackingDialog(false);
      setTrackingLink("");
      setSelectedStatus(null);
    } catch (error: any) {
      toast({
        title: "Error Updating Order Status",
        description: `Failed to update status: ${
          error.message || "Unknown error"
        }`,
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  return (
    <>
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            type="button"
            variant="ghost"
            className="flex items-center justify-between h-8 px-3 py-1 hover:bg-transparent w-fit"
          >
            <OrderStatusBadge status={currentStatus} />
            <ChevronDown className="h-4 w-4 ml-2" />
            {updateStatusMutation.isPending ? (
              <Loader2 className="h-4 w-4 ml-auto animate-spin" />
            ) : null}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48 p-0">
          <ScrollArea className="h-60">
            {ORDER_STATUSES.map((status) => (
              <DropdownMenuItem
                key={status}
                className={`flex items-center gap-2 px-3 py-2 cursor-pointer ${
                  currentStatus === status ? "bg-accent" : ""
                }`}
                onClick={() => handleStatusChange(status)}
              >
                <div className="flex items-center gap-2 w-full">
                  <OrderStatusBadge status={status} />
                  {currentStatus === status && (
                    <Check className="h-4 w-4 ml-auto" />
                  )}
                </div>
              </DropdownMenuItem>
            ))}
          </ScrollArea>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={showTrackingDialog} onOpenChange={setShowTrackingDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add Tracking Information</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="tracking-link">Tracking Link</Label>
              <div className="flex items-center gap-2">
                <LinkIcon className="h-4 w-4 text-muted-foreground" />
                <Input
                  id="tracking-link"
                  placeholder="https://tracking.carrier.com/ABC123"
                  value={trackingLink}
                  onChange={(e) => setTrackingLink(e.target.value)}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setShowTrackingDialog(false);
                setTrackingLink("");
                setSelectedStatus(null);
              }}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleTrackingSubmit}
              disabled={updateStatusMutation.isPending}
            >
              {updateStatusMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : null}
              Save & Update Status
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default function Orders() {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [statusFilter, setStatusFilter] = useState("");
  const { token, data: userData } = useAuthStore((state) => state);
  const { data, isLoading, error } = useGetOrdersQuery(
    page,
    limit,
    token,
    searchTerm,
    statusFilter
  );

  const [selectedOrder, setSelectedOrder] = useState<AdminOrderData | null>(
    null
  );
  const [showOrderDetails, setShowOrderDetails] = useState(false);

  // Function to handle server-side search
  const handleSearch = (value: string) => {
    // Only set searching state if the value has changed
    if (value !== searchTerm) {
      setIsSearching(true);
      setSearchTerm(value);
      setPage(1); // Reset to first page when searching
    }
  };

  // Function to handle status filter changes
  const handleStatusChange = (status: string) => {
    setStatusFilter(status);
    setPage(1); // Reset to first page when changing filters
  };

  // When new data arrives, clear the searching state
  useEffect(() => {
    if (isSearching && !isLoading) {
      setIsSearching(false);
    }
  }, [isLoading, isSearching]);

  const transformOrderData = (orders: any[]): AdminOrderData[] => {
    if (!orders) return [];

    return orders.map((order) => {
      const orderItems = order.order_items || [];
      const totalItems = orderItems.reduce(
        (acc: number, item: any) => acc + item.quantity,
        0
      );
      const totalAmount = order.total_amount;

      const currentStatus = order.order_statuses?.[0]?.status || "pending";

      return {
        ...order,
        totalItems,
        totalAmount,
        currentStatus,
      };
    });
  };

  const tableData = useMemo(() => transformOrderData(data?.data || []), [data]);
  const total = data?.total || 0;
  const totalPages = data?.totalPages || 1;

  // Define columns with the token for the StatusCell
  const columns = useMemo<ColumnDef<AdminOrderData>[]>(
    () => [
      {
        header: "Company Name",
        accessorKey: "customer_data.company_name",
        cell: ({ row }) => {
          const companyName =
            row.original?.customer_data?.company_name?.trim() || "N/A";

          return <div>{companyName}</div>;
        },
      },
      {
        header: "Order ID",
        accessorKey: "id",
        cell: ({ row }) => {
          const id = row.original.id;
          const orderIdWithoutDash = id.split("-");
          const firstPart = orderIdWithoutDash.at(0);
          const secondPart = orderIdWithoutDash.at(-1);

          return (
            <div>
              {firstPart}...{secondPart}
            </div>
          );
        },
      },
      {
        header: "Customer ID",
        accessorKey: "customer_data.id",
        cell: ({ row }) => {
          const maxtonAccount =
            row?.original?.customer_data?.user_data?.business_details?.[0]?.maxton_account?.trim();

          const customerId =
            maxtonAccount !== "" && maxtonAccount !== undefined
              ? maxtonAccount
              : row?.original?.customer_id;

          return <div>{customerId}</div>;
        },
      },
      {
        header: "Customer",
        accessorKey: "customer_data.user_data.email",
      },
      {
        header: "Date",
        accessorKey: "created_at",
        cell: ({ row }) => {
          const date = new Date(row.getValue("created_at"));
          return <div>{date.toLocaleDateString()}</div>;
        },
      },
      {
        header: "Items",
        accessorKey: "totalItems",
      },
      {
        header: "Total",
        accessorKey: "totalAmount",
        cell: ({ row }) => {
          const amount = row.getValue("totalAmount") as number;
          return <div>{formatPrice(amount)}</div>;
        },
      },
      {
        header: "Status",
        accessorKey: "currentStatus",
        cell: ({ row }) => <StatusCell row={row} token={token} />,
      },
      {
        header: "Tax Exempt",
        accessorKey: "tax_exempt",
        cell: ({ row }) => {
          const taxExempt = row.original.tax_exempt as boolean | null;
          return <div>{taxExempt === true ? "Yes" : "No"}</div>;
        },
      },
      {
        header: "Tracking",
        accessorKey: "tracking_link",
        cell: ({ row }) => {
          const order = row.original;
          const trackingLink = order.tracking_link;
          if (!trackingLink) {
            return <div>No tracking link</div>;
          }
          if (trackingLink) {
            return (
              <div>
                <Button variant="outline" asChild className="gap-2">
                  <Link
                    href={trackingLink}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <ExternalLink className="h-4 w-4" />
                    Track Order
                  </Link>
                </Button>
              </div>
            );
          }
        },
      },
      {
        header: "Actions",
        id: "actions",
        meta: {
          className: "sticky right-0 bg-gray-100 dark:bg-zinc-950",
        },
        cell: ({ row }) => {
          const order = row.original;
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-full p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => {
                    setSelectedOrder(order);
                    setShowOrderDetails(true);
                  }}
                >
                  View details
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
      },
    ],
    [token]
  );

  // Create a table instance with server-side pagination
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [rowSelection, setRowSelection] = useState({});

  const table = useReactTable({
    data: tableData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    pageCount: totalPages,
    manualPagination: true, // Tell the table we're handling pagination server-side
    manualFiltering: true, // Enable server-side filtering
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    onRowSelectionChange: setRowSelection,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const newPagination = updater({
          pageIndex: page - 1,
          pageSize: limit,
        });
        setPage(newPagination.pageIndex + 1);
        setLimit(newPagination.pageSize);
      } else {
        setPage(updater.pageIndex + 1);
        setLimit(updater.pageSize);
      }
    },
    state: {
      sorting,
      rowSelection,
      columnFilters,
      pagination: {
        pageIndex: page - 1, // convert to 0-indexed for the table
        pageSize: limit,
      },
    },
  });

  return (
    <AdminLayout>
      <Head>
        <title>Sales</title>
      </Head>
      <div className="min-h-screen bg-white">
        <div className="flex-1 space-y-8 p-8 pt-6">
          {/* Header Section */}
          <div className="flex flex-col space-y-6">
            <div className="flex flex-col space-y-2">
              <h2 className="text-3xl font-bold tracking-tight">
                Orders Management
              </h2>
              <p className="text-muted-foreground">
                View and manage all customer orders in one place
              </p>
            </div>

            <div className="flex flex-col md:flex-row gap-4">
              <Badge variant="outline" className="w-fit px-6 py-2 text-lg">
                <ShoppingCart className="mr-2 h-4 w-4" />
                <span className="text-primary">Total Orders: {total}</span>
              </Badge>
            </div>
          </div>

          {/* Orders Table Section */}
          <Card className="hover:shadow-md transition-all">
            <CardHeader>
              <CardTitle>Orders List</CardTitle>
            </CardHeader>
            <CardContent>
              {/* Search and Filter Controls */}
              <div className="mb-4">
                <DataTableToolbar table={table}>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div>
                          <DataTableFilter
                            table={table}
                            column="id"
                            placeholder="Search by order ID..."
                            className="max-w-md min-w-80 h-12"
                            onSearch={handleSearch}
                            serverSideSearch={true}
                            debounceTime={300}
                          />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="bottom" className="max-w-xs">
                        <p>
                          Search by full or partial order ID. For exact matches,
                          paste the complete ID.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <OrderStatusFilter
                    value={statusFilter}
                    onChange={handleStatusChange}
                  />
                </DataTableToolbar>
              </div>

              {isLoading || isSearching ? (
                <DataTableSkeleton />
              ) : error ? (
                <div className="text-red-500 p-4">
                  Error loading orders: {error.message}
                </div>
              ) : (
                <DataTable
                  data={tableData}
                  columns={columns}
                  table={table}
                  className="border-none"
                  renderToolbar={() => null} // Toolbar is rendered above
                />
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      <Dialog open={showOrderDetails} onOpenChange={setShowOrderDetails}>
        <DialogContent className="max-w-4xl">
          <DialogHeader className="flex flex-row items-center justify-between">
            <DialogTitle>Order Details</DialogTitle>
          </DialogHeader>
          {selectedOrder && <OrderDetails order={selectedOrder} />}
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
}

interface OrderDetailsProps {
  order: AdminOrderData;
}

function OrderItem({ item }: { item: any }) {
  const options = item.options ?? [];
  const itemTotal = item.quantity * item.products.price;
  const { data: imageUrl } = useGetImage(item.products.image);

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-start gap-4">
        <div className="aspect-square h-16 w-16 rounded-lg">
          <img
            src={imageUrl || item.products.image || ""}
            alt={item.products.name}
            className="h-full w-full rounded-lg object-cover"
          />
        </div>
        <div className="flex flex-col gap-2">
          <p className="text-sm font-medium">{item.products.name}</p>
          <p className="text-sm text-muted-foreground">
            Quantity: {item.quantity}
          </p>
          {options.map((option: any, index: number) => (
            <p
              key={`${item.id}-${option.name}-${option.value}-${index}`}
              className="text-sm text-muted-foreground"
            >
              {option.name}: {option.value}
              {Number(option.price) > 0 && (
                <span className="text-green-500">
                  {` (+${formatPrice(Number(option.price))})`}
                </span>
              )}
            </p>
          ))}
        </div>
      </div>
      <div className="flex items-end">
        <p className="text-sm font-medium">{formatPrice(itemTotal)}</p>
      </div>
    </div>
  );
}

function OrderItemSkeleton() {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-start gap-4">
        <Skeleton className="aspect-square h-16 w-16 rounded-lg" />
        <div className="flex flex-col gap-2 w-full">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-3 w-20" />
          <Skeleton className="h-3 w-32" />
        </div>
      </div>
      <Skeleton className="h-4 w-16" />
    </div>
  );
}

function OrderDetailsSkeleton() {
  return (
    <div className="grid gap-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Skeleton className="h-5 w-5 rounded-full" />
          <div className="flex flex-col gap-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-3 w-32" />
          </div>
        </div>
        <div className="flex items-center gap-4">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-5 w-24 rounded-full" />
        </div>
      </div>

      <Separator />

      <div className="flex flex-col gap-4">
        <Skeleton className="h-4 w-16" />
        <div className="grid gap-4">
          <OrderItemSkeleton />
          <OrderItemSkeleton />
        </div>
      </div>

      <Separator />

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Skeleton className="h-5 w-5 rounded-full" />
          <div className="flex flex-col gap-2">
            <Skeleton className="h-4 w-32" />
            <div className="space-y-1">
              <Skeleton className="h-3 w-40" />
              <Skeleton className="h-3 w-64" />
              <Skeleton className="h-3 w-48" />
            </div>
          </div>
        </div>
      </div>

      <Separator />

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Skeleton className="h-5 w-5 rounded-full" />
          <div className="flex flex-col gap-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-3 w-48" />
          </div>
        </div>
      </div>

      <Separator />

      <div className="flex flex-col gap-4">
        <div className="flex justify-between">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-20" />
        </div>
      </div>
    </div>
  );
}

function OrderDetails({ order }: OrderDetailsProps) {
  const orderId = order.id;
  const trackingLink = order.tracking_link;
  const token = useAuthStore((state) => state.token);
  const {
    data: orderData,
    isLoading,
    error: orderError,
  } = useGetOrderQuery(orderId, token);

  if (isLoading) return <OrderDetailsSkeleton />;
  if (orderError) return <div>Error loading order: {orderError.message}</div>;

  const items = (orderData as any)?.order_items ?? [];
  const shippingAddress = {
    ...(orderData as any)?.shipping_addresses,
    company_name: orderData?.customer_data?.company_name?.trim() || "N/A",
  };
  const date = orderData?.created_at;
  const dateFormat = new Intl.DateTimeFormat("en-US", {
    dateStyle: "medium",
    timeStyle: "short",
  });

  const dateString = date
    ? dateFormat.format(new Date(date))
    : new Date(date ?? "").toString();
  const totalAmount = orderData?.total_amount ?? 0;
  console.log("orderData", orderData);

  return (
    <div className="grid gap-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <ShoppingCart className="h-5 w-5 text-muted-foreground" />
          <div className="flex flex-col gap-2">
            <p className="text-sm font-medium">Order Details</p>
            <p className="text-sm text-muted-foreground">{dateString}</p>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <p className="text-sm font-medium">
            Total: {formatPrice(totalAmount)}
          </p>
          <OrderStatusBadge status={order.currentStatus} />
        </div>
      </div>

      <Separator />

      <div className="flex flex-col gap-4">
        <h3 className="text-sm font-medium">Items</h3>
        <ScrollArea className="h-[200px]">
          <div className="grid gap-4 pr-4">
            {items.map((item: any) => (
              <OrderItem key={item.id} item={item} />
            ))}
          </div>
        </ScrollArea>
      </div>

      <Separator />

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Truck className="h-5 w-5 text-muted-foreground" />
          <div className="flex flex-col gap-2">
            <p className="text-sm font-medium">Shipping Address</p>
            <p className="text-sm text-muted-foreground">
              {shippingAddress?.contact_name}
              <br />
              {shippingAddress?.address}
              <br />
              {shippingAddress?.city}, {shippingAddress?.state}{" "}
              {shippingAddress?.zip_code}
              {shippingAddress?.company_name && (
                <>
                  <br />
                  Company Name: {shippingAddress?.company_name}
                </>
              )}
              {orderData?.billing_address_id && (
                <>
                  <br />
                  Billing Number: {orderData?.billing_address_id}
                </>
              )}
              {orderData?.purchase_order && (
                <>
                  <br />
                  P0 Number: {orderData?.purchase_order}
                </>
              )}
            </p>
            {trackingLink && (
              <div className="mt-2">
                <Link
                  href={trackingLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm inline-flex items-center gap-1.5 text-primary hover:underline"
                >
                  <ExternalLink className="h-3.5 w-3.5" />
                  Track order
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>

      <Separator />

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Package className="h-5 w-5 text-muted-foreground" />
          <div className="flex flex-col gap-2">
            <p className="text-sm font-medium">Delivery Method</p>
            <div className="text-sm text-muted-foreground">
              {order.delivery_method}
              {order.ship_collect && (
                <>
                  <br />
                  Ship Collect UPS
                  {order.ups_account_number && (
                    <>
                      <br />
                      Account Number: {order.ups_account_number}
                    </>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      <Separator />

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <FileText className="h-5 w-5 text-muted-foreground" />
          <div className="flex flex-col gap-2">
            <p className="text-sm font-medium">Tax Information</p>
            <div className="text-sm text-muted-foreground">
              {orderData?.tax_exempt ? "Tax Exempt" : "Taxable"}
              {(orderData as any)?.tax_rate &&
                (orderData as any).tax_rate > 0 && (
                  <>
                    <br />
                    Tax Rate: {(orderData as any).tax_rate.toFixed(3)}%
                    <br />
                    Tax Amount:{" "}
                    {(orderData as any).tax_amount
                      ? formatPrice((orderData as any).tax_amount)
                      : "$0.00"}
                  </>
                )}
            </div>
          </div>
        </div>
      </div>

      <Separator />

      <div className="flex flex-col gap-4">
        {(orderData as any)?.tax_rate && (orderData as any).tax_rate > 0 && (
          <>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Subtotal</span>
              <span>
                {formatPrice(
                  (totalAmount || 0) - ((orderData as any).tax_amount || 0)
                )}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">
                Tax ({(orderData as any).tax_rate.toFixed(3)}%)
                {orderData?.tax_exempt && (
                  <span className="text-orange-600 ml-1">(Exempt)</span>
                )}
              </span>
              <span
                className={
                  orderData?.tax_exempt
                    ? "line-through text-muted-foreground"
                    : ""
                }
              >
                {orderData?.tax_exempt
                  ? "$0.00"
                  : formatPrice((orderData as any).tax_amount || 0)}
              </span>
            </div>
          </>
        )}
        <div className="flex justify-between font-medium">
          <p className="text-sm">Total</p>
          <p className="text-sm font-medium">{formatPrice(totalAmount)}</p>
        </div>
      </div>
    </div>
  );
}

function OrderStatusFilter({
  value,
  onChange,
}: {
  value: string;
  onChange: (value: string) => void;
}) {
  const [open, setOpen] = useState(false);

  const handleStatusSelect = (status: OrderStatusType | "") => {
    onChange(status);
    setOpen(false);
  };

  // Get display text for the button
  const getButtonText = () => {
    if (!value) {
      return "All Statuses";
    }
    return value.charAt(0).toUpperCase() + value.slice(1);
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant={value ? "primary" : "outline"}
          size="default"
          className={`h-12 w-fit ${value ? "" : "border-dashed"}`}
        >
          <Filter className="h-4 w-4 mr-2" />
          {getButtonText()}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[200px] p-0">
        <ScrollArea className="h-60">
          <DropdownMenuItem onClick={() => handleStatusSelect("")}>
            All Statuses
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          {ORDER_STATUSES.map((status) => (
            <DropdownMenuItem
              key={status}
              className={value === status ? "bg-accent" : ""}
              onClick={() => handleStatusSelect(status)}
            >
              <OrderStatusBadge status={status} />
            </DropdownMenuItem>
          ))}
        </ScrollArea>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
