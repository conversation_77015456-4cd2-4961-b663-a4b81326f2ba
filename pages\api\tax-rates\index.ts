import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NevadaTaxRate } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";

export default matchRoute({
  GET: getTaxRatesHandler,
});

export interface GetTaxRatesResponse {
  error?: string;
  taxRates?: NevadaTaxRate[];
}

async function getTaxRatesHandler(
  req: NextApiRequest,
  res: NextApiResponse<GetTaxRatesResponse>,
) {
  const supabaseAdminClient = createSupabaseAdminClient();

  const { data, error } = await supabaseAdminClient
    .schema("public")
    .from("nevada_tax_rates")
    .select("*")
    .order("city", { ascending: true });

  if (error) {
    return res.status(500).json({ error: error.message });
  }

  return res.status(200).json({
    taxRates: data as NevadaTaxRate[],
  });
}