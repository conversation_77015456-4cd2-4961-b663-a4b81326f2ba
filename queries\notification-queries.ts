import { GetNotificationsResponse } from "@/pages/api/users/[slug]/notifications";
import { DismissNotificationResponse } from "@/pages/api/users/[slug]/notifications/[id]";
import useAuthStore from "@/stores/auth-store";
import { Notification } from "@/supabase/types";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

async function getAllNotifications(userId: string, token: string): Promise<Notification[]> {
    const response = await fetch(`/api/users/${userId}/notifications`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
        },
    });

    const data = (await response.json()) as GetNotificationsResponse;

    if (data.error) {
        throw new Error(data.error);
    }

    return data.notifications ?? [];
}

export function useGetAllNotificationsQuery(userId: string) {
    const token = useAuthStore((state) => state.token);
    const isAuthenticated = useAuthStore((state) => state.isAuthenticated)();

    return useQuery({
        enabled: isAuthenticated && !!userId,
        queryKey: ["notifications", userId],
        queryFn: async () => await getAllNotifications(userId, token),
    });
}

async function dismissNotification(userId: string, notificationId: string, token: string) {
    const response = await fetch(`/api/users/${userId}/notifications/${notificationId}`, {
        method: "PATCH",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
        },
    });

    const data = (await response.json()) as DismissNotificationResponse;

    if (data.error) {
        throw new Error(data.error);
    }

    return data.success;
}

export function useDismissNotificationMutation(userId: string) {
    const token = useAuthStore((state) => state.token);
    const queryClient = useQueryClient();

    return useMutation({
        mutationKey: ["dismiss-notification", userId],
        mutationFn: async (notificationId: string) =>
            await dismissNotification(userId, notificationId, token),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["notifications", userId] });
        },
        onError: (error) => {
            return error.message;
        },
    });
}
