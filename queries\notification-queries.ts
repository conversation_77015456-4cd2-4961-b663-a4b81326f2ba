import { GetNotificationsResponse } from "@/pages/api/notifications";
import { DismissNotificationResponse } from "@/pages/api/notifications/[id]";
import useAuthStore from "@/stores/auth-store";
import { Notification } from "@/supabase/types";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

async function getAllNotifications(token: string): Promise<Notification[]> {
    const response = await fetch(`/api/notifications`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
        },
    });

    const data = (await response.json()) as GetNotificationsResponse;

    if (data.error) {
        throw new Error(data.error);
    }

    return data.notifications ?? [];
}

export function useGetAllNotificationsQuery() {
    const token = useAuthStore((state) => state.token);
    const isAuthenticated = useAuthStore((state) => state.isAuthenticated)();
    const isAdmin = useAuthStore((state) => state.isAdmin)();
    const isStaff = useAuthStore((state) => state.isStaff)();
    const isAdminOrStaff = isAdmin || isStaff;

    return useQuery({
        enabled: isAuthenticated && isAdminOrStaff,
        queryKey: ["notifications"],
        queryFn: async () => await getAllNotifications(token),
    });
}

async function dismissNotification(notificationId: string, token: string) {
    const response = await fetch(`/api/notifications/${notificationId}`, {
        method: "PATCH",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
        },
    });

    const data = (await response.json()) as DismissNotificationResponse;

    if (data.error) {
        throw new Error(data.error);
    }

    return data.success;
}

export function useDismissNotificationMutation() {
    const token = useAuthStore((state) => state.token);
    const queryClient = useQueryClient();

    return useMutation({
        mutationKey: ["dismiss-notification"],
        mutationFn: async (notificationId: string) =>
            await dismissNotification(notificationId, token),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["notifications"] });
        },
        onError: (error) => {
            return error.message;
        },
    });
}
