import emailTemplate from ".";

interface QuoteConfirmationProps {
  to: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  companyName?: string;
  productName: string;
  quantity: number;
  quoteText?: string;
}

export default function createQuoteConfirmationTemplate({
  to,
  name,
  firstName,
  lastName,
  companyName,
  productName,
  quantity,
  quoteText,
}: QuoteConfirmationProps) {
  const APP_NAME = process.env.APP_NAME ?? "Maxton Valve";
  const APP_EMAIL_FROM = process.env.MAIL_FROM ?? "<EMAIL>";
  const from = `${APP_NAME} <${APP_EMAIL_FROM}>`;

  // Display name properly
  const displayName =
    firstName || lastName
      ? `${firstName || ""} ${lastName || ""}`.trim()
      : name || "";

  return emailTemplate({
    to,
    subject: `Quote Request Confirmation - ${APP_NAME}`,
    from,
    bcc: "<EMAIL>",
    body: `
      <h2 style="color: #0045d8; background-color: #e6f0ff; padding: 10px; border-left: 4px solid #0045d8;">Quote Request Confirmation</h2>
      <p>Thank you for your interest in Maxton Manufacturing Company products. Your quote request has been received and is being processed.</p>
      
      <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background-color: #f5f5f5;">
        <h3 style="margin-top: 0;">Request Information</h3>
        <ul style="padding-left: 20px; margin-bottom: 0;">
          ${displayName ? `<li><strong>Name:</strong> ${displayName}</li>` : ""}
          ${
            companyName
              ? `<li><strong>Company/Business:</strong> ${companyName}</li>`
              : ""
          }
          <li><strong>Email:</strong> ${to}</li>
          <li><strong>Product:</strong> ${productName}</li>
          <li><strong>Quantity:</strong> ${quantity}</li>
        </ul>
      </div>
      
      <p>We will review your request and contact you shortly with pricing and availability information.</p>
      <p>If you have any questions or need further assistance, please don't hesitate to contact us.</p>
    `,
  });
}
